<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources xmlns:android="http://schemas.android.com/apk/res/android">
    <style name="setupTitleStyleCommon">
        <item name="android:textColor">@color/setup_text_dark</item>
        <item name="android:textSize">@dimen/setup_title_text_size</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>
    <style name="setupWelcomeDescritpionStyle" parent="setupTitleStyle">
        <item name="android:textSize">@dimen/setup_welcome_description_text_size</item>
    </style>
    <style name="setupStepIndicatorStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/setup_step_triangle_indicator_height</item>
    </style>
    <style name="setupStepBulletStyle">
        <item name="android:textColor">@color/setup_text_dark</item>
        <item name="android:textSize">@dimen/setup_step_bullet_text_size</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1.0</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center_horizontal</item>
    </style>
    <style name="setupStepBaseStyle">
        <item name="android:textColor">@color/setup_text_dark</item>
        <item name="android:background">@color/setup_step_background</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingLeft">@dimen/setup_step_horizontal_padding</item>
        <item name="android:paddingRight">@dimen/setup_step_horizontal_padding</item>
    </style>
    <style name="setupStepTitleStyle" parent="setupStepBaseStyle">
        <item name="android:textSize">@dimen/setup_step_title_text_size</item>
    </style>
    <style name="setupStepInstructionStyle" parent="setupStepBaseStyle">
        <item name="android:textSize">@dimen/setup_step_instruction_text_size</item>
    </style>
    <style name="setupStepStartIndicatorStyle">
        <!-- The triangle indicator here is placed sideways. -->
        <item name="android:layout_width">@dimen/setup_step_triangle_indicator_height</item>
        <item name="android:layout_height">@dimen/setup_step_action_height</item>
    </style>
    <style name="setupStepActionLabelStyleCommon" parent="setupStepBaseStyle">
        <item name="android:textColor">@color/setup_step_action_color</item>
        <item name="android:background">@drawable/setup_step_action_background</item>
        <item name="android:layout_height">@dimen/setup_step_action_height</item>
        <item name="android:paddingLeft">@dimen/setup_step_horizontal_padding_half</item>
        <item name="android:textSize">@dimen/setup_step_action_text_size</item>
        <item name="android:drawablePadding">@dimen/setup_step_horizontal_padding_half</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>
    <style name="setupStepStartActionLabelStyleCommon" parent="setupStepActionLabelStyleCommon">
        <item name="android:paddingLeft">@dimen/setup_step_horizontal_padding</item>
        <item name="android:paddingRight">@dimen/setup_step_horizontal_padding</item>
    </style>
</resources>
