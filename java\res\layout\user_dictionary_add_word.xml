<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/user_dict_settings_add_dialog_top"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical" >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical" >

        <com.android.internal.widget.DialogTitle
            style="?android:attr/windowTitleStyle"
            android:layout_width="match_parent"
            android:layout_height="64dip"
            android:layout_marginEnd="16dip"
            android:layout_marginStart="16dip"
            android:ellipsize="end"
            android:gravity="center_vertical|start"
            android:singleLine="true"
            android:text="@string/user_dict_settings_add_dialog_title" />

        <View
            android:layout_width="match_parent"
            android:layout_height="2dip"
            android:background="@android:color/holo_blue_light" />
    </LinearLayout>

    <EditText
        android:id="@+id/user_dictionary_add_word_text"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="fill_horizontal|center_vertical"
        android:layout_marginBottom="8dip"
        android:layout_marginStart="8dip"
        android:layout_marginTop="8dip"
        android:hint="@string/user_dict_settings_add_word_hint"
        android:imeOptions="flagNoFullscreen"
        android:inputType="textNoSuggestions"
        android:maxLength="@integer/config_user_dictionary_max_word_length" >

        <requestFocus />
    </EditText>

    <LinearLayout
        style="?android:attr/buttonBarStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:measureWithLargestChild="true"
        android:orientation="horizontal" >

        <Button
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_weight="1"
            android:maxLines="2"
            android:onClick="onClickCancel"
            android:text="@string/cancel"
            android:textSize="14sp" />

        <Button
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_weight="1"
            android:maxLines="2"
            android:onClick="onClickConfirm"
            android:text="@string/user_dict_settings_add_dialog_confirm"
            android:textSize="14sp" />
    </LinearLayout>

</LinearLayout>