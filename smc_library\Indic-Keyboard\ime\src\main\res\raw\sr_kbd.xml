<?xml version="1.0"?>
<inputmethod id="sr-kbd" name="Serbian keyboard" description="Serbian keyboard layout" author="Parag Nemade" version="1.0" contextLength="0" maxKeyLength="1">
  <pattern input="&apos;g" replacement="ѓ"/>
  <pattern input="&apos;G" replacement="Ѓ"/>
  <pattern input="&apos;k" replacement="ќ"/>
  <pattern input="&apos;K" replacement="Ќ"/>
  <pattern input="\@" replacement="&quot;"/>
  <pattern input="\^" replacement="&amp;"/>
  <pattern input="\&amp;" replacement="/"/>
  <pattern input="\*" replacement="("/>
  <pattern input="\(" replacement=")"/>
  <pattern input="\)" replacement="="/>
  <pattern input="\-" replacement="&apos;"/>
  <pattern input="\_" replacement="?"/>
  <pattern input="\=" replacement="+"/>
  <pattern input="\+" replacement="*"/>
  <pattern input="q" replacement="љ"/>
  <pattern input="Q" replacement="Љ"/>
  <pattern input="w" replacement="њ"/>
  <pattern input="W" replacement="Њ"/>
  <pattern input="e" replacement="е"/>
  <pattern input="E" replacement="Е"/>
  <pattern input="r" replacement="р"/>
  <pattern input="R" replacement="Р"/>
  <pattern input="t" replacement="т"/>
  <pattern input="T" replacement="Т"/>
  <pattern input="y" replacement="з"/>
  <pattern input="Y" replacement="З"/>
  <pattern input="u" replacement="у"/>
  <pattern input="U" replacement="У"/>
  <pattern input="i" replacement="и"/>
  <pattern input="I" replacement="И"/>
  <pattern input="o" replacement="о"/>
  <pattern input="O" replacement="О"/>
  <pattern input="p" replacement="п"/>
  <pattern input="P" replacement="П"/>
  <pattern input="\[" replacement="ш"/>
  <pattern input="\{" replacement="Ш"/>
  <pattern input="\]" replacement="ђ"/>
  <pattern input="\}" replacement="Ђ"/>
  <pattern input="a" replacement="а"/>
  <pattern input="A" replacement="А"/>
  <pattern input="s" replacement="с"/>
  <pattern input="S" replacement="С"/>
  <pattern input="d" replacement="д"/>
  <pattern input="D" replacement="Д"/>
  <pattern input="f" replacement="ф"/>
  <pattern input="F" replacement="Ф"/>
  <pattern input="g" replacement="г"/>
  <pattern input="G" replacement="Г"/>
  <pattern input="h" replacement="х"/>
  <pattern input="H" replacement="Х"/>
  <pattern input="j" replacement="ј"/>
  <pattern input="J" replacement="Ј"/>
  <pattern input="k" replacement="к"/>
  <pattern input="K" replacement="К"/>
  <pattern input="l" replacement="л"/>
  <pattern input="L" replacement="Л"/>
  <pattern input=";" replacement="ч"/>
  <pattern input=":" replacement="Ч"/>
  <pattern input="&apos;" replacement="ћ"/>
  <pattern input="&quot;" replacement="Ћ"/>
  <pattern input="\" replacement="ж"/>
  <pattern input="\|" replacement="Ж"/>
  <pattern input="z" replacement="ѕ"/>
  <pattern input="Z" replacement="Ѕ"/>
  <pattern input="x" replacement="џ"/>
  <pattern input="X" replacement="Џ"/>
  <pattern input="c" replacement="ц"/>
  <pattern input="C" replacement="Ц"/>
  <pattern input="v" replacement="в"/>
  <pattern input="V" replacement="В"/>
  <pattern input="b" replacement="б"/>
  <pattern input="B" replacement="Б"/>
  <pattern input="n" replacement="н"/>
  <pattern input="N" replacement="Н"/>
  <pattern input="m" replacement="м"/>
  <pattern input="M" replacement="М"/>
  <pattern input="\&lt;" replacement=";"/>
  <pattern input="\&gt;" replacement=":"/>
  <pattern input="/" replacement="-"/>
  <pattern input="\?" replacement="_"/>
</inputmethod>
