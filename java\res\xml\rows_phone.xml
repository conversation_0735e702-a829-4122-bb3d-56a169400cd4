<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2011, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <include
        latin:keyboardLayout="@xml/key_styles_common" />
    <include
        latin:keyboardLayout="@xml/key_styles_number" />
    <Row>
        <Key
            latin:keyStyle="num1KeyStyle" />
        <Key
            latin:keyStyle="num2KeyStyle" />
        <Key
            latin:keyStyle="num3KeyStyle" />
        <Key
            latin:keySpec="-"
            latin:moreKeys="+"
            latin:keyLabelFlags="hasPopupHint"
            latin:keyStyle="numFunctionalKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
    <Row>
        <Key
            latin:keyStyle="num4KeyStyle" />
        <Key
            latin:keyStyle="num5KeyStyle" />
        <Key
            latin:keyStyle="num6KeyStyle" />
        <Key
            latin:keySpec="."
            latin:keyStyle="numFunctionalKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
    <Row>
        <Key
            latin:keyStyle="num7KeyStyle" />
        <Key
            latin:keyStyle="num8KeyStyle" />
        <Key
            latin:keyStyle="num9KeyStyle" />
        <Key
            latin:keyStyle="deleteKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
    <Row>
        <Key
            latin:keyStyle="numPhoneToSymbolKeyStyle" />
        <!-- U+0030: "0" DIGIT ZERO -->
        <Key
            latin:keyStyle="num0KeyStyle"
            latin:keySpec="0 +|0"
            latin:moreKeys="!noPanelAutoMoreKey!,+" />
        <Key
            latin:keyStyle="numSpaceKeyStyle" />
        <Key
            latin:keyStyle="enterKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
</merge>
