#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test script for Tamil transliteration
Quick way to test individual words or phrases
"""

from tamil_transliterator import TamilTransliterator, simulate_real_time_typing

def quick_test():
    """Quick test of common Tamil words"""
    transliterator = TamilTransliterator()
    
    print("Tamil Transliteration Quick Test")
    print("=" * 40)
    
    # Test common words
    test_cases = [
        ("vanakkam", "Hello"),
        ("nandri", "Thank you"),
        ("amma", "Mother"),
        ("appa", "Father"),
        ("tamil", "Tamil"),
        ("naan", "I"),
        ("neenga", "You"),
        ("sari", "Okay"),
        ("illa", "No"),
        ("enna", "What"),
        ("ka", "Ka syllable"),
        ("ki", "Ki syllable"),
        ("ma", "Ma syllable"),
        ("mi", "Mi syllable"),
    ]
    
    for english, meaning in test_cases:
        tamil = transliterator.transliterate(english)
        print(f"{english:<12} -> {tamil:<8} ({meaning})")
    
    print("\nReal-time typing simulation:")
    simulate_real_time_typing(transliterator, "vanakkam")

def test_custom_words():
    """Test your own custom words"""
    transliterator = TamilTransliterator()
    
    print("\nCustom Word Testing")
    print("=" * 30)
    
    # Add your test words here
    custom_words = [
        "hello",
        "world", 
        "computer",
        "phone",
        "book",
        "water",
        "food",
        # Add more words you want to test
    ]
    
    for word in custom_words:
        result = transliterator.transliterate(word)
        print(f"{word:<15} -> {result}")

def test_syllables():
    """Test basic Tamil syllables"""
    transliterator = TamilTransliterator()
    
    print("\nBasic Syllable Testing")
    print("=" * 30)
    
    consonants = ['k', 'm', 'n', 't', 'p', 'r', 'l', 'v']
    vowels = ['a', 'i', 'u', 'e', 'o']
    
    for consonant in consonants:
        for vowel in vowels:
            syllable = consonant + vowel
            result = transliterator.transliterate(syllable)
            print(f"{syllable:<4} -> {result}")
        print()  # Empty line between consonant groups

def interactive_single_word():
    """Test single words interactively"""
    transliterator = TamilTransliterator()
    
    print("\nSingle Word Interactive Test")
    print("Type words to transliterate (press Enter to exit):")
    print("-" * 40)
    
    while True:
        word = input("English word: ").strip()
        if not word:
            break
        
        result = transliterator.transliterate(word)
        print(f"Tamil: {result}")
        
        # Show real-time for longer words
        if len(word) > 2:
            print("Real-time simulation:")
            simulate_real_time_typing(transliterator, word)
        print()

if __name__ == "__main__":
    # Run all tests
    quick_test()
    test_custom_words()
    test_syllables()
    
    # Interactive mode
    print("\n" + "=" * 50)
    interactive_single_word()
    
    print("Testing complete!")
