#include "smc_transliterator.h"
#include <iostream>
#include <string>

/**
 * Complete SMC Tamil Transliteration Engine - Main Application
 * Ready-to-run C++ implementation with full integration
 */

void showUsage(const char* programName) {
    std::cout << "Usage: " << programName << " [options]" << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  -x, --xml <file>     XML patterns file (optional)" << std::endl;
    std::cout << "  -d, --dict <file>    Dictionary file (optional)" << std::endl;
    std::cout << "  -t, --test           Run tests only" << std::endl;
    std::cout << "  -b, --batch          Run batch test and exit" << std::endl;
    std::cout << "  -h, --help           Show this help" << std::endl;
    std::cout << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  " << programName << std::endl;
    std::cout << "  " << programName << " -d ../smc_library/Indic-Keyboard/dictionaries-indic/ta_wordlist.combined" << std::endl;
    std::cout << "  " << programName << " -x ../smc_library/Indic-Keyboard/ime/src/main/res/raw/ta_transliteration.xml" << std::endl;
    std::cout << "  " << programName << " -t" << std::endl;
}

int main(int argc, char* argv[]) {
    std::string xmlFile;
    std::string dictFile;
    bool testOnly = false;
    bool batchOnly = false;
    
    // Parse command line arguments
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            showUsage(argv[0]);
            return 0;
        }
        else if (arg == "-t" || arg == "--test") {
            testOnly = true;
        }
        else if (arg == "-b" || arg == "--batch") {
            batchOnly = true;
        }
        else if ((arg == "-x" || arg == "--xml") && i + 1 < argc) {
            xmlFile = argv[++i];
        }
        else if ((arg == "-d" || arg == "--dict") && i + 1 < argc) {
            dictFile = argv[++i];
        }
        else {
            std::cerr << "Unknown option: " << arg << std::endl;
            showUsage(argv[0]);
            return 1;
        }
    }
    
    // Try to auto-detect files if not specified
    if (xmlFile.empty()) {
        std::vector<std::string> possibleXmlPaths = {
            "../smc_library/Indic-Keyboard/ime/src/main/res/raw/ta_transliteration.xml",
            "../../smc_library/Indic-Keyboard/ime/src/main/res/raw/ta_transliteration.xml",
            "smc_library/Indic-Keyboard/ime/src/main/res/raw/ta_transliteration.xml",
            "ime/src/main/res/raw/ta_transliteration.xml",
            "ta_transliteration.xml"
        };
        
        for (const auto& path : possibleXmlPaths) {
            if (smc::utils::fileExists(path)) {
                xmlFile = path;
                break;
            }
        }
    }
    
    if (dictFile.empty()) {
        std::vector<std::string> possibleDictPaths = {
            "../smc_library/Indic-Keyboard/dictionaries-indic/ta_wordlist.combined",
            "../../smc_library/Indic-Keyboard/dictionaries-indic/ta_wordlist.combined",
            "smc_library/Indic-Keyboard/dictionaries-indic/ta_wordlist.combined",
            "dictionaries-indic/ta_wordlist.combined",
            "ta_wordlist.combined"
        };
        
        for (const auto& path : possibleDictPaths) {
            if (smc::utils::fileExists(path)) {
                dictFile = path;
                break;
            }
        }
    }
    
    // Create and initialize the interface
    smc::SMCInterface interface;
    
    std::cout << "🚀 Initializing Complete SMC Tamil Transliteration Engine..." << std::endl;
    
    if (!xmlFile.empty()) {
        std::cout << "📋 Using XML patterns: " << xmlFile << std::endl;
    } else {
        std::cout << "📋 Using built-in patterns (XML file not found)" << std::endl;
    }
    
    if (!dictFile.empty()) {
        std::cout << "📚 Using dictionary: " << dictFile << std::endl;
    } else {
        std::cout << "📚 Dictionary not found - using pattern-only mode" << std::endl;
    }
    
    if (!interface.initialize(xmlFile, dictFile)) {
        std::cerr << "❌ Failed to initialize transliterator" << std::endl;
        return 1;
    }
    
    if (testOnly) {
        interface.runTests();
        return 0;
    }
    
    if (batchOnly) {
        // Quick batch test
        smc::SMCTransliterator transliterator;
        transliterator.initialize(xmlFile, dictFile);
        
        std::vector<std::string> testWords = {
            "vanakkam", "nandri", "amma", "appa", "tamil",
            "computer", "school", "krishna", "chennai"
        };
        
        std::cout << "\n🧪 Quick Batch Test:" << std::endl;
        std::cout << std::string(40, '-') << std::endl;
        
        for (const auto& word : testWords) {
            std::string result = transliterator.transliterateSmart(word);
            std::cout << word << " -> " << result << std::endl;
        }
        
        return 0;
    }
    
    // Run interactive interface
    interface.run();
    
    return 0;
}
