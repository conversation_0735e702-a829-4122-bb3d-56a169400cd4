<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case
            latin:languageCode="fa"
        >
            <!-- U+066C: "٬" ARABIC THOUSANDS SEPARATOR
                 U+066B: "٫" ARABIC DECIMAL SEPARATOR -->
            <Key
                latin:keySpec="&#x066C;"
                latin:keyHintLabel="\@"
                latin:moreKeys="\@" />
            <Key
                latin:keySpec="&#x066B;"
                latin:keyHintLabel="\#"
                latin:moreKeys="\#" />
        </case>
        <default>
            <Key
                latin:keySpec="\@" />
            <Key
                latin:keySpec="\#" />
        </default>
    </switch>
    <Key
        latin:keyStyle="currencyKeyStyle" />
    <Key
        latin:keySpec="!text/keyspec_symbols_percent"
        latin:moreKeys="!text/morekeys_symbols_percent" />
    <Key
        latin:keySpec="&amp;" />
    <!-- U+2013: "–" EN DASH
         U+2014: "—" EM DASH
         U+00B7: "·" MIDDLE DOT -->
    <Key
        latin:keySpec="-"
        latin:moreKeys="_,&#x2013;,&#x2014;,&#x00B7;" />
    <Key
        latin:keySpec="+"
        latin:moreKeys="!text/morekeys_plus" />
    <include
        latin:keyboardLayout="@xml/keys_parentheses" />
</merge>
