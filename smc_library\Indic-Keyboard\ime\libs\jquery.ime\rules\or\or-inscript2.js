( function ( $ ) {
	'use strict';

	var orInScript2 = {
		id: 'or-inscript2',
		name: 'InScript2',
		description: 'Enhanced InScript keyboard for Odiya language',
		date: '2013-02-09',
		author: '<PERSON>g N<PERSON>de',
		license: 'GPLv3',
		version: '1.0',
		patterns: [
			['1', '୧'],
			['2', '୨'],
			['\\#', '୍ର'],
			['3', '୩'],
			['\\$', 'ର୍'],
			['4', '୪'],
			['5', '୫'],
			['6', '୬'],
			['7', '୭'],
			['8', '୮'],
			['\\(', '('],
			['9', '୯'],
			['\\)', ')'],
			['0', '୦'],
			['\\_', 'ଃ'],
			['\\-', '-'],
			['\\+', 'ଋ'],
			['\\=', 'ୃ'],
			['Q', 'ଔ'],
			['q', 'ୌ'],
			['W', 'ଐ'],
			['w', 'ୈ'],
			['E', 'ଆ'],
			['e', 'ା'],
			['R', 'ଈ'],
			['r', 'ୀ'],
			['T', 'ଊ'],
			['t', 'ୂ'],
			['Y', 'ଭ'],
			['y', 'ବ'],
			['U', 'ଙ'],
			['u', 'ହ'],
			['I', 'ଘ'],
			['i', 'ଗ'],
			['O', 'ଧ'],
			['o', 'ଦ'],
			['P', 'ଝ'],
			['p', 'ଜ'],
			['\\{', 'ଢ'],
			['\\[', 'ଡ'],
			['\\}', 'ଞ'],
			['\\]', '଼'],
			['A', 'ଓ'],
			['a', 'ୋ'],
			['S', 'ଏ'],
			['s', 'େ'],
			['D', 'ଅ'],
			['d', '୍'],
			['F', 'ଇ'],
			['f', 'ି'],
			['G', 'ଉ'],
			['g', 'ୁ'],
			['H', 'ଫ'],
			['h', 'ପ'],
			['j', 'ର'],
			['K', 'ଖ'],
			['k', 'କ'],
			['L', 'ଥ'],
			['l', 'ତ'],
			[':', 'ଛ'],
			[';', 'ଚ'],
			['\"', 'ଠ'],
			['\'', 'ଟ'],
			['X', 'ଁ'],
			['x', 'ଂ'],
			['C', 'ଣ'],
			['c', 'ମ'],
			['v', 'ନ'],
			['b', 'ୱ'],
			['N', 'ଳ'],
			['n', 'ଲ'],
			['M', 'ଶ'],
			['m', 'ସ'],
			['\\<', 'ଷ'],
			[',', ','],
			['\\>', '।'],
			['\\.', '.'],
			['\\?', 'ଯ'],
			['/', 'ୟ'],
			['\\%', 'ଜ୍ଞ'],
			['\\^', 'ତ୍ର'],
			['\\&', 'କ୍ଷ'],
			['\\*', 'ଶ୍ର']
		],
		patterns_x: [
			['1', '‍'],
			['2', '‌'],
			['4', '₹'],
			['\\+', 'ୠ'],
			['\\=', 'ୄ'],
			['R', 'ୡ'],
			['r', 'ୣ'],
			['\\{', 'ଢ଼'],
			['\\[', 'ଡ଼'],
			['F', 'ଌ'],
			['f', 'ୢ'],
			['b', 'ଵ'],
			['\\>', 'ଽ'],
			['\\.', '॥'],
			['x', '୰']]
	};

	$.ime.register( orInScript2 );

}( jQuery ) );
