<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources>
    <!-- The array of auto correction threshold values. -->
    <string-array name="auto_correction_threshold_values" translatable="false">
        <!-- Off, When auto correction setting is Off, this value is not used. -->
        <item>floatMaxValue</item>
        <!-- Modest : Suggestion whose normalized score is greater than this value
             will be subject to auto-correction. -->
        <item>0.185</item>
        <!-- Aggressive -->
        <item>0.067</item>
        <!-- Very Aggressive : Suggestion whose normalized score is greater than this value
             will be subject to auto-correction. "floatNegativeInfinity" is a special marker
             string for Float.NEGATIVE_INFINITY -->
        <item>floatNegativeInfinity</item>
    </string-array>

    <!-- The index of the auto correction threshold values array. -->
    <string name="auto_correction_threshold_mode_index_off" translatable="false">0</string>
    <string name="auto_correction_threshold_mode_index_modest" translatable="false">1</string>
    <string name="auto_correction_threshold_mode_index_aggressive" translatable="false">2</string>
    <string name="auto_correction_threshold_mode_index_very_aggressive" translatable="false">3</string>

    <!-- The array of the auto correction threshold settings values. -->
    <string-array name="auto_correction_threshold_mode_indexes" translatable="false">
      <item>@string/auto_correction_threshold_mode_index_off</item>
      <item>@string/auto_correction_threshold_mode_index_modest</item>
      <item>@string/auto_correction_threshold_mode_index_aggressive</item>
      <item>@string/auto_correction_threshold_mode_index_very_aggressive</item>
    </string-array>
    <!-- The array of the human readable auto correction threshold settings entries. -->
    <string-array name="auto_correction_threshold_modes" translatable="false">
      <item>@string/auto_correction_threshold_mode_off</item>
      <item>@string/auto_correction_threshold_mode_modest</item>
      <item>@string/auto_correction_threshold_mode_aggressive</item>
      <item>@string/auto_correction_threshold_mode_very_aggressive</item>
    </string-array>
</resources>
