( function ( $ ) {
	'use strict';

	var pattachote = {
		id: 'th-pattachote',
		name: 'Thai Pattachote',
		description: 'Thai Pattachote Input Method',
		date: '2012-10-25',
		URL: 'http://github.com/wikimedia/jquery.ime',
		author: '<PERSON> <PERSON>, <<EMAIL>>',
		license: 'GPLv3',
		version: '1.0',
		patterns: [
			['Q', '๊'],
			['W', 'ฤ'],
			['E', 'ๆ'],
			['R', 'ญ'],
			['T', 'ษ'],
			['Y', 'ึ'],
			['U', 'ฝ'],
			['I', 'ซ'],
			['O', 'ถ'],
			['P', 'ฒ'],
			['{', 'ฯ'],
			['}', 'ฦ'],
			['\\|', 'ํ'],
			['A', '๋'],
			['S', 'ธ'],
			['D', 'ำ'],
			['F', 'ณ'],
			['G', '์'],
			['H', 'ื'],
			['J', 'ผ'],
			['K', 'ช'],
			['L', 'โ'],
			[':', 'ฆ'],
			['"', 'ฑ'],
			['Z', 'ฎ'],
			['X', 'ฏ'],
			['C', 'ฐ'],
			['V', 'ภ'],
			['B', 'ั'],
			['N', 'ศ'],
			['M', 'ฮ'],
			['<', 'ฟ'],
			['>', 'ฉ'],
			['\\?', 'ฬ'],

			['q', '็'],
			['w', 'ต'],
			['e', 'ย'],
			['r', 'อ'],
			['t', 'ร'],
			['y', '่'],
			['u', 'ด'],
			['i', 'ม'],
			['o', 'ว'],
			['p', 'แ'],
			['\\[', 'ใ'],
			['\\]', 'ฌ'],
			['\\\\', ''],
			['a', '้'],
			['s', 'ท'],
			['d', 'ง'],
			['f', 'ก'],
			['g', 'ั'],
			['h', 'ี'],
			['j', 'า'],
			['k', 'น'],
			['l', 'เ'],
			[';', 'ไ'],
			['\'', 'ข'],
			['z', 'บ'],
			['x', 'ป'],
			['c', 'ล'],
			['v', 'ห'],
			['b', 'ิ'],
			['n', 'ค'],
			['m', 'ส'],
			[',', 'ะ'],
			['\\.', 'จ'],
			['/', 'พ'],

			['`', '_'],
			['1', '='],
			['2', '๒'],
			['3', '๓'],
			['4', '๔'],
			['5', '๕'],
			['6', 'ู'],
			['7', '๗'],
			['8', '๘'],
			['9', '๙'],
			['0', '๐'],
			['\\-', '๑'],
			['=', '๖'],
			['~', '฿'],
			['!', '+'],
			['@', '"'],
			['#', '/'],
			['\\$', ','],
			['%', '?'],
			['\\^', 'ุ'],
			['&', '_'],
			['\\*', '.'],
			['\\(', '('],
			['\\)', ')'],
			['_', '-'],
			['\\+', '%']
		]
		
	};

	$.ime.register( pattachote );

}( jQuery ) );
