<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <!-- U+0994: "ঔ" BENGALI LETTER AU
         U+09CC: "ৌ" BENGALI VOWEL SIGN AU
         U+09E7: "১" BENGALI DIGIT ONE -->
    <Key
        latin:keySpec="&#x0994;"
        latin:keyHintLabel="&#x09E7;"
        latin:additionalMoreKeys="&#x09CC;,&#x09E7;,1" />
    <!-- U+0990: "ঐ" BENGALI LETTER AI
         U+09C8: "ৈ" BENGALI VOWEL SIGN AI
         U+09E8: "২" BENGALI DIGIT TWO -->
    <Key
        latin:keySpec="&#x0990;"
        latin:keyHintLabel="&#x09E8;"
        latin:additionalMoreKeys="&#x09C8;,&#x09E8;,2" />
    <!-- U+0986: "আ" BENGALI LETTER AA
         U+09BE: "া" BENGALI VOWEL SIGN AA
         U+09E9: "৩" BENGALI DIGIT THREE -->
    <Key
        latin:keySpec="&#x0986;"
        latin:keyHintLabel="&#x09E9;"
        latin:additionalMoreKeys="&#x09BE;,&#x09E9;,3" />
    <!-- U+0988: "ঈ" BENGALI LETTER II
         U+09C0: "ী" BENGALI VOWEL SIGN II
         U+09EA: "৪" BENGALI DIGIT FOUR -->
    <Key
        latin:keySpec="&#x0988;"
        latin:keyHintLabel="&#x09EA;"
        latin:additionalMoreKeys="&#x09C0;,&#x09EA;,4" />
    <!-- U+098A: "ঊ" BENGALI LETTER UU
         U+09C2: "ূ" BENGALI VOWEL SIGN UU
         U+09EB: "৫" BENGALI DIGIT FIVE -->
    <Key
        latin:keySpec="&#x098A;"
        latin:keyHintLabel="&#x09EB;"
        latin:additionalMoreKeys="&#x09C2;,&#x09EB;,5" />
    <!-- U+09AC: "ব" BENGALI LETTER BA
         U+09AD: "ভ" BENGALI LETTER BHA
         U+09EC: "৬" BENGALI DIGIT SIX -->
    <Key
        latin:keySpec="&#x09AC;"
        latin:moreKeys="&#x09AD;,%"
        latin:keyHintLabel="&#x09EC;"
        latin:additionalMoreKeys="&#x09EC;,6" />
    <!-- U+09B9: "হ" BENGALI LETTER HA
         U+09ED: "৭" BENGALI DIGIT SEVEN -->
    <Key
        latin:keySpec="&#x09B9;"
        latin:keyHintLabel="&#x09ED;"
        latin:additionalMoreKeys="&#x09ED;,7" />
    <!-- U+0997: "গ" BENGALI LETTER GA
         U+0998: "ঘ" BENGALI LETTER GHA
         U+09EE: "৮" BENGALI DIGIT EIGHT -->
    <Key
        latin:keySpec="&#x0997;"
        latin:moreKeys="&#x0998;,%"
        latin:keyHintLabel="&#x09EE;"
        latin:additionalMoreKeys="&#x09EE;,8" />
    <!-- U+09A6: "দ" BENGALI LETTER DA
         U+09A7: "ধ" BENGALI LETTER DHA
         U+09EF: "৯" BENGALI DIGIT NINE -->
    <Key
        latin:keySpec="&#x09A6;"
        latin:moreKeys="&#x09A7;,%"
        latin:keyHintLabel="&#x09EF;"
        latin:additionalMoreKeys="&#x09EF;,9" />
    <!-- U+099C: "জ" BENGALI LETTER JA
         U+099D: "ঝ" BENGALI LETTER JHA
         U+099C/U+09CD/U+099E: "জ্ঞ" BENGALI LETTER JA/BENGALI SIGN VIRAMA/BENGALI LETTER NYA
         U+09E6: "০" BENGALI DIGIT ZERO -->
    <Key
        latin:keySpec="&#x099C;"
        latin:moreKeys="&#x099D;,&#x099C;&#x09CD;&#x099E;,%"
        latin:keyHintLabel="&#x09E6;"
        latin:additionalMoreKeys="&#x09E6;,0" />
    <!-- U+09A1: "ড" BENGALI LETTER DDA
         U+09A1/U+09BC: "ড়" BENGALI LETTER DDA/BENGALI SIGN NUKTA -->
    <Key
        latin:keySpec="&#x09A1;"
        latin:moreKeys="&#x09A1;&#x09BC;" />
</merge>
