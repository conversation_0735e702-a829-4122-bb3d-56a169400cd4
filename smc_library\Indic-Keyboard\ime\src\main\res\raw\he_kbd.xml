<?xml version="1.0"?>
<inputmethod id="he-kbd" name="Hebrew kbd" description="Hebrew kbd keyboard layout" author="Parag Nemade" version="1.0" contextLength="0" maxKeyLength="1">
  <pattern input="\`" replacement=";"/>
  <pattern input="q" replacement="/"/>
  <pattern input="w" replacement="&apos;"/>
  <pattern input="e" replacement="ק"/>
  <pattern input="r" replacement="ר"/>
  <pattern input="t" replacement="א"/>
  <pattern input="y" replacement="ט"/>
  <pattern input="u" replacement="ו"/>
  <pattern input="i" replacement="ן"/>
  <pattern input="o" replacement="ם"/>
  <pattern input="p" replacement="פ"/>
  <pattern input="a" replacement="ש"/>
  <pattern input="s" replacement="ד"/>
  <pattern input="d" replacement="ג"/>
  <pattern input="f" replacement="כ"/>
  <pattern input="g" replacement="ע"/>
  <pattern input="h" replacement="י"/>
  <pattern input="j" replacement="ח"/>
  <pattern input="k" replacement="ל"/>
  <pattern input="l" replacement="ך"/>
  <pattern input=";" replacement="ף"/>
  <pattern input="z" replacement="ז"/>
  <pattern input="x" replacement="ס"/>
  <pattern input="c" replacement="ב"/>
  <pattern input="v" replacement="ה"/>
  <pattern input="b" replacement="נ"/>
  <pattern input="n" replacement="מ"/>
  <pattern input="m" replacement="צ"/>
  <pattern input="," replacement="ת"/>
  <pattern input="." replacement="ץ"/>
  <pattern input="/" replacement="."/>
  <pattern input="&apos;" replacement=","/>
  <pattern input="\(" replacement=")"/>
  <pattern input="\)" replacement="("/>
  <pattern input="\&lt;" replacement="&gt;"/>
  <pattern input="\&gt;" replacement="&lt;"/>
  <pattern input="\[" replacement="]"/>
  <pattern input="\]" replacement="["/>
  <pattern input="\{" replacement="}"/>
  <pattern input="\}" replacement="{"/>
</inputmethod>
