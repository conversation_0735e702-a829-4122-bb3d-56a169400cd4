<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright ©
**      2012-2013 <PERSON><PERSON><PERSON>
**      2013-2014 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC), International Centre for Free and Open Source Software (ICFOSS)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case
            latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted"
        >
            <!-- ശ, ശ്ശ -->
            <Key
                latin:keySpec="&#x0D36;"
                latin:moreKeys="&#x0D36;&#x0D4D;&#x0D36;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ന്‍, ണ്‍, zwsp -->
            <Key
                latin:keySpec="&#x0D28;&#x0D4D;&#x200D;"
                latin:moreKeys="&#x0D28;&#x0D4D;&#x200D;,&#x200B;"
                latin:keyHintLabel="&#x0D28;&#x0D4D;&#x200D;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <!-- ‍ഛ -->
            <Key
                latin:keySpec="&#x0D1B;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ള്‍, ല്‍, ര്‍, zwnj‍‍‍ -->
            <Key
                latin:keySpec="&#x0D33;&#x0D4D;&#x200D;"
                latin:moreKeys="&#x0d32;&#x0D4D;&#x200D;,&#x200D;"
                latin:keyHintLabel="&#x0d32;&#x0D4D;&#x200D;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <!-- ‍ഭ -->
            <Key
                latin:keySpec="&#x0D2D;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ണ, ഞ, ഞ്ഞ, ണ്ണ, ണ്‍ -->
            <Key
                latin:keySpec="&#x0D23;"
                latin:moreKeys="&#x0D1E;,&#x0D1E;&#x0D4D;&#x0D1E;,&#x0D23;&#x0D4D;&#x0D23;,&#x0d23;&#x0D4D;&#x200D;"
                latin:keyHintLabel="&#x0D1E;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ം -->
            <Key
                latin:keySpec="&#x0D02;"
                latin:keyLabelFlags="fontNormal" />
        </case>

        <default>
            <!-- ഴ  -->
            <Key
                latin:keySpec="&#x0D34;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ് -->
            <Key
                latin:keySpec="&#x0D4D;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <!-- ച, ച്ച -->
            <Key
                latin:keySpec="&#x0D1A;"
                latin:moreKeys="&#x0D1A;&#x0D4D;&#x0D1A;"
                latin:keyLabelFlags="fontNormal" />
            <!-- വ, വ്വ -->
            <Key
                latin:keySpec="&#x0D35;"
                latin:moreKeys="&#x0D35;&#x0D4D;&#x0D35;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ബ, ബ്ബ -->
            <Key
                latin:keySpec="&#x0D2C;"
                latin:moreKeys="&#x0D2C;&#x0D4D;&#x0D2C;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ന, ങ, ങ്ങ, ന്ന, ന്‍-->
            <Key
                latin:keySpec="&#x0D28;" 
                latin:moreKeys="&#x0D19;,&#x0D19;&#x0D4D;&#x0D19;,&#x0D28;&#x0D4D;&#x0D28;,&#x0D28;&#x0D4D;&#x200D;"
                latin:keyHintLabel="&#x0D19;"
                latin:keyLabelFlags="fontNormal" />
            <!-- മ, മ്മ -->
            <Key
                latin:keySpec="&#x0D2E;"
                latin:moreKeys="&#x0D2E;&#x0D4D;&#x0D2E;"
                latin:keyLabelFlags="fontNormal" />
         </default>
    </switch>
</merge>
