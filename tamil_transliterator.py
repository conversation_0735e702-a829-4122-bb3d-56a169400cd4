#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tamil Transliteration Engine
Based on the Indic Keyboard project logic
Converts English phonetic input to Tamil script
"""

import re
from typing import Dict, Optional, Tuple

class TamilTransliterator:
    """
    Tamil transliteration engine that converts English input to Tamil script
    Based on the logic from Indic Keyboard's WordComposer.applyTransliteration()
    """
    
    def __init__(self):
        self.max_key_length = 8
        self._initialize_mappings()
    
    def _initialize_mappings(self):
        """Initialize all the mapping dictionaries"""
        
        # Tamil vowels (independent)
        self.vowel_map = {
            'a': 'அ',    # U+0B85
            'aa': 'ஆ',   # U+0B86
            'i': 'இ',    # U+0B87
            'ii': 'ஈ',   # U+0B88
            'u': 'உ',    # U+0B89
            'uu': 'ஊ',   # U+0B8A
            'e': 'எ',    # U+0B8E
            'ee': 'ஏ',   # U+0B8F
            'ai': 'ஐ',   # U+0B90
            'o': 'ஒ',    # U+0B92
            'oo': 'ஓ',   # U+0B93
            'au': 'ஔ',   # U+0B94
        }
        
        # Tamil consonants (with inherent 'a')
        self.consonant_map = {
            'k': 'க',     # U+0B95
            'ng': 'ங',    # U+0B99
            'ch': 'ச',    # U+0B9A
            'nj': 'ஞ',    # U+0B9E
            't': 'ட',     # U+0B9F
            'n': 'ந',     # U+0BA8
            'th': 'த',    # U+0BA4
            'p': 'ப',     # U+0BAA
            'm': 'ம',     # U+0BAE
            'y': 'ய',     # U+0BAF
            'r': 'ர',     # U+0BB0
            'l': 'ல',     # U+0BB2
            'v': 'வ',     # U+0BB5
            'zh': 'ழ',    # U+0BB4
            'll': 'ள',    # U+0BB3
            'rr': 'ற',    # U+0BB1
            'nn': 'ண',    # U+0BA3
            's': 'ஸ',     # U+0BB8
            'sh': 'ஷ',    # U+0BB7
            'h': 'ஹ',     # U+0BB9
            'j': 'ஜ',     # U+0B9C
            'g': 'க',     # Map g to k
            'd': 'ட',     # Map d to t
            'b': 'ப',     # Map b to p
        }
        
        # Tamil vowel signs (matras) to combine with consonants
        self.vowel_sign_map = {
            'aa': 'ா',    # U+0BBE
            'i': 'ி',     # U+0BBF
            'ii': 'ீ',    # U+0BC0
            'u': 'ு',     # U+0BC1
            'uu': 'ூ',    # U+0BC2
            'e': 'ெ',     # U+0BC6
            'ee': 'ே',    # U+0BC7
            'ai': 'ை',    # U+0BC8
            'o': 'ொ',     # U+0BCA
            'oo': 'ோ',    # U+0BCB
            'au': 'ௌ',    # U+0BCC
        }
        
        # Tamil virama (pulli) to remove inherent vowel
        self.virama = '்'  # U+0BCD
        
        # Common Tamil words and phrases
        self.complex_map = {
            'vanakkam': 'வணக்கம்',
            'nandri': 'நன்றி',
            'tamil': 'தமிழ்',
            'amma': 'அம்மா',
            'appa': 'அப்பா',
            'anna': 'அண்ணா',
            'akka': 'அக்கா',
            'thambi': 'தம்பி',
            'thangai': 'தங்கை',
            'naan': 'நான்',
            'neenga': 'நீங்க',
            'neengal': 'நீங்கள்',
            'enna': 'என்ன',
            'eppo': 'எப்போ',
            'enga': 'எங்க',
            'eppadi': 'எப்படி',
            'yen': 'ஏன்',
            'sari': 'சரி',
            'illa': 'இல்ல',
            'illai': 'இல்லை',
            'aam': 'ஆம்',
            'aama': 'ஆமா',
            'theriyala': 'தெரியல',
            'theriyathu': 'தெரியாது',
            'varen': 'வரேன்',
            'vanthen': 'வந்தேன்',
            'poren': 'போறேன்',
            'poithen': 'போய்தேன்',
            'irukku': 'இருக்கு',
            'irukkum': 'இருக்கும்',
            'sapadu': 'சாப்பாடு',
            'saapadu': 'சாப்பாடு',
            'thanni': 'தண்ணி',
            'thanneer': 'தண்ணீர்',
            'mozhi': 'மொழி',
            'bhasha': 'பாஷை',
            'veedu': 'வீடு',
            'school': 'ஸ்கூல்',
            'college': 'காலேஜ்',
            'office': 'ஆபீஸ்',
            'hospital': 'ஹாஸ்பிடல்',
            'mannikkavum': 'மன்னிக்கவும்',
            'poitu': 'போய்ட்டு',
            'vantu': 'வந்து',
            'irunthu': 'இருந்து',
            'sollungal': 'சொல்லுங்கள்',
            'kelunga': 'கேளுங்க',
            'parunga': 'பாருங்க',
            'vaanga': 'வாங்க',
        }
    
    def transliterate(self, input_text: str, context: str = "", is_complete: bool = False) -> str:
        """
        Main transliteration method - converts English input to Tamil
        
        Args:
            input_text: English text to transliterate
            context: Previous context (not used in this simplified version)
            is_complete: Whether the input is complete
            
        Returns:
            Tamil transliterated text
        """
        if not input_text:
            return input_text
        
        lower_input = input_text.lower().strip()
        
        # Check for exact complex word matches first
        if lower_input in self.complex_map:
            return self.complex_map[lower_input]
        
        # Try progressive transliteration
        return self._progressive_transliterate(lower_input)
    
    def _progressive_transliterate(self, input_text: str) -> str:
        """
        Progressive transliteration that builds Tamil text character by character
        Similar to the original WordComposer.applyTransliteration logic
        """
        result = []
        i = 0
        
        while i < len(input_text):
            match, consumed_length = self._find_longest_match(input_text, i)
            if match:
                result.append(match)
                i += consumed_length
            else:
                # If no match found, append the character as-is
                result.append(input_text[i])
                i += 1
        
        return ''.join(result)
    
    def _find_longest_match(self, input_text: str, start_pos: int) -> Tuple[Optional[str], int]:
        """
        Find the longest possible Tamil match starting from position start_pos
        Returns (tamil_match, consumed_length)
        """
        max_len = min(self.max_key_length, len(input_text) - start_pos)
        
        # Try different lengths, starting from longest possible
        for length in range(max_len, 0, -1):
            substring = input_text[start_pos:start_pos + length]
            
            # Check complex mappings first
            if substring in self.complex_map:
                return self.complex_map[substring], length
            
            # Try consonant + vowel combinations
            consonant_vowel_match = self._try_consonant_vowel_combination(substring)
            if consonant_vowel_match:
                return consonant_vowel_match, length
            
            # Try standalone vowels
            if substring in self.vowel_map:
                return self.vowel_map[substring], length
            
            # Try standalone consonants
            if substring in self.consonant_map:
                return self.consonant_map[substring], length
        
        return None, 0
    
    def _try_consonant_vowel_combination(self, input_text: str) -> Optional[str]:
        """
        Try to match consonant + vowel combinations
        """
        # Try different splits of consonant + vowel
        for split in range(1, len(input_text)):
            consonant_part = input_text[:split]
            vowel_part = input_text[split:]
            
            if consonant_part in self.consonant_map and vowel_part in self.vowel_sign_map:
                consonant = self.consonant_map[consonant_part]
                vowel_sign = self.vowel_sign_map[vowel_part]
                
                # Combine consonant with vowel sign
                # Remove inherent 'a' by adding virama, then add vowel sign
                return consonant + vowel_sign
        
        return None
    
    def get_max_key_length(self) -> int:
        """Get maximum key length for transliteration"""
        return self.max_key_length
    
    @staticmethod
    def first_divergence(str1: str, str2: str) -> int:
        """
        Find the first position where two strings diverge
        Similar to the firstDivergence method in WordComposer
        """
        min_length = min(len(str1), len(str2))
        for i in range(min_length):
            if str1[i] != str2[i]:
                return i
        return min_length - 1 if min_length > 0 else 0


def simulate_real_time_typing(transliterator: TamilTransliterator, word: str):
    """
    Simulate real-time typing like in the Indic Keyboard
    Shows how the transliteration changes as each character is typed
    """
    print(f"\nSimulating real-time typing for: '{word}'")
    print("=" * 50)
    
    current_word = ""
    for char in word:
        current_word += char
        tamil_output = transliterator.transliterate(current_word)
        print(f"Typed: {current_word:<15} -> Tamil: {tamil_output}")


def batch_test(transliterator: TamilTransliterator):
    """Test common Tamil words"""
    print("\nBatch Test - Common Tamil Words")
    print("=" * 40)
    
    test_words = [
        # Greetings
        'vanakkam', 'nandri', 'mannikkavum',
        
        # Family
        'amma', 'appa', 'anna', 'akka', 'thambi', 'thangai',
        
        # Common phrases
        'naan', 'neenga', 'enna', 'eppo', 'enga', 'eppadi', 'yen',
        
        # Responses
        'sari', 'illa', 'aam', 'theriyala',
        
        # Actions
        'varen', 'poren', 'irukku', 'sapadu', 'thanni',
        
        # Language
        'tamil', 'mozhi',
        
        # Simple syllables
        'ka', 'ki', 'ku', 'ke', 'ko',
        'ma', 'mi', 'mu', 'me', 'mo',
        'na', 'ni', 'nu', 'ne', 'no',
        'ta', 'ti', 'tu', 'te', 'to',
        'pa', 'pi', 'pu', 'pe', 'po',
        'ra', 'ri', 'ru', 're', 'ro',
        'la', 'li', 'lu', 'le', 'lo',
        'va', 'vi', 'vu', 've', 'vo'
    ]
    
    for word in test_words:
        result = transliterator.transliterate(word)
        print(f"{word:<15} -> {result}")


def interactive_mode(transliterator: TamilTransliterator):
    """Interactive testing mode"""
    print("\n" + "=" * 50)
    print("Interactive Tamil Transliteration")
    print("=" * 50)
    print("Commands:")
    print("  Type any English word to transliterate")
    print("  'realtime <word>' - See character-by-character conversion")
    print("  'batch' - Run batch test")
    print("  'help' - Show this help")
    print("  'exit' or 'quit' - Exit program")
    print()
    
    while True:
        try:
            user_input = input("Tamil> ").strip()
            
            if user_input.lower() in ['exit', 'quit', 'q']:
                break
            elif user_input.lower() == 'help':
                print("Available commands: realtime <word>, batch, help, exit")
                print("Or just type any English word to transliterate it.")
            elif user_input.lower() == 'batch':
                batch_test(transliterator)
            elif user_input.lower().startswith('realtime '):
                word = user_input[9:].strip()
                if word:
                    simulate_real_time_typing(transliterator, word)
            elif user_input:
                result = transliterator.transliterate(user_input)
                print(f"Tamil: {result}")
                
                # Also show real-time simulation for longer words
                if len(user_input) > 3:
                    simulate_real_time_typing(transliterator, user_input)
        
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except EOFError:
            print("\nExiting...")
            break


def main():
    """Main function to run the Tamil transliteration test"""
    print("Tamil Transliteration Engine")
    print("Based on Indic Keyboard Logic")
    print("=" * 40)
    
    # Create transliterator instance
    transliterator = TamilTransliterator()
    
    # Quick demo
    print("\nQuick Demo:")
    simulate_real_time_typing(transliterator, "vanakkam")
    
    # Start interactive mode
    interactive_mode(transliterator)
    
    print("\nThank you for testing Tamil transliteration!")


if __name__ == "__main__":
    main()
