<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <!-- Go key -->
    <switch>
        <case latin:isIconDefined="go_key">
            <key-style
                latin:styleName="goActionKeyStyle"
                latin:keySpec="!icon/go_key|!code/key_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </case>
        <default>
            <key-style
                latin:styleName="goActionKeyStyle"
                latin:keySpec="!text/label_go_key|!code/key_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </default>
    </switch>
    <!-- Next key -->
    <switch>
        <case latin:isIconDefined="next_key">
            <key-style
                latin:styleName="nextActionKeyStyle"
                latin:keySpec="!icon/next_key|!code/key_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </case>
        <default>
            <key-style
                latin:styleName="nextActionKeyStyle"
                latin:keySpec="!text/label_next_key|!code/key_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </default>
    </switch>
    <!-- Previous key -->
    <switch>
        <case latin:isIconDefined="previous_key">
            <key-style
                latin:styleName="previousActionKeyStyle"
                latin:keySpec="!icon/previous_key|!code/key_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </case>
        <default>
            <key-style
                latin:styleName="previousActionKeyStyle"
                latin:keySpec="!text/label_previous_key|!code/key_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </default>
    </switch>
    <!-- Done key -->
    <switch>
        <case latin:isIconDefined="done_key">
            <key-style
                latin:styleName="doneActionKeyStyle"
                latin:keySpec="!icon/done_key|!code/key_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </case>
        <default>
            <key-style
                latin:styleName="doneActionKeyStyle"
                latin:keySpec="!text/label_done_key|!code/key_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </default>
    </switch>
    <!-- Send key -->
    <switch>
        <case latin:isIconDefined="send_key">
            <key-style
                latin:styleName="sendActionKeyStyle"
                latin:keySpec="!icon/send_key|!code/key_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </case>
        <default>
            <key-style
                latin:styleName="sendActionKeyStyle"
                latin:keySpec="!text/label_send_key|!code/key_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </default>
    </switch>
    <!-- Seartch key -->
    <switch>
        <case latin:isIconDefined="search_key">
            <key-style
                latin:styleName="searchActionKeyStyle"
                latin:keySpec="!icon/search_key|!code/key_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </case>
        <default>
            <key-style
                latin:styleName="searchActionKeyStyle"
                latin:keySpec="!text/label_search_key|!code/key_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </default>
    </switch>
    <switch>
        <case latin:keyboardTheme="ICS|KLP">
            <key-style
                latin:styleName="customLabelActionKeyStyle"
                latin:keySpec="dummy_label|!code/key_enter"
                latin:keyLabelFlags="fromCustomActionLabel"
                latin:backgroundType="action"
                latin:parentStyle="defaultEnterKeyStyle" />
        </case>
        <!-- keyboardTheme="LXXLight|LXXDark" -->
        <default>
            <key-style
                latin:styleName="customLabelActionKeyStyle"
                latin:keySpec="dummy_label|!code/key_enter"
                latin:keyLabelFlags="fromCustomActionLabel"
                latin:backgroundType="functional"
                latin:parentStyle="defaultEnterKeyStyle" />
        </default>
    </switch>
</merge>
