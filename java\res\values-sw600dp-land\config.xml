<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2011, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- Configuration values for Small Tablet Landscape. -->
<resources>
    <!-- Preferable keyboard height in absolute scale: 45.0mm -->
    <!-- This config_default_keyboard_height value should match with keyboard-heights.xml -->
    <dimen name="config_default_keyboard_height">283.5dp</dimen>
    <fraction name="config_min_keyboard_height">40%p</fraction>

    <dimen name="config_more_keys_keyboard_key_height">81.9dp</dimen>

    <fraction name="config_keyboard_top_padding_holo">2.727%p</fraction>
    <fraction name="config_keyboard_bottom_padding_holo">0.0%p</fraction>
    <fraction name="config_key_vertical_gap_holo">4.5%p</fraction>
    <fraction name="config_key_horizontal_gap_holo">0.9%p</fraction>

    <fraction name="config_key_letter_ratio_holo">50%</fraction>
    <fraction name="config_key_letter_ratio_lxx">50%</fraction>
    <fraction name="config_key_large_letter_ratio_holo">48%</fraction>
    <fraction name="config_key_large_letter_ratio_lxx">60%</fraction>
    <fraction name="config_key_label_ratio_holo">32%</fraction>
    <fraction name="config_key_label_ratio_lxx">32%</fraction>
    <fraction name="config_key_hint_letter_ratio_holo">23%</fraction>
    <fraction name="config_key_hint_letter_ratio_lxx">23%</fraction>
    <fraction name="config_key_hint_label_ratio_holo">34%</fraction>
    <fraction name="config_key_hint_label_ratio_lxx">20%</fraction>
    <fraction name="config_key_shifted_letter_hint_ratio_holo">29%</fraction>
    <fraction name="config_key_shifted_letter_hint_ratio_lxx">29%</fraction>
    <fraction name="config_language_on_spacebar_text_ratio">30.0%</fraction>
    <dimen name="config_key_shifted_letter_hint_padding">4dp</dimen>

    <!-- For 5-row keyboard -->
    <fraction name="config_key_vertical_gap_5row">3.20%p</fraction>
    <fraction name="config_key_letter_ratio_5row">62%</fraction>
    <fraction name="config_key_shifted_letter_hint_ratio_5row">36%</fraction>

    <dimen name="config_suggestions_strip_height">44dp</dimen>
    <dimen name="config_suggestions_strip_horizontal_margin">180.0dp</dimen>
    <dimen name="config_suggestions_strip_edge_key_width">54dp</dimen>
    <integer name="config_max_more_suggestions_row">5</integer>
    <fraction name="config_min_more_suggestions_width">50%</fraction>

    <!-- Gesture floating preview text parameters -->
    <dimen name="config_gesture_floating_preview_text_size">26dp</dimen>
    <dimen name="config_gesture_floating_preview_text_offset">76dp</dimen>
    <dimen name="config_gesture_floating_preview_horizontal_padding">26dp</dimen>
    <dimen name="config_gesture_floating_preview_vertical_padding">17dp</dimen>

    <!-- Emoji keyboard -->
    <fraction name="config_emoji_keyboard_key_width">10%p</fraction>
    <fraction name="config_emoji_keyboard_row_height">33%p</fraction>
    <fraction name="config_emoji_keyboard_key_letter_size">70%p</fraction>
    <integer name="config_emoji_keyboard_max_page_key_count">30</integer>
</resources>
