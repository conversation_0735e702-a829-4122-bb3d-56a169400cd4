<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- Note: This emoji code point list is valid prior to JB-MR2 (API < 18).
     There is another emoji code point list for JB-MR2 and KLP and later under
     res/xml/values-v1[89].-->
<resources>
    <!-- Dummy codeArrays for recents emoji keyboard.
         Do not remove these keys, because they are used as a template. -->
    <array
        name="emoji_recents"
        format="string"
    >
        <!-- These code point should be aligned with {@link RecentsKeyboard#TEMPLATE_KEY_CODE_*. -->
        <item>30</item>
        <item>31</item>
    </array>
    <array
        name="emoji_nature"
        format="string"
    >
        <item>2744</item> <!-- SNOWFLAKE -->
    </array>
    <array
        name="emoji_symbols"
        format="string"
    >
        <item>2460</item> <!-- CIRCLED DIGIT ONE -->
        <item>2461</item> <!-- CIRCLED DIGIT TWO -->
        <item>2462</item> <!-- CIRCLED DIGIT THREE -->
        <item>2463</item> <!-- CIRCLED DIGIT FOUR -->
        <item>2464</item> <!-- CIRCLED DIGIT FIVE -->
        <item>2465</item> <!-- CIRCLED DIGIT SIX -->
        <item>2466</item> <!-- CIRCLED DIGIT SEVEN -->
        <item>2467</item> <!-- CIRCLED DIGIT EIGHT -->
        <item>2468</item> <!-- CIRCLED DIGIT NINE -->
        <item>2469</item> <!-- CIRCLED DIGIT TEN -->
        <item>00ae</item> <!-- REGISTERED SIGN -->
        <item>00a9</item> <!-- COPYRIGHT SIGN -->
        <item>2122</item> <!-- TRADE MARK SIGN -->
        <item>2734</item> <!-- EIGHT POINTED BLACK STAR -->
        <item>2733</item> <!-- EIGHT POINTED PINWHEEL STAR -->
        <item>2716</item> <!-- HEAVY MULTIPLICATION MARK -->
        <item>2195</item> <!-- UP DOWN ARROW -->
        <item>2197</item> <!-- NORTH EAST ARROW -->
        <item>27a1</item> <!-- BLACK RIGHTWARDS ARROW -->
        <item>2198</item> <!-- SOUTH EAST ARROW -->
        <item>2199</item> <!-- SOUTH WEST ARROW -->
        <item>2196</item> <!-- NORTH EAST ARROW -->
        <item>2194</item> <!-- LEFT RIGHT ARROW -->
        <item>25c0</item> <!-- BLACK LEFT-POINTING TRIANGLE -->
        <item>25b6</item> <!-- BLACK ROGHT-POINTING TRIANGLE -->
        <item>2747</item> <!-- SPARKLE -->
        <item>25aa</item> <!-- BLACK SMALL SQUARE -->
        <item>203c</item> <!-- DOUBLE EXCLAMATION MARK -->
        <item>2660</item> <!-- BLACK SPADE SUIT -->
        <item>2665</item> <!-- BLACK HEART SUIT -->
        <item>2663</item> <!-- BLACK CLUB SUIT -->
        <item>2666</item> <!-- BLACK DIAMOND SUIT -->
        <item>21a9</item> <!-- LEFTWARDS ARROW WITH HOOK -->
        <item>21aa</item> <!-- RIGHTWARDS ARROW WITH HOOK -->
    </array>
    <array
        name="emoji_faces"
        format="string"
    >
        <item>270C</item> <!-- VICTORY HAND -->
        <item>2764</item> <!-- HEAVY BLACK HEART -->
    </array>
    <array
        name="emoji_objects"
        format="string"
    >
        <item>260e</item> <!-- BLACK TELEPHONE -->
        <item>2709</item> <!-- ENVELOPE -->
        <item>2712</item> <!-- BLACK NIB -->
        <item>270f</item> <!-- PENCIL -->
        <item>2702</item> <!-- BLACK SCISSORS -->
        <item>2669</item> <!-- QUARTER NOTE -->
        <item>266a</item> <!-- EIGHTH NOTE -->
        <item>266c</item> <!-- BEAMED SIXTEENTH NOTES -->
    </array>
    <array
        name="emoji_places"
        format="string"
    >
        <item>2708</item> <!-- AIRPLANE -->
        <item>2668</item> <!-- HOT SPRINGS -->
    </array>
    <array
        name="emoji_emoticons"
        format="string"
    >
        <item>=-O</item>
        <item>:-P</item>
        <item>;-)</item>
        <item>:-(</item>
        <item>:-)</item>
        <item>:-!</item>
        <item>:-$</item>
        <item>B-)</item>
        <item>:O</item>
        <item>:-*</item>
        <item>:-D</item>
        <item>:\'(</item>
        <item>:-\\</item>
        <item>O:-)</item>
        <item>:-[</item>
        <item>^^</item>
        <item>-_-</item>
    </array>
</resources>
