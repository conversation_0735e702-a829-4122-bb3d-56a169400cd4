<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:divider="?android:attr/dividerHorizontal"
    android:showDividers="middle"
    android:dividerPadding="0dip" >

    <!-- The list of packages that correspond to the requesting UID
    and the account/authtokenType that is being requested -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:layout_weight="1"
        android:gravity="top|center_horizontal">

      <TextView
         android:id="@+id/download_over_metered_prompt"
         android:paddingTop="14dip"
         android:layout_width="wrap_content"
         android:layout_height="wrap_content"
         android:textAppearance="?android:attr/textAppearanceMedium"
         android:paddingLeft="16dip"
         android:paddingRight="16dip"
         android:paddingBottom="12dip" />

    </ScrollView>

    <!-- The buttons to trigger download or wait -->
    <LinearLayout
        android:id="@+id/buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="?android:attr/buttonBarStyle">

        <Button
            android:id="@+id/deny_button"
            android:text="@string/do_not_download_over_metered"
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:onClick="onClickDeny"
            style="?android:attr/buttonBarButtonStyle" />

        <!-- The text of this button contains the size of the dictionary so it will be filled programmatically -->
        <Button
            android:id="@+id/allow_button"
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:onClick="onClickAllow"
            style="?android:attr/buttonBarButtonStyle" />

    </LinearLayout>
</LinearLayout>

