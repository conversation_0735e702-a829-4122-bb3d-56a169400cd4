<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright © 2013-2014 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC), International Centre for Free and Open Source Software (ICFOSS)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case latin:keyboardLayoutSetElement="alphabet|alphabetAutomaticShifted" >
            <Key
                latin:keySpec="&#x094A;"
                latin:moreKeys="`"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0967;"
                latin:moreKeys="1, !text/morekeys_symbols_1"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0968;"
                latin:moreKeys="2, !text/morekeys_symbols_2"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0969;"
                latin:moreKeys="3, !text/morekeys_symbols_3"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x096A;"
                latin:moreKeys="4, !text/morekeys_symbols_4"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x096B;"
                latin:moreKeys="5, !text/morekeys_symbols_5"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x096C;"
                latin:moreKeys="6, !text/morekeys_symbols_6"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x096D;"
                latin:moreKeys="7, !text/morekeys_symbols_7"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x096E;"
                latin:moreKeys="8, !text/morekeys_symbols_8"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x096F;"
                latin:moreKeys="9, !text/morekeys_symbols_9"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0966;"
                latin:moreKeys="0, !text/morekeys_symbols_0"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="-"
                latin:moreKeys="&#x2013;,&#x2014;,&#x00B7;"
                latin:keyLabelFlags="fontNormal" />

            <!--\= ृ DEVANAGARI VOWEL SIGN VOCALIC R-->
            <Key
                latin:keySpec="&#x0943;"
                latin:moreKeys="="
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
        </case>
        <default>
            <Key
                latin:keySpec="&#x0912;"
                latin:moreKeys="~"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--! ऍ DEVANAGARI LETTER CANDRA E-->
            <Key
                latin:keySpec="&#x090D;"
                latin:moreKeys="!"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\@ ॅ DEVANAGARI VOWEL SIGN CANDRA E-->
            <Key
                latin:keySpec="&#x0945;"
                latin:moreKeys="\@"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\# ्र  -->
            <Key
                latin:keySpec="\#"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\$ र्  -->
            <Key
                latin:keySpec="&#x20b9;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\% ज्ञ  -->
            <Key
                latin:keySpec="&#x091C;&#x094D;&#x091E;"
                latin:moreKeys="%"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\^ त्र  -->
            <Key
                latin:keySpec="&#x0924;&#x094D;&#x0930;"
                latin:moreKeys="^"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\& क्ष  -->
            <Key
                latin:keySpec="&#x0915;&#x094D;&#x0937;"
                latin:moreKeys="&amp;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\* श्र  -->
            <Key
                latin:keySpec="&#x0936;&#x094D;&#x0930;"
                latin:moreKeys="*"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\( ‍ ZERO WIDTH JOINER-->
            <Key
                latin:keySpec="!icon/zwj_key|&#x200D;"
                latin:moreKeys="("
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\) ‌ ZERO WIDTH NON-JOINER-->
            <Key
                latin:keySpec="!icon/zwnj_key|&#x200C;"
                latin:moreKeys=")"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--_ ः DEVANAGARI SIGN VISARGA-->
            <Key
                latin:keySpec="&#x0903;"
                latin:moreKeys="_"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\+ ऋ DEVANAGARI LETTER VOCALIC R-->
            <Key
                latin:keySpec="&#x090B;"
                latin:moreKeys="+, &#x00B1;,&#x00D7;,&#x00F7;,&#x221A;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
        </default>
    </switch>

</merge>
