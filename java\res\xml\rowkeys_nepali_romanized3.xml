<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted">
            <!-- U+090B: "ऋ" DEVANAGARI LETTER VOCALIC R -->
            <Key latin:keySpec="&#x090B;" />
            <!-- U+0922: "ढ" DEVANAGARI LETTER DDHA -->
            <Key latin:keySpec="&#x0922;" />
            <!-- U+091B: "छ" DEVANAGARI LETTER CHA -->
            <Key latin:keySpec="&#x091B;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_candrabindu" />
            <Key latin:keyStyle="baseKeyDevanagariSignCandrabindu" />
            <!-- U+092D: "भ" DEVANAGARI LETTER BHA -->
            <Key latin:keySpec="&#x092D;" />
            <!-- U+0923: "ण" DEVANAGARI LETTER NNA -->
            <Key latin:keySpec="&#x0923;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_anusvara" />
            <Key latin:keyStyle="baseKeyDevanagariSignAnusvara" />
            <!-- U+0919: "ङ" DEVANAGARI LETTER NGA -->
            <Key latin:keySpec="&#x0919;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_virama" />
            <Key latin:keyStyle="baseKeyDevanagariSignVirama" />
        </case>
        <default>
            <!-- U+0937: "ष" DEVANAGARI LETTER SSA -->
            <Key latin:keySpec="&#x0937;" />
            <!-- U+0921: "ड" DEVANAGARI LETTER DDA -->
            <Key latin:keySpec="&#x0921;" />
            <!-- U+091A: "च" DEVANAGARI LETTER CA -->
            <Key latin:keySpec="&#x091A;" />
            <!-- U+0935: "व" DEVANAGARI LETTER VA -->
            <Key latin:keySpec="&#x0935;" />
            <!-- U+092C: "ब" DEVANAGARI LETTER BHA -->
            <Key latin:keySpec="&#x092C;" />
            <!-- U+0928: "न" DEVANAGARI LETTER NA -->
            <Key latin:keySpec="&#x0928;" />
            <!-- U+092E: "म" DEVANAGARI LETTER MA -->
            <Key latin:keySpec="&#x092E;" />
            <!-- U+0964: "।" DEVANAGARI DANDA
                 U+093D: "ऽ" DEVANAGARI SIGN AVAGRAHA -->
            <Key
                latin:keySpec="&#x0964;"
                latin:moreKeys="&#x093D;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_virama" />
            <Key latin:keyStyle="baseKeyDevanagariSignVirama" />
        </default>
    </switch>
</merge>
