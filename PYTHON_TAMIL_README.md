# Tamil Transliteration Engine (Python)

A Python implementation of Tamil transliteration logic based on the Indic Keyboard project. Converts English phonetic input to Tamil script in real-time.

## 🚀 Quick Start

### Simple Run
```bash
python tamil_transliterator.py
```

### Menu-driven Interface
```bash
python run_tamil.py
```

### Quick Test
```bash
python test_tamil.py
```

## 📁 Files

- **`tamil_transliterator.py`** - Main transliteration engine
- **`run_tamil.py`** - Menu-driven interface
- **`test_tamil.py`** - Quick testing script
- **`requirements.txt`** - Dependencies (none needed!)

## ✨ Features

### 1. **Real-time Simulation**
See exactly how typing works character by character:
```
Typed: v               -> Tamil: வ
Typed: va              -> Tamil: வ
Typed: van             -> Tamil: வன்
Typed: vana            -> Tamil: வன
Typed: vanakkam        -> Tamil: வணக்கம்
```

### 2. **Interactive Mode**
```python
Tamil> vanakkam
Tamil: வணக்கம்

Tamil> realtime nandri
Simulating real-time typing for: 'nandri'
Typed: n               -> Tamil: ந
Typed: na              -> Tamil: ந
Typed: nan             -> Tamil: நன்
Typed: nand            -> Tamil: நன்ட்
Typed: nandr           -> Tamil: நன்ட்ர்
Typed: nandri          -> Tamil: நன்றி
```

### 3. **Batch Testing**
Test multiple words at once:
```python
vanakkam        -> வணக்கம்
nandri          -> நன்றி
amma            -> அம்மா
appa            -> அப்பா
tamil           -> தமிழ்
```

## 🔧 Usage Examples

### Basic Usage
```python
from tamil_transliterator import TamilTransliterator

# Create transliterator
transliterator = TamilTransliterator()

# Transliterate a word
result = transliterator.transliterate("vanakkam")
print(result)  # Output: வணக்கம்

# Real-time simulation
from tamil_transliterator import simulate_real_time_typing
simulate_real_time_typing(transliterator, "nandri")
```

### Advanced Usage
```python
# Test custom words
words = ["hello", "world", "computer"]
for word in words:
    tamil = transliterator.transliterate(word)
    print(f"{word} -> {tamil}")

# Character-by-character analysis
word = "vanakkam"
for i in range(1, len(word) + 1):
    partial = word[:i]
    result = transliterator.transliterate(partial)
    print(f"{partial} -> {result}")
```

## 📝 Supported Conversions

### Vowels
```
a -> அ    aa -> ஆ    i -> இ     ii -> ஈ
u -> உ    uu -> ஊ    e -> எ     ee -> ஏ
ai -> ஐ   o -> ஒ     oo -> ஓ    au -> ஔ
```

### Consonants
```
k -> க    ng -> ங    ch -> ச    nj -> ஞ
t -> ட    n -> ந     th -> த    p -> ப
m -> ம    y -> ய     r -> ர     l -> ல
v -> வ    zh -> ழ    ll -> ள    rr -> ற
```

### Common Words
```
vanakkam -> வணக்கம்    (Hello)
nandri -> நன்றி        (Thank you)
amma -> அம்மா          (Mother)
appa -> அப்பா          (Father)
tamil -> தமிழ்         (Tamil)
naan -> நான்           (I)
neenga -> நீங்க        (You)
sari -> சரி            (Okay)
illa -> இல்ல          (No)
```

## 🎯 Test Words to Try

### Greetings & Politeness
- `vanakkam` (Hello)
- `nandri` (Thank you)
- `mannikkavum` (Sorry)

### Family Members
- `amma` (Mother)
- `appa` (Father)
- `anna` (Elder brother)
- `akka` (Elder sister)
- `thambi` (Younger brother)
- `thangai` (Younger sister)

### Question Words
- `enna` (What)
- `eppo` (When)
- `enga` (Where)
- `eppadi` (How)
- `yen` (Why)

### Common Responses
- `sari` (Okay)
- `illa` (No)
- `aam` (Yes)
- `theriyala` (Don't know)

### Basic Syllables
- `ka`, `ki`, `ku`, `ke`, `ko`
- `ma`, `mi`, `mu`, `me`, `mo`
- `na`, `ni`, `nu`, `ne`, `no`
- `ta`, `ti`, `tu`, `te`, `to`

## 🔍 How It Works

The engine replicates the Indic Keyboard's logic:

1. **Progressive Matching**: Finds the longest possible Tamil match
2. **Context Awareness**: Considers character combinations
3. **Vowel Signs**: Automatically combines consonants with vowel signs
4. **Real-time Processing**: Updates output as each character is typed

### Core Algorithm
```python
def transliterate(self, input_text, context="", is_complete=False):
    # 1. Check for complete word matches
    if input_text in self.complex_map:
        return self.complex_map[input_text]
    
    # 2. Progressive character-by-character conversion
    return self._progressive_transliterate(input_text)

def _progressive_transliterate(self, input_text):
    # 3. Find longest matches at each position
    # 4. Combine consonants with vowel signs
    # 5. Build final Tamil output
```

## 🎮 Interactive Commands

When running the interactive mode:

- **Type any word**: Get immediate Tamil output
- **`realtime <word>`**: See character-by-character conversion
- **`batch`**: Test common Tamil words
- **`help`**: Show available commands
- **`exit`** or **`quit`**: Exit the program

## 🛠️ Customization

### Add New Words
```python
# In tamil_transliterator.py, add to complex_map:
self.complex_map.update({
    'your_word': 'உங்கள்_வார்த்தை',
    'another_word': 'மற்றொரு_வார்த்தை',
})
```

### Add New Consonants/Vowels
```python
# Add to consonant_map or vowel_map:
self.consonant_map['new_consonant'] = 'புதிய_மெய்'
self.vowel_map['new_vowel'] = 'புதிய_உயிர்'
```

## 🎯 Requirements

- **Python 3.6+** (for proper Unicode support)
- **No external dependencies** (uses only standard library)

## 🔗 Based On

This implementation follows the logic from:
- **Indic Keyboard** project
- **WordComposer.applyTransliteration()** method
- **SMC IME** (Swathanthra Malayalam Computing) principles
- Tamil Unicode standard (U+0B80–U+0BFF)

## 🚀 Performance

- **Real-time**: Processes characters instantly
- **Memory efficient**: Uses simple dictionaries
- **Unicode compliant**: Proper Tamil script rendering
- **Cross-platform**: Works on Windows, Mac, Linux

Perfect for learning Tamil typing, testing transliteration algorithms, or building Tamil input applications!
