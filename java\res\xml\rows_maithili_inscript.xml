<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright © 2015 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <include latin:keyboardLayout="@xml/key_styles_common" />
    <Row latin:keyWidth="7.692%p" >
        <include latin:keyboardLayout="@xml/rowkeys_hindi_inscript1" />
    </Row>
    <Row latin:keyWidth="7.692%p" >
        <include latin:keyboardLayout="@xml/rowkeys_hindi_inscript2" />
    </Row>
    <Row latin:keyWidth="7.692%p" >
        <include
            latin:keyboardLayout="@xml/rowkeys_hindi_inscript3"
            latin:keyXPos="3.846%p" />
        <Key
            latin:keyStyle="deleteKeyStyle"
            latin:keyWidth="fillRight"
            latin:visualInsetsLeft="1%p" />
    </Row>
    <Row latin:keyWidth="7.692%p" >
        <Key
            latin:keyStyle="shiftKeyStyle"
            latin:keyWidth="11.538%p"
            latin:visualInsetsRight="1%p" />
        <include latin:keyboardLayout="@xml/rowkeys_maithili_inscript4" />
        <Key
            latin:keyStyle="shiftKeyStyle"
            latin:keyWidth="fillRight"
            latin:visualInsetsLeft="1%p" />
    </Row>
    <include latin:keyboardLayout="@xml/row_pcqwerty5" />
</merge>
