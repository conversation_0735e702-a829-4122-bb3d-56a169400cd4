<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright © 2013-2014 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC), International Centre for Free and Open Source Software (ICFOSS)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
 >
    <switch>
        <case
            latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted"
            >
            <!-- A: aa matra, aa, short a -->
            <Key
                latin:keySpec="&#x0C06;"
                latin:keyLabelFlags="fontNormal" />
            <!-- S: sa, sha -->
            <Key
                latin:keySpec="&#x0C36;"
                latin:keyLabelFlags="fontNormal" />
            <!-- D: dental da, dha -->
            <Key
                latin:keySpec="&#x0C27;"
                latin:keyLabelFlags="fontNormal" />
            <!-- F: virama, vocalic RR matra, vocalic RR -->
            <Key
                latin:keySpec="&#x0C44;"
                latin:keyLabelFlags="fontNormal" />
            <!-- G: ga, gha -->
            <Key
                latin:keySpec="&#x0C18;"
                latin:keyLabelFlags="fontNormal" />
            <!-- H: ha, visarga, jihvanuliya, upadhmaniya -->
            <Key
                latin:keySpec="&#x0C03;"
                
                latin:keyLabelFlags="fontNormal" />
            <!-- J: ja, jha -->
            <Key
                latin:keySpec="&#x0C1D;"
                latin:keyLabelFlags="fontNormal" />
            <!-- K: ka, kha -->
            <Key
                latin:keySpec="&#x0C16;"
                latin:keyLabelFlags="fontNormal" />
            <!-- L: la, lla, vocalic L matra, vocalic L -->
            <Key
                latin:keySpec="&#x0C33;"
                latin:moreKeys="&#x0C0C;"
                latin:keyLabelFlags="fontNormal" />
        </case>

        <default>
            <!-- A: aa matra, aa, short a -->
            <Key
                latin:keySpec="&#x0C3E;"
                latin:moreKeys="&#x0C05;"
                latin:keyLabelFlags="fontNormal" />
            <!--  S: sa, sha -->
            <Key
                latin:keySpec="&#x0C38;"
                latin:keyLabelFlags="fontNormal" />
            <!-- D: dental da, dha -->
            <Key
                latin:keySpec="&#x0C26;"
                latin:keyLabelFlags="fontNormal" />
            <!-- F: virama, vocalic RR matra, vocalic RR -->
            <Key
                latin:keySpec="&#x0C4D;"
                latin:moreKeys="&#x0C60;"
                latin:keyLabelFlags="fontNormal" />
            <!-- G: ga, gha -->
            <Key
                latin:keySpec="&#x0C17;"
                latin:keyLabelFlags="fontNormal" />
            <!-- H: ha, visarga, jihvanuliya, upadhmaniya -->
            <Key
                latin:keySpec="&#x0C39;"
                latin:keyLabelFlags="fontNormal" />
            <!-- J: ja, jha -->
            <Key
                latin:keySpec="&#x0C1C;"
                latin:keyLabelFlags="fontNormal" />
            <!-- K: ka, kha -->
            <Key
                latin:keySpec="&#x0C15;"
                latin:keyLabelFlags="fontNormal" />
            <!-- L: la, lla, vocalic L matra, vocalic L -->
            <Key
                latin:keySpec="&#x0C32;"
                latin:moreKeys="&#x0C62;"
                latin:keyLabelFlags="fontNormal" />
        </default>
    </switch>
</merge>
