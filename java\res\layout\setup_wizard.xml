<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/setup_wizard"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/setup_background"
    android:paddingLeft="@dimen/setup_horizontal_padding"
    android:paddingRight="@dimen/setup_horizontal_padding"
    android:paddingTop="@dimen/setup_vertical_padding"
    android:paddingBottom="@dimen/setup_vertical_padding">
    <include
        android:id="@+id/setup_welcome_screen"
        layout="@layout/setup_welcome_screen" />
    <include
        android:id="@+id/setup_steps_screen"
        layout="@layout/setup_steps_screen" />
</FrameLayout>
