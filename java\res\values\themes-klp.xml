<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources xmlns:android="http://schemas.android.com/apk/res/android">
    <style name="KeyboardTheme.KLP" parent="KeyboardIcons.Holo">
        <item name="keyboardStyle">@style/Keyboard.KLP</item>
        <item name="keyboardViewStyle">@style/KeyboardView.KLP</item>
        <item name="mainKeyboardViewStyle">@style/MainKeyboardView.KLP</item>
        <item name="emojiPalettesViewStyle">@style/EmojiPalettesView.KLP</item>
        <item name="moreKeysKeyboardStyle">@style/MoreKeysKeyboard.KLP</item>
        <!-- Note: KLP theme uses the same style for both general more keys and action more keys. -->
        <item name="moreKeysKeyboardViewStyle">@style/MoreKeysKeyboardView.KLP</item>
        <item name="moreKeysKeyboardViewForActionStyle">@style/MoreKeysKeyboardView.KLP</item>
        <item name="suggestionStripViewStyle">@style/SuggestionStripView.KLP</item>
        <item name="suggestionWordStyle">@style/SuggestionWord.KLP</item>
    </style>
    <style
        name="Keyboard.KLP"
        parent="Keyboard"
    >
        <!-- This should be aligned with KeyboardTheme.THEME_ID_* -->
        <item name="themeId">KLP</item>
    </style>
    <style
        name="KeyboardView.KLP"
        parent="KeyboardView.Holo"
    >
        <item name="android:background">@drawable/keyboard_background_holo</item>
        <item name="keyBackground">@drawable/btn_keyboard_key_klp</item>
        <item name="functionalKeyBackground">@drawable/btn_keyboard_key_functional_klp</item>
        <item name="spacebarBackground">@drawable/btn_keyboard_spacebar_klp</item>
        <item name="keyTextColor">@color/key_text_color_holo</item>
        <item name="keyTextInactivatedColor">@color/key_text_inactivated_color_holo</item>
        <item name="functionalTextColor">@color/key_text_color_holo</item>
        <item name="keyHintLetterColor">@color/key_hint_letter_color_holo</item>
        <item name="keyHintLabelColor">@color/key_hint_label_color_holo</item>
        <item name="keyShiftedLetterHintInactivatedColor">@color/key_shifted_letter_hint_inactivated_color_holo</item>
        <item name="keyShiftedLetterHintActivatedColor">@color/key_shifted_letter_hint_activated_color_holo</item>
        <item name="keyPreviewTextColor">@color/key_text_color_holo</item>
    </style>
    <style
        name="MainKeyboardView.KLP"
        parent="KeyboardView.KLP"
    >
        <item name="keyPreviewBackground">@drawable/keyboard_key_feedback_klp</item>
        <item name="keyPreviewHeight">@dimen/config_key_preview_height_holo</item>
        <item name="keyPreviewOffset">@dimen/config_key_preview_offset_holo</item>
        <item name="keyPreviewShowUpAnimator">@anim/key_preview_show_up_holo</item>
        <item name="keyPreviewDismissAnimator">@anim/key_preview_dismiss_holo</item>
        <item name="gestureFloatingPreviewTextColor">@color/highlight_color_klp</item>
        <item name="gestureFloatingPreviewColor">@color/gesture_floating_preview_color_holo</item>
        <item name="gestureTrailColor">@color/highlight_color_klp</item>
        <item name="slidingKeyInputPreviewColor">@color/highlight_translucent_color_klp</item>
        <item name="languageOnSpacebarTextColor">@color/spacebar_text_color_holo</item>
        <item name="languageOnSpacebarTextShadowRadius">1.0</item>
        <item name="languageOnSpacebarTextShadowColor">@color/spacebar_text_shadow_color_holo</item>
    </style>
    <!-- Though {@link EmojiPalettesView} doesn't extend {@link KeyboardView}, some views inside it,
         for instance delete button, need themed {@link KeyboardView} attributes. -->
    <style
        name="EmojiPalettesView.KLP"
        parent="MainKeyboardView.KLP"
    >
        <item name="categoryIndicatorEnabled">true</item>
        <item name="categoryIndicatorDrawable">@drawable/emoji_category_tab_selected_klp</item>
        <item name="categoryIndicatorBackground">@drawable/emoji_category_tab_unselected_holo_dark</item>
        <item name="categoryPageIndicatorColor">@color/highlight_color_klp</item>
        <item name="categoryPageIndicatorBackground">@color/emoji_tab_page_indicator_background_holo</item>
        <item name="iconEmojiRecentsTab">@drawable/ic_emoji_recents_holo_dark</item>
        <item name="iconEmojiCategory1Tab">@drawable/ic_emoji_people_holo_dark</item>
        <item name="iconEmojiCategory2Tab">@drawable/ic_emoji_objects_holo_dark</item>
        <item name="iconEmojiCategory3Tab">@drawable/ic_emoji_nature_holo_dark</item>
        <item name="iconEmojiCategory4Tab">@drawable/ic_emoji_places_holo_dark</item>
        <item name="iconEmojiCategory5Tab">@drawable/ic_emoji_symbols_holo_dark</item>
        <item name="iconEmojiCategory6Tab">@drawable/ic_emoji_emoticons_holo_dark</item>
    </style>
    <style
        name="MoreKeysKeyboard.KLP"
        parent="Keyboard.KLP"
    >
        <item name="keyboardTopPadding">0%p</item>
        <item name="keyboardBottomPadding">0%p</item>
        <item name="horizontalGap">0%p</item>
        <item name="touchPositionCorrectionData">@null</item>
    </style>
    <style
        name="MoreKeysKeyboardView.KLP"
        parent="KeyboardView.KLP"
    >
        <item name="android:background">@drawable/keyboard_popup_panel_background_klp</item>
        <item name="keyBackground">@drawable/btn_keyboard_key_popup_klp</item>
        <item name="divider">@drawable/more_keys_divider</item>
        <item name="keyTypeface">normal</item>
        <item name="verticalCorrection">@dimen/config_more_keys_keyboard_vertical_correction_holo</item>
    </style>
    <style
        name="SuggestionStripView.KLP"
        parent="KeyboardView.KLP"
    >
        <item name="suggestionsCountInStrip">@integer/config_suggestions_count_in_strip</item>
        <item name="centerSuggestionPercentile">@fraction/config_center_suggestion_percentile</item>
        <item name="maxMoreSuggestionsRow">@integer/config_max_more_suggestions_row</item>
        <item name="minMoreSuggestionsWidth">@fraction/config_min_more_suggestions_width</item>
        <item name="android:background">@drawable/keyboard_suggest_strip_holo</item>
        <item name="android:src">@drawable/suggestions_strip_divider_holo</item>
        <item name="suggestionStripOptions">autoCorrectBold|validTypedWordBold</item>
        <item name="colorValidTypedWord">@color/typed_word_color_klp</item>
        <item name="colorTypedWord">@color/typed_word_color_klp</item>
        <item name="colorAutoCorrect">@color/highlight_color_klp</item>
        <item name="colorSuggested">@color/suggested_word_color_klp</item>
        <item name="alphaObsoleted">70%</item>
    </style>
    <style
        name="SuggestionWord.KLP"
        parent="SuggestionWord"
    >
        <item name="android:background">@drawable/btn_suggestion_klp</item>
        <item name="android:textColor">@color/highlight_color_klp</item>
    </style>
</resources>
