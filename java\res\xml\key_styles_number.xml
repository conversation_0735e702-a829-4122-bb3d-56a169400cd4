<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2010, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <key-style
        latin:styleName="numKeyBaseStyle"
        latin:keyActionFlags="noKeyPreview" />
    <key-style
        latin:styleName="numKeyStyle"
        latin:keyLabelFlags="followKeyLargeLetterRatio"
        latin:parentStyle="numKeyBaseStyle" />
    <key-style
        latin:styleName="numModeKeyStyle"
        latin:keyLabelFlags="fontNormal|followKeyLetterRatio|followFunctionalTextColor"
        latin:parentStyle="numKeyBaseStyle" />
    <key-style
        latin:styleName="numFunctionalKeyStyle"
        latin:keyLabelFlags="followKeyLargeLetterRatio"
        latin:backgroundType="functional"
        latin:parentStyle="numKeyBaseStyle" />
    <key-style
        latin:styleName="numberKeyStyle"
        latin:keyLabelFlags="alignLabelOffCenter|hasHintLabel"
        latin:parentStyle="numKeyStyle" />
    <key-style
        latin:styleName="num0KeyStyle"
        latin:keySpec="0"
        latin:parentStyle="numberKeyStyle" />
    <key-style
        latin:styleName="num1KeyStyle"
        latin:keySpec="1"
        latin:parentStyle="numberKeyStyle" />
    <key-style
        latin:styleName="num2KeyStyle"
        latin:keySpec="2"
        latin:keyHintLabel="ABC"
        latin:parentStyle="numberKeyStyle" />
    <key-style
        latin:styleName="num3KeyStyle"
        latin:keySpec="3"
        latin:keyHintLabel="DEF"
        latin:parentStyle="numberKeyStyle" />
    <key-style
        latin:styleName="num4KeyStyle"
        latin:keySpec="4"
        latin:keyHintLabel="GHI"
        latin:parentStyle="numberKeyStyle" />
    <key-style
        latin:styleName="num5KeyStyle"
        latin:keySpec="5"
        latin:keyHintLabel="JKL"
        latin:parentStyle="numberKeyStyle" />
    <key-style
        latin:styleName="num6KeyStyle"
        latin:keySpec="6"
        latin:keyHintLabel="MNO"
        latin:parentStyle="numberKeyStyle" />
    <key-style
        latin:styleName="num7KeyStyle"
        latin:keySpec="7"
        latin:keyHintLabel="PQRS"
        latin:parentStyle="numberKeyStyle" />
    <key-style
        latin:styleName="num8KeyStyle"
        latin:keySpec="8"
        latin:keyHintLabel="TUV"
        latin:parentStyle="numberKeyStyle" />
    <key-style
        latin:styleName="num9KeyStyle"
        latin:keySpec="9"
        latin:keyHintLabel="WXYZ"
        latin:parentStyle="numberKeyStyle" />
    <!-- U+FF0A: "＊" FULLWIDTH ASTERISK -->
    <key-style
        latin:styleName="numStarKeyStyle"
        latin:keySpec="&#xFF0A;|*"
        latin:parentStyle="numKeyStyle" />
    <!-- Only for non-tablet device -->
    <key-style
        latin:styleName="numPhoneToSymbolKeyStyle"
        latin:keySpec="!text/keylabel_to_phone_symbols|!code/key_switch_alpha_symbol"
        latin:parentStyle="numModeKeyStyle" />
    <key-style
        latin:styleName="numPhoneToNumericKeyStyle"
        latin:keySpec="!text/keylabel_to_phone_numeric|!code/key_switch_alpha_symbol"
        latin:parentStyle="numModeKeyStyle" />
    <key-style
        latin:styleName="numPauseKeyStyle"
        latin:keySpec="!text/label_pause_key|,"
        latin:keyLabelFlags="followKeyHintLabelRatio|autoXScale"
        latin:parentStyle="numKeyBaseStyle" />
    <key-style
        latin:styleName="numWaitKeyStyle"
        latin:keySpec="!text/label_wait_key|;"
        latin:keyLabelFlags="followKeyHintLabelRatio|autoXScale"
        latin:parentStyle="numKeyBaseStyle" />
    <key-style
        latin:styleName="numTabKeyStyle"
        latin:keyActionFlags="noKeyPreview"
        latin:parentStyle="tabKeyStyle" />
    <key-style
        latin:styleName="numSpaceKeyStyle"
        latin:keySpec="!icon/space_key_for_number_layout|!code/key_space"
        latin:keyLabelFlags="alignIconToBottom"
        latin:keyActionFlags="enableLongPress"
        latin:parentStyle="numKeyBaseStyle" />
    <!-- TODO: Consolidate these space key styles with numSpaceKeyStyle above by introducing <case>
         predicator that checks device form-factor. -->
    <switch>
        <case latin:keyboardTheme="ICS|KLP">
            <key-style
                latin:styleName="tabletNumSpaceKeyStyle"
                latin:keySpec="!icon/space_key|!code/key_space"
                latin:backgroundType="functional"
                latin:keyActionFlags="enableLongPress"
                latin:parentStyle="numKeyBaseStyle" />
        </case>
        <case latin:keyboardTheme="LXXLight|LXXDark">
            <key-style
                latin:styleName="tabletNumSpaceKeyStyle"
                latin:keySpec="!icon/space_key|!code/key_space"
                latin:backgroundType="spacebar"
                latin:keyActionFlags="enableLongPress"
                latin:parentStyle="numKeyBaseStyle" />
        </case>
    </switch>
</merge>
