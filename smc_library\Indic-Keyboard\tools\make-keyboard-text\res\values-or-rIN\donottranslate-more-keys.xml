<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, <PERSON><PERSON><PERSON> <PERSON> <<EMAIL>>
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="keyspec_symbols_1">&#x0B67;</string>
    <string name="keyspec_symbols_2">&#x0B68;</string>
    <string name="keyspec_symbols_3">&#x0B69;</string>
    <string name="keyspec_symbols_4">&#x0B6A;</string>
    <string name="keyspec_symbols_5">&#x0B6B;</string>
    <string name="keyspec_symbols_6">&#x0B6C;</string>
    <string name="keyspec_symbols_7">&#x0B6D;</string>
    <string name="keyspec_symbols_8">&#x0B6E;</string>
    <string name="keyspec_symbols_9">&#x0B6F;</string>
    <string name="keyspec_symbols_0">&#x0B66;</string>
    <string name="keyspec_comma">&#x0B3C;</string>
    <string name="additional_morekeys_symbols_1">1</string>
    <string name="additional_morekeys_symbols_2">2</string>
    <string name="additional_morekeys_symbols_3">3</string>
    <string name="additional_morekeys_symbols_4">4</string>
    <string name="additional_morekeys_symbols_5">5</string>
    <string name="additional_morekeys_symbols_6">6</string>
    <string name="additional_morekeys_symbols_7">7</string>
    <string name="additional_morekeys_symbols_8">8</string>
    <string name="additional_morekeys_symbols_9">9</string>
    <string name="additional_morekeys_symbols_0">0</string>
    <string name="keylabel_to_alpha">&#x0B05;</string>
    <string name="keylabel_to_symbol">&#x0B67;&#x0B68;&#x0B69;</string>
</resources>
