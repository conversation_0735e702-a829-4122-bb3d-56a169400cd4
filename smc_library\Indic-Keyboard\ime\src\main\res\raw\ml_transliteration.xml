<?xml version="1.0"?>
<inputmethod id="ml-transliteration" name="ലിപ്യന്തരണം" description="Malayalam Transliteration based input method" author="Junaid P V" version="1.0" contextLength="2" maxKeyLength="4">
  <pattern input="(‌)+a" replacement="അ"/>
  <pattern input="(‌)+A" replacement="ആ"/>
  <pattern input="(‌)+i" replacement="ഇ"/>
  <pattern input="(‌)+u" replacement="ഉ"/>
  <pattern input="(‌)+U" replacement="ഊ"/>
  <pattern input="(‌)+R" replacement="ഋ"/>
  <pattern input="(‌)+e" replacement="എ"/>
  <pattern input="(‌)+E" replacement="ഏ"/>
  <pattern input="(‌)+I" replacement="ഐ"/>
  <pattern input="(‌)+o" replacement="ഒ"/>
  <pattern input="(‌)+O" replacement="ഓ"/>
  <pattern input="(‌)+H" replacement="ഃ"/>
  <pattern input="ൻൿh" context="nc" replacement="ഞ്ച്"/>
  <pattern input="ൿh" context="c" replacement="ച്"/>
  <pattern input="ക്ക്h" context="cc" replacement="ച്ച്"/>
  <pattern input="ല്പ്p" context="lp" replacement="ൽപ്പ്"/>
  <pattern input="\\([A-Za-z\&gt;_~])" context="\\" replacement="$1"/>
  <pattern input="([ക-ഹ])്R" replacement="$1ൃ"/>
  <pattern input="ന്ന്j" replacement="ഞ്ഞ്"/>
  <pattern input="ന്ന്g" replacement="ങ്ങ്"/>
  <pattern input="ട്ട്h" replacement="ത്ത്"/>
  <pattern input="റ്റ്h" replacement="ത്"/>
  <pattern input="റ്റ്t" replacement="ട്ട്"/>
  <pattern input="ന്റ്h" replacement="ന്ത്"/>
  <pattern input="([ക-ഹ])്ല്l" context="L" replacement="$1ൢ"/>
  <pattern input="([ക-ഹ])ൢl" replacement="$1ൣ"/>
  <pattern input="([അ-ഊഌഎ-ഐഒ-ഹ])([ഽ-ൂെ-ൌൗ])+R" replacement="$1$2റ്"/>
  <pattern input="([ക-ഹ])ൃa" replacement="$1്ര"/>
  <pattern input="([ക-ഹ])ൃA" replacement="$1്രാ"/>
  <pattern input="([ക-ഹ])ൃi" replacement="$1്രി"/>
  <pattern input="([ക-ഹ])ൃI" replacement="$1്രീ"/>
  <pattern input="([ക-ഹ])ൃu" replacement="$1്രു"/>
  <pattern input="([ക-ഹ])ൃU" replacement="$1്രൂ"/>
  <pattern input="([ക-ഹ])ൃe" replacement="$1്രെ"/>
  <pattern input="([ക-ഹ])ൃE" replacement="$1്രേ"/>
  <pattern input="([ക-ഹ])ൃo" replacement="$1്രൊ"/>
  <pattern input="([ക-ഹ])ൃO" replacement="$1്രോ"/>
  <pattern input="([ക-ഹ])്(l|L)" replacement="$1്ല്"/>
  <pattern input="അa" replacement="ആ"/>
  <pattern input="അi" replacement="ഐ"/>
  <pattern input="അu" replacement="ഔ"/>
  <pattern input="ഇi" replacement="ഈ"/>
  <pattern input="ഉu" replacement="ഊ"/>
  <pattern input="ഊo" replacement="ഊഊ"/>
  <pattern input="ഋR" replacement="ൠ"/>
  <pattern input="ഋa" replacement="റ"/>
  <pattern input="ഋA" replacement="റാ"/>
  <pattern input="ഋi" replacement="റി"/>
  <pattern input="ഋI" replacement="റീ"/>
  <pattern input="ഋu" replacement="റു"/>
  <pattern input="ഋU" replacement="റൂ"/>
  <pattern input="ഋe" replacement="റെ"/>
  <pattern input="ഋE" replacement="റേ"/>
  <pattern input="ഋo" replacement="റൊ"/>
  <pattern input="ഋO" replacement="റോ"/>
  <pattern input="ഋ~" replacement="റ്"/>
  <pattern input="ഌl" replacement="ൡ"/>
  <pattern input="എe" replacement="ഈ"/>
  <pattern input="ഒo" replacement="ഊ"/>
  <pattern input="ാa" replacement="ാാ"/>
  <pattern input="ീi" replacement="ീീ"/>
  <pattern input="ൂu" replacement="ൂൂ"/>
  <pattern input="ൂo" replacement="ൂൂ"/>
  <pattern input="ൄR" replacement="ൄൄ"/>
  <pattern input="േE" replacement="േേ"/>
  <pattern input="ോO" replacement="ോോ"/>
  <pattern input="ൗu" replacement="ൗൗ"/>
  <pattern input="ൢ\^" replacement="ൣ"/>
  <pattern input="ൣ\^" replacement="ൣൣ"/>
  <pattern input="ക്h" replacement="ഖ്"/>
  <pattern input="ഗ്h" replacement="ഘ്"/>
  <pattern input="ൻg" replacement="ങ്"/>
  <pattern input="ച്h" replacement="ഛ്"/>
  <pattern input="ജ്h" replacement="ഝ്"/>
  <pattern input="ട്h" replacement="ഠ്"/>
  <pattern input="ഡ്h" replacement="ഢ്"/>
  <pattern input="ത്h" replacement="ഥ്"/>
  <pattern input="ദ്h" replacement="ധ്"/>
  <pattern input="പ്h" replacement="ഫ്"/>
  <pattern input="ബ്h" replacement="ഭ്"/>
  <pattern input="ംa" replacement="മ"/>
  <pattern input="ംA" replacement="മാ"/>
  <pattern input="ംi" replacement="മി"/>
  <pattern input="ംI" replacement="മീ"/>
  <pattern input="ംu" replacement="മു"/>
  <pattern input="ംU" replacement="മൂ"/>
  <pattern input="ംR" replacement="മൃ"/>
  <pattern input="ംe" replacement="മെ"/>
  <pattern input="ംE" replacement="മേ"/>
  <pattern input="ംo" replacement="മൊ"/>
  <pattern input="ംO" replacement="മോ"/>
  <pattern input="ംm" replacement="മ്മ്"/>
  <pattern input="([^സ])ംr" replacement="$1മ്ര്"/>
  <pattern input="ംp" replacement="മ്പ്"/>
  <pattern input="([^സ])ം(l|L)" replacement="$1മ്ല്"/>
  <pattern input="ം~" replacement="മ്"/>
  <pattern input="([^സ])ംn" replacement="$1മ്ന്"/>
  <pattern input="([^സ])ംy" replacement="$1മ്യ്"/>
  <pattern input="സ്h" replacement="ഷ്"/>
  <pattern input="ശ്h" replacement="ഴ്"/>
  <pattern input="ൺ\\\\" replacement="ണ്‍"/>
  <pattern input="ൺ~" replacement="ണ്"/>
  <pattern input="ൺR" replacement="ണൃ"/>
  <pattern input="ൺN" replacement="ണ്ണ്"/>
  <pattern input="ൺT" replacement="ണ്ട്"/>
  <pattern input="ൺD" replacement="ണ്ഡ്"/>
  <pattern input="ൺy" replacement="ണ്യ്"/>
  <pattern input="ൺv" replacement="ണ്വ്"/>
  <pattern input="ൺm" replacement="ണ്മ്"/>
  <pattern input="ൻ\\\\" replacement="ന്‍"/>
  <pattern input="ൻ~" replacement="ന്"/>
  <pattern input="ൻr" replacement="ന്ര്"/>
  <pattern input="ൻR" replacement="നൃ"/>
  <pattern input="ൻk" replacement="ങ്ക്"/>
  <pattern input="ൻj" replacement="ഞ്"/>
  <pattern input="ൻn" replacement="ന്ന്"/>
  <pattern input="ൻd" replacement="ന്ദ്"/>
  <pattern input="ൻt" replacement="ന്റ്"/>
  <pattern input="ൻm" replacement="ന്മ്"/>
  <pattern input="ൻL" replacement="ന്ല്"/>
  <pattern input="ൻv" replacement="ന്വ്"/>
  <pattern input="ൻy" replacement="ന്യ്"/>
  <pattern input="ർ\\\\" replacement="ര്‍"/>
  <pattern input="ർ~" replacement="ര്"/>
  <pattern input="ർ#" replacement="ൎ"/>
  <pattern input="ർr" replacement="റ്"/>
  <pattern input="ർR" replacement="രൃ"/>
  <pattern input="ർy" replacement="ര്യ്"/>
  <pattern input="ൽ\\\\" replacement="ല്‍"/>
  <pattern input="ൽ~" replacement="ല്"/>
  <pattern input="ൽR" replacement="ലൃ"/>
  <pattern input="ൽ(l|L)" replacement="ല്ല്"/>
  <pattern input="ൽp" replacement="ല്പ്"/>
  <pattern input="ൽy" replacement="ല്യ്"/>
  <pattern input="ൽv" replacement="ല്വ്"/>
  <pattern input="ൾ\\\\" replacement="ള്‍"/>
  <pattern input="ൾ~" replacement="ള്"/>
  <pattern input="ൾR" replacement="ളൃ"/>
  <pattern input="ൾL" replacement="ള്ള്"/>
  <pattern input="ൾy" replacement="ള്യ്"/>
  <pattern input="ൾl" replacement="ഌ"/>
  <pattern input="ൿ\\\\" replacement="ക്‍"/>
  <pattern input="ൿR" replacement="കൃ"/>
  <pattern input="ൿy" replacement="ക്യ്"/>
  <pattern input="ൿ(l|L)" replacement="ക്ല്"/>
  <pattern input="ൿv" replacement="ക്വ്"/>
  <pattern input="ൿ(k|c)" replacement="ക്ക്"/>
  <pattern input="([ക-ഹ])a" replacement="$1ാ"/>
  <pattern input="([ക-ഹ])്a" replacement="$1"/>
  <pattern input="ൺa" replacement="ണ"/>
  <pattern input="ൻa" replacement="ന"/>
  <pattern input="ർa" replacement="ര"/>
  <pattern input="ൽa" replacement="ല"/>
  <pattern input="ൾa" replacement="ള"/>
  <pattern input="ൿa" replacement="ക"/>
  <pattern input="([ക-ഹ])്A" replacement="$1ാ"/>
  <pattern input="ൺA" replacement="ണാ"/>
  <pattern input="ൻA" replacement="നാ"/>
  <pattern input="ർA" replacement="രാ"/>
  <pattern input="ൽA" replacement="ലാ"/>
  <pattern input="ൾA" replacement="ളാ"/>
  <pattern input="ൿA" replacement="കാ"/>
  <pattern input="([ക-ഹ])്i" replacement="$1ി"/>
  <pattern input="ൺi" replacement="ണി"/>
  <pattern input="ൻi" replacement="നി"/>
  <pattern input="ർi" replacement="രി"/>
  <pattern input="ൽi" replacement="ലി"/>
  <pattern input="ൾi" replacement="ളി"/>
  <pattern input="ൿi" replacement="കി"/>
  <pattern input="([ക-ഹ])്I" replacement="$1ീ"/>
  <pattern input="ൺI" replacement="ണീ"/>
  <pattern input="ൻI" replacement="നീ"/>
  <pattern input="ർI" replacement="രീ"/>
  <pattern input="ൽI" replacement="ലീ"/>
  <pattern input="ൾI" replacement="ളീ"/>
  <pattern input="ൿI" replacement="കീ"/>
  <pattern input="([ക-ഹ])ിi" replacement="$1ീ"/>
  <pattern input="([ക-ഹ])െe" replacement="$1ീ"/>
  <pattern input="([ക-ഹ])്u" replacement="$1ു"/>
  <pattern input="ൺu" replacement="ണു"/>
  <pattern input="ൻu" replacement="നു"/>
  <pattern input="ർu" replacement="രു"/>
  <pattern input="ൽu" replacement="ലു"/>
  <pattern input="ൾu" replacement="ളു"/>
  <pattern input="ൿu" replacement="കു"/>
  <pattern input="([ക-ഹ])്U" replacement="$1ൂ"/>
  <pattern input="([ക-ഹ])ുu" replacement="$1ൂ"/>
  <pattern input="ൺU" replacement="ണൂ"/>
  <pattern input="ൻU" replacement="നൂ"/>
  <pattern input="ർU" replacement="രൂ"/>
  <pattern input="ൽU" replacement="ലൂ"/>
  <pattern input="ൾU" replacement="ളൂ"/>
  <pattern input="ൿU" replacement="കൂ"/>
  <pattern input="([ക-ഹ])ൊo" replacement="$1ൂ"/>
  <pattern input="([ക-ഹ])ൃR" replacement="$1ൄ"/>
  <pattern input="([ക-ഹ])്e" replacement="$1െ"/>
  <pattern input="ൺe" replacement="ണെ"/>
  <pattern input="ൻe" replacement="നെ"/>
  <pattern input="ർe" replacement="രെ"/>
  <pattern input="ൽe" replacement="ലെ"/>
  <pattern input="ൾe" replacement="ളെ"/>
  <pattern input="ൿe" replacement="കെ"/>
  <pattern input="([ക-ഹ])്E" replacement="$1േ"/>
  <pattern input="ൺE" replacement="ണേ"/>
  <pattern input="ൻE" replacement="നേ"/>
  <pattern input="ർE" replacement="രേ"/>
  <pattern input="ൽE" replacement="ലേ"/>
  <pattern input="ൾE" replacement="ളേ"/>
  <pattern input="ൿE" replacement="കേ"/>
  <pattern input="([ക-ഹ])i" replacement="$1ൈ"/>
  <pattern input="([ക-ഹ])്o" replacement="$1ൊ"/>
  <pattern input="ൺo" replacement="ണൊ"/>
  <pattern input="ൻo" replacement="നൊ"/>
  <pattern input="ർo" replacement="രൊ"/>
  <pattern input="ൽo" replacement="ലൊ"/>
  <pattern input="ൾo" replacement="ളൊ"/>
  <pattern input="ൿo" replacement="കൊ"/>
  <pattern input="([ക-ഹ])്O" replacement="$1ോ"/>
  <pattern input="ൺO" replacement="ണോ"/>
  <pattern input="ൻO" replacement="നോ"/>
  <pattern input="ർO" replacement="രോ"/>
  <pattern input="ൽO" replacement="ലോ"/>
  <pattern input="ൾO" replacement="ളോ"/>
  <pattern input="ൿO" replacement="കോ"/>
  <pattern input="([ക-ഹ])u" replacement="$1ൗ"/>
  <pattern input="([ക-ഹ])U" replacement="$1ൌ"/>
  <pattern input="([അ-്])m" replacement="$1ം"/>
  <pattern input="\\0" replacement="൦"/>
  <pattern input="\\1" replacement="൧"/>
  <pattern input="\\2" replacement="൨"/>
  <pattern input="\\3" replacement="൩"/>
  <pattern input="\\4" replacement="൪"/>
  <pattern input="\\5" replacement="൫"/>
  <pattern input="\\6" replacement="൬"/>
  <pattern input="\\7" replacement="൭"/>
  <pattern input="\\8" replacement="൮"/>
  <pattern input="\\9" replacement="൯"/>
  <pattern input="മ്\\" replacement="ം"/>
  <pattern input="ആ\\" replacement="ാ"/>
  <pattern input="ഇ\\" replacement="ി"/>
  <pattern input="ഈ\\" replacement="ീ"/>
  <pattern input="ഉ\\" replacement="ു"/>
  <pattern input="ഊ\\" replacement="ൂ"/>
  <pattern input="ഋ\\" replacement="ൃ"/>
  <pattern input="ൠ\\" replacement="ൄ"/>
  <pattern input="എ\\" replacement="െ"/>
  <pattern input="ഏ\\" replacement="േ"/>
  <pattern input="ഐ\\" replacement="ൈ"/>
  <pattern input="ഒ\\" replacement="ൊ"/>
  <pattern input="ഓ\\" replacement="ോ"/>
  <pattern input="ഔ\\" replacement="ൗ"/>
  <pattern input="ൗ\\" replacement="ൌ"/>
  <pattern input="ഌ\\" replacement="ൢ"/>
  <pattern input="ൡ\\" replacement="ൣ"/>
  <pattern input="(([ൺ-ൿം])‌+)?A" replacement="$2ആ"/>
  <pattern input="(([ൺ-ൿം])‌+)?B" replacement="$2ബ്ബ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?C" replacement="$2ച്ച്"/>
  <pattern input="(([ൺ-ൿം])‌+)?D" replacement="$2ഡ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?E" replacement="$2ഏ"/>
  <pattern input="(([ൺ-ൿം])‌+)?F" replacement="$2ഫ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?G" replacement="$2ഗ്ഗ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?H" replacement="$2ഃ"/>
  <pattern input="(([ൺ-ൿം])‌+)?I" replacement="$2ഐ"/>
  <pattern input="(([ൺ-ൿം])‌+)?J" replacement="$2ജ്ജ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?K" replacement="$2ക്ക്"/>
  <pattern input="(([ൺ-ൿം])‌+)?L" replacement="$2ൾ"/>
  <pattern input="(([ൺ-ൿം])‌+)?M" replacement="$2മ്മ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?N" replacement="$2ൺ"/>
  <pattern input="(([ൺ-ൿം])‌+)?O" replacement="$2ഓ"/>
  <pattern input="(([ൺ-ൿം])‌+)?P" replacement="$2പ്പ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?Q" replacement="$2ക്യ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?R" replacement="$2ഋ"/>
  <pattern input="(([ൺ-ൿം])‌+)?(S|z)" replacement="$2ശ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?T" replacement="$2ട്"/>
  <pattern input="(([ൺ-ൿം])‌+)?U" replacement="$2ഊ"/>
  <pattern input="(([ൺ-ൿം])‌+)?(V|W)" replacement="$2വ്വ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?X" replacement="$2ക്ഷ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?Y" replacement="$2യ്യ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?Z" replacement="$2ശ്ശ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?a" replacement="$2അ"/>
  <pattern input="(([ൺ-ൿം])‌+)?b" replacement="$2ബ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?c" replacement="$2ൿ"/>
  <pattern input="(([ൺ-ൿം])‌+)?d" replacement="$2ദ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?e" replacement="$2എ"/>
  <pattern input="(([ൺ-ൿം])‌+)?f" replacement="$2ഫ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?g" replacement="$2ഗ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?h" replacement="$2ഹ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?i" replacement="$2ഇ"/>
  <pattern input="(([ൺ-ൿം])‌+)?j" replacement="$2ജ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?k" replacement="$2ക്"/>
  <pattern input="(([ൺ-ൿം])‌+)?l" replacement="$2ൽ"/>
  <pattern input="(([ൺ-ൿം])‌+)?m" replacement="$2മ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?n" replacement="$2ൻ"/>
  <pattern input="(([ൺ-ൿം])‌+)?o" replacement="$2ഒ"/>
  <pattern input="(([ൺ-ൿം])‌+)?p" replacement="$2പ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?q" replacement="$2ക്ക്"/>
  <pattern input="(([ൺ-ൿം])‌+)?r" replacement="$2ർ"/>
  <pattern input="(([ൺ-ൿം])‌+)?s" replacement="$2സ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?t" replacement="$2റ്റ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?u" replacement="$2ഉ"/>
  <pattern input="(([ൺ-ൿം])‌+)?(v|w)" replacement="$2വ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?x" replacement="$2ക്ഷ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?y" replacement="$2യ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?~" replacement="$2്"/>
  <pattern input="(‌)*_" replacement="‌"/>
  <pattern input="//" replacement="ഽ"/>
  <pattern input="1/4\\" replacement="൳"/>
  <pattern input="1/2\\" replacement="൴"/>
  <pattern input="3/4\\" replacement="൵"/>
  <pattern input="10\\" replacement="൰"/>
  <pattern input="100\\" replacement="൱"/>
  <pattern input="1000\\" replacement="൲"/>
  <pattern input="\-\-\-\\" replacement="—"/>
  <pattern input="\-\-\\" replacement="–"/>
  <pattern input="\\\-" replacement="−"/>
  <pattern input="\\\*" replacement="×"/>
  <pattern input="\\/" replacement="÷"/>
  <pattern input="1#" replacement="൧"/>
  <pattern input="2#" replacement="൨"/>
  <pattern input="3#" replacement="൩"/>
  <pattern input="4#" replacement="൪"/>
  <pattern input="5#" replacement="൫"/>
  <pattern input="6#" replacement="൬"/>
  <pattern input="7#" replacement="൭"/>
  <pattern input="8#" replacement="൮"/>
  <pattern input="9#" replacement="൯"/>
  <pattern input="0#" replacement="൦"/>
</inputmethod>
