<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <include latin:keyboardLayout="@xml/key_styles_common" />
    <Row
        latin:keyWidth="8.182%p"
        latin:keyLabelFlags="fontNormal"
    >
        <include latin:keyboardLayout="@xml/rowkeys_bengali1" />
        <Key
            latin:keyStyle="deleteKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
    <Row
        latin:keyWidth="8.182%p"
        latin:keyLabelFlags="fontNormal"
    >
        <include latin:keyboardLayout="@xml/rowkeys_bengali2" />
        <Key
            latin:keyStyle="enterKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
    <Row
        latin:keyWidth="8.182%p"
        latin:keyLabelFlags="fontNormal"
    >
        <include latin:keyboardLayout="@xml/rowkeys_bengali3" />
        <include latin:keyboardLayout="@xml/keys_exclamation_question" />
    </Row>
    <include latin:keyboardLayout="@xml/row_qwerty4" />
</merge>
