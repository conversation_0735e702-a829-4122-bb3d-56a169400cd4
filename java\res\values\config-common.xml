<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources>
    <bool name="config_block_potentially_offensive">true</bool>
    <!-- Default value for next word prediction: after entering a word and a space only, should we look
         at input history to suggest a hopefully helpful suggestions for the next word? -->
    <bool name="config_default_next_word_prediction">true</bool>

    <integer name="config_delay_in_milliseconds_to_update_shift_state">100</integer>
    <integer name="config_double_space_period_timeout">1100</integer>

    <integer name="config_key_repeat_start_timeout">400</integer>
    <integer name="config_key_repeat_interval">50</integer>

    <integer name="config_ignore_alt_code_key_timeout">350</integer>

    <integer name="config_key_preview_show_up_duration">17</integer>
    <integer name="config_key_preview_dismiss_duration">53</integer>
    <fraction name="config_key_preview_show_up_start_scale">98%</fraction>
    <fraction name="config_key_preview_dismiss_end_scale">94%</fraction>
    <!-- TODO: consolidate key preview linger timeout with the above animation parameters. -->
    <integer name="config_key_preview_linger_timeout">70</integer>
    <!-- Suppress showing key preview duration after batch input in millisecond -->
    <integer name="config_suppress_key_preview_after_batch_input_duration">1000</integer>

    <bool name="config_default_vibration_enabled">true</bool>
    <integer name="config_max_vibration_duration">100</integer>

    <integer name="config_default_longpress_key_timeout">300</integer>
    <integer name="config_max_longpress_timeout">700</integer>
    <integer name="config_min_longpress_timeout">100</integer>
    <integer name="config_longpress_timeout_step">10</integer>
    <integer name="config_accessibility_long_press_key_timeout">3000</integer>
    <integer name="config_max_more_keys_column">5</integer>
    <integer name="config_more_keys_keyboard_fadein_anim_time">0</integer>
    <integer name="config_more_keys_keyboard_fadeout_anim_time">100</integer>

    <!-- Long pressing shift will invoke caps-lock if > 0, never invoke caps-lock if == 0 -->
    <integer name="config_longpress_shift_lock_timeout">1200</integer>

    <!-- Sliding key input preview parameters -->
    <dimen name="config_sliding_key_input_preview_width">8.0dp</dimen>
    <!-- Percentages of sliding key input preview body and shadow, in proportion to the width.
         A negative value of the shadow ratio disables drawing shadow. -->
    <!-- TODO: May use the shadow to alleviate rugged trail drawing. -->
    <integer name="config_sliding_key_input_preview_body_ratio">100</integer>
    <integer name="config_sliding_key_input_preview_shadow_ratio">-1</integer>
    <dimen name="config_key_hysteresis_distance_for_sliding_modifier">8.0dp</dimen>

    <integer name="config_language_on_spacebar_final_alpha">128</integer>
    <dimen name="config_language_on_spacebar_horizontal_margin">1dp</dimen>

    <integer name="config_gesture_floating_preview_text_linger_timeout">200</integer>
    <integer name="config_gesture_trail_fadeout_start_delay">100</integer>
    <integer name="config_gesture_trail_fadeout_duration">800</integer>
    <integer name="config_gesture_trail_update_interval">20</integer>
    <!-- Static threshold for gesture after fast typing (msec) -->
    <integer name="config_gesture_static_time_threshold_after_fast_typing">500</integer>
    <!-- Static threshold for starting gesture detection (keyWidth%/sec) -->
    <fraction name="config_gesture_detect_fast_move_speed_threshold">150%</fraction>
    <!-- Dynamic threshold for gesture after fast typing (msec) -->
    <integer name="config_gesture_dynamic_threshold_decay_duration">450</integer>
    <!-- Time based threshold values for gesture detection (msec) -->
    <integer name="config_gesture_dynamic_time_threshold_from">300</integer>
    <integer name="config_gesture_dynamic_time_threshold_to">20</integer>
    <!-- Distance based threshold values for gesture detection (keyWidth%/sec) -->
    <fraction name="config_gesture_dynamic_distance_threshold_from">600%</fraction>
    <fraction name="config_gesture_dynamic_distance_threshold_to">50%</fraction>
    <!-- Parameter for gesture sampling (keyWidth%/sec) -->
    <fraction name="config_gesture_sampling_minimum_distance">16.6666%</fraction>
    <!-- Parameters for gesture recognition (msec) and (keyWidth%/sec) -->
    <integer name="config_gesture_recognition_minimum_time">100</integer>
    <integer name="config_gesture_recognition_update_time">100</integer>
    <fraction name="config_gesture_recognition_speed_threshold">550%</fraction>

    <integer name="config_keyboard_grid_width">32</integer>
    <integer name="config_keyboard_grid_height">16</integer>
    <dimen name="config_touch_noise_threshold_distance">12.6dp</dimen>
    <integer name="config_touch_noise_threshold_time">40</integer>

    <!-- Common keyboard configuration. -->
    <fraction name="config_keyboard_left_padding">0%p</fraction>
    <fraction name="config_keyboard_right_padding">0%p</fraction>
    <dimen name="config_keyboard_vertical_correction">0.0dp</dimen>

    <!-- Common key top visual configuration. -->
    <dimen name="config_key_popup_hint_letter_padding">2dp</dimen>

    <!-- Common suggestion strip configuration. -->
    <integer name="config_suggestions_count_in_strip">3</integer>
    <fraction name="config_center_suggestion_percentile">36%</fraction>
    <integer name="config_delay_in_milliseconds_to_update_suggestions">100</integer>
    <integer name="config_delay_in_milliseconds_to_update_old_suggestions">300</integer>

    <!-- Common more suggestions configuraion. -->
    <dimen name="config_more_suggestions_key_horizontal_padding">12dp</dimen>
    <dimen name="config_more_suggestions_bottom_gap">6dp</dimen>
    <dimen name="config_more_suggestions_modal_tolerance">32.0dp</dimen>
    <fraction name="config_more_suggestions_info_ratio">18%</fraction>

    <!-- Common gesture trail parameters -->
    <!-- Minimum distance between gesture trail sampling points. -->
    <dimen name="config_gesture_trail_min_sampling_distance">9.6dp</dimen>
    <!-- Maximum angular threshold between gesture trails interpolation segments in degree. -->
    <integer name="config_gesture_trail_max_interpolation_angular_threshold">15</integer>
    <!-- Maximum distance threshold between gesture trails interpolation segments. -->
    <dimen name="config_gesture_trail_max_interpolation_distance_threshold">16.0dp</dimen>
    <!-- Maximum number of gesture trail interpolation segments. -->
    <integer name="config_gesture_trail_max_interpolation_segments">6</integer>
    <dimen name="config_gesture_trail_start_width">10.0dp</dimen>
    <dimen name="config_gesture_trail_end_width">2.5dp</dimen>
    <!-- Percentages of gesture preview taril body and shadow, in proportion to the trail width.
         A negative value of the shadow ratio disables drawing shadow. -->
    <!-- TODO: May use the shadow to alleviate rugged trail drawing. -->
    <integer name="config_gesture_trail_body_ratio">100</integer>
    <integer name="config_gesture_trail_shadow_ratio">-1</integer>

    <!-- Common configuration of Emoji keyboard -->
    <dimen name="config_emoji_category_page_id_height">2dp</dimen>

    <!-- Inset used in Accessibility mode to avoid accidental key presses when a finger slides off the screen. -->
    <dimen name="config_accessibility_edge_slop">8dp</dimen>

    <integer name="config_user_dictionary_max_word_length">48</integer>

    <!-- Personalization configuration -->
    <!-- -1 means periocical wipe of the personalization dict is disabled. -->
    <integer name="config_personalization_dict_wipe_interval_in_days">-1</integer>
</resources>
