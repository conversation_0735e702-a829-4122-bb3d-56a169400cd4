#include "SMCTransliterator.h"
#include <iostream>

/**
 * Example usage of SMCTransliterator - Single File Integration
 * 
 * This shows how to integrate the Tamil transliteration engine
 * into your existing project with just one header file!
 */

int main() {
    std::cout << "🎯 SMC Tamil Transliteration Engine - Single File Integration" << std::endl;
    std::cout << "=============================================================" << std::endl;
    
    // Create transliterator instance
    SMCTransliterator transliterator;
    
    // Show stats
    auto stats = transliterator.getStats();
    std::cout << "📊 Engine loaded:" << std::endl;
    std::cout << "   Patterns: " << stats.totalPatterns << std::endl;
    std::cout << "   Dictionary: " << stats.dictionarySize << " words" << std::endl;
    std::cout << std::endl;
    
    // Test words
    std::vector<std::string> testWords = {
        "vanakkam", "nandri", "amma", "appa", "tamil",
        "computer", "school", "krishna", "chennai",
        "thiruvananthapuram", "hello", "world"
    };
    
    std::cout << "🧪 Test Results:" << std::endl;
    std::cout << "----------------" << std::endl;
    
    for (const auto& word : testWords) {
        // SIMPLE API - Just one method call!
        std::string result = transliterator.transliterate(word);
        
        std::cout << word << " -> " << result << std::endl;
        
        // Optional: Get suggestions
        auto suggestions = transliterator.getSuggestions(word, 2);
        if (suggestions.size() > 1) {
            std::cout << "   Alternatives: ";
            for (size_t i = 1; i < suggestions.size(); ++i) {
                std::cout << suggestions[i];
                if (i < suggestions.size() - 1) std::cout << ", ";
            }
            std::cout << std::endl;
        }
    }
    
    std::cout << std::endl;
    std::cout << "✅ Integration complete! Just include SMCTransliterator.h in your project." << std::endl;
    
    return 0;
}

/*
INTEGRATION INSTRUCTIONS:
========================

1. Copy SMCTransliterator.h to your project
2. Include it: #include "SMCTransliterator.h"
3. Use it:

   SMCTransliterator transliterator;
   std::string tamil = transliterator.transliterate("vanakkam");
   // Result: "வணக்கம்"

That's it! No external dependencies, no complex setup.

SIMPLE API METHODS:
==================

// Basic transliteration
std::string transliterate(const std::string& input);

// Get multiple suggestions  
std::vector<std::string> getSuggestions(const std::string& input, int maxSuggestions = 3);

// Load external dictionary (optional)
bool loadDictionary(const std::string& filename);

// Clear cache for memory management
void clearCache();

// Get engine statistics
Stats getStats();

FEATURES INCLUDED:
=================
✅ 92 real SMC transliteration patterns
✅ Built-in Tamil dictionary
✅ Smart caching for performance
✅ Thread-safe design
✅ No external dependencies
✅ Single header file
✅ Easy integration

EXAMPLE INTEGRATION IN YOUR PROJECT:
===================================

#include "SMCTransliterator.h"

class YourClass {
private:
    SMCTransliterator tamilEngine;
    
public:
    std::string convertToTamil(const std::string& englishText) {
        return tamilEngine.transliterate(englishText);
    }
    
    std::vector<std::string> getTamilSuggestions(const std::string& englishText) {
        return tamilEngine.getSuggestions(englishText, 5);
    }
};

*/
