<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Holo KeyboardView theme (ICS and KLP) -->
    <style
        name="KeyboardView.Holo"
        parent="KeyboardView"
    >
        <item name="keyTypeface">bold</item>
        <item name="keyLetterSize">@fraction/config_key_letter_ratio_holo</item>
        <item name="keyLabelSize">@fraction/config_key_label_ratio_holo</item>
        <item name="keyHintLetterRatio">@fraction/config_key_hint_letter_ratio_holo</item>
        <item name="keyShiftedLetterHintRatio">@fraction/config_key_shifted_letter_hint_ratio_holo</item>
        <item name="keyLargeLetterRatio">@fraction/config_key_large_letter_ratio_holo</item>
        <item name="keyLabelOffCenterRatio">-175%</item>
        <item name="keyHintLabelRatio">@fraction/config_key_hint_label_ratio_holo</item>
        <item name="keyHintLabelOffCenterRatio">200%</item>
        <!-- U+2026: "…" HORIZONTAL ELLIPSIS -->
        <item name="keyPopupHintLetter">&#x2026;</item>
    </style>
</resources>
