<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2010 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<PreferenceScreen
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:latin="http://schemas.android.com/apk/res-auto"
    android:title="@string/prefs_debug_mode"
    android:key="english_ime_debug_settings"
>
    <CheckBoxPreference
        android:key="debug_mode"
        android:title="@string/prefs_debug_mode"
        android:defaultValue="false"
        android:persistent="true" />
    <CheckBoxPreference
        android:key="force_non_distinct_multitouch"
        android:title="@string/prefs_force_non_distinct_multitouch"
        android:defaultValue="false"
        android:persistent="true" />
    <CheckBoxPreference
        android:key="force_physical_keyboard_special_key"
        android:title="@string/prefs_force_physical_keyboard_special_key"
        android:defaultValue="false"
        android:persistent="true" />
    <CheckBoxPreference
        android:key="pref_show_ui_to_accept_typed_word"
        android:title="@string/prefs_show_ui_to_accept_typed_word"
        android:defaultValue="true"
        android:persistent="true" />
    <CheckBoxPreference
        android:key="pref_sliding_key_input_preview"
        android:title="@string/sliding_key_input_preview"
        android:summary="@string/sliding_key_input_preview_summary"
        android:defaultValue="true"
        android:persistent="true" />
    <in.androidtweak.inputmethod.indic.settings.SeekBarDialogPreference
        android:key="pref_key_longpress_timeout"
        android:title="@string/prefs_key_longpress_timeout_settings"
        latin:minValue="@integer/config_min_longpress_timeout"
        latin:maxValue="@integer/config_max_longpress_timeout"
        latin:stepValue="@integer/config_longpress_timeout_step" />
    <CheckBoxPreference
        android:key="pref_has_custom_key_preview_animation_params"
        android:title="@string/prefs_customize_key_preview_animation"
        android:defaultValue="false"
        android:persistent="true" />
    <in.androidtweak.inputmethod.indic.settings.SeekBarDialogPreference
        android:dependency="pref_has_custom_key_preview_animation_params"
        android:key="pref_key_preview_show_up_start_x_scale"
        android:title="@string/prefs_key_popup_show_up_start_x_scale_settings"
        latin:maxValue="100" /> <!-- percent -->
    <in.androidtweak.inputmethod.indic.settings.SeekBarDialogPreference
        android:dependency="pref_has_custom_key_preview_animation_params"
        android:key="pref_key_preview_show_up_start_y_scale"
        android:title="@string/prefs_key_popup_show_up_start_y_scale_settings"
        latin:maxValue="100" /> <!-- percent -->
    <in.androidtweak.inputmethod.indic.settings.SeekBarDialogPreference
        android:dependency="pref_has_custom_key_preview_animation_params"
        android:key="pref_key_preview_dismiss_end_x_scale"
        android:title="@string/prefs_key_popup_dismiss_end_x_scale_settings"
        latin:maxValue="100" /> <!-- percent -->
    <in.androidtweak.inputmethod.indic.settings.SeekBarDialogPreference
        android:dependency="pref_has_custom_key_preview_animation_params"
        android:key="pref_key_preview_dismiss_end_y_scale"
        android:title="@string/prefs_key_popup_dismiss_end_y_scale_settings"
        latin:maxValue="100" /> <!-- percent -->
    <in.androidtweak.inputmethod.indic.settings.SeekBarDialogPreference
        android:dependency="pref_has_custom_key_preview_animation_params"
        android:key="pref_key_preview_show_up_duration"
        android:title="@string/prefs_key_popup_show_up_duration_settings"
        latin:maxValue="100" /> <!-- milliseconds -->
    <in.androidtweak.inputmethod.indic.settings.SeekBarDialogPreference
        android:dependency="pref_has_custom_key_preview_animation_params"
        android:key="pref_key_preview_dismiss_duration"
        android:title="@string/prefs_key_popup_dismiss_duration_settings"
        latin:maxValue="100" /> <!-- milliseconds -->
    <PreferenceScreen
        android:key="read_external_dictionary"
        android:title="@string/prefs_read_external_dictionary" />
    <PreferenceCategory
        android:key="pref_key_dump_dictionaries"
        android:title="@string/prefs_dump_dynamic_dicts">
    </PreferenceCategory>
</PreferenceScreen>
