<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted">
            <!-- U+0914: "औ" DEVANAGARI LETTER AU
                 U+0912/U+0902: "ऒं" DEVANAGARI LETTER SHORT O//DEVANAGARI SIGN ANUSVARA -->
            <Key
                latin:keySpec="&#x0914;"
                latin:moreKeys="&#x0912;&#x0902;" />
            <!-- U+0910: "ऐ" DEVANAGARI LETTER AI
                 U+0910/U+0902: "ऐं" DEVANAGARI LETTER AI/DEVANAGARI SIGN ANUSVARA -->
            <Key
                latin:keySpec="&#x0910;"
                latin:moreKeys="&#x0910;&#x0902;" />
            <!-- U+0906: "आ" DEVANAGARI LETTER AA
                 U+0906/U+0902: "आं" DEVANAGARI LETTER AA/DEVANAGARI SIGN ANUSVARA
                 U+0906/U+0901: "आँ" DEVANAGARI LETTER AA/DEVANAGARI SIGN CANDRABINDU -->
            <Key
                latin:keySpec="&#x0906;"
                latin:moreKeys="&#x0906;&#x0902;,&#x0906;&#x0901;" />
            <!-- U+0908: "ई" DEVANAGARI LETTER II
                 U+0908/U+0902: "ईं" DEVANAGARI LETTER II/DEVANAGARI SIGN ANUSVARA -->
            <Key
                latin:keySpec="&#x0908;"
                latin:moreKeys="&#x0908;&#x0902;" />
            <!-- U+090A: "ऊ" DEVANAGARI LETTER UU
                 U+090A/U+0902: "ऊं" DEVANAGARI LETTER UU/DEVANAGARI SIGN ANUSVARA
                 U+090A/U+0901: "ऊँ" DEVANAGARI LETTER UU/DEVANAGARI SIGN CANDRABINDU -->
            <Key
                latin:keySpec="&#x090A;"
                latin:moreKeys="&#x090A;&#x0902;,&#x090A;&#x0901;" />
            <!-- U+092D: "भ" DEVANAGARI LETTER BHA -->
            <Key latin:keySpec="&#x092D;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_visarga" />
            <Key latin:keyStyle="baseKeyDevanagariSignVisarga" />
            <!-- U+0918: "घ" DEVANAGARI LETTER GHA -->
            <Key latin:keySpec="&#x0918;" />
            <!-- U+0927: "ध" DEVANAGARI LETTER DHA
                 U+0915/U+094D/U+0937: "क्ष" DEVANAGARI LETTER KA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER SSA
                 U+0936/U+094D/U+0930: "श्र" DEVANAGARI LETTER SHA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER RA -->
            <Key
                latin:keySpec="&#x0927;"
                latin:moreKeys="&#x0915;&#x094D;&#x0937;,&#x0936;&#x094D;&#x0930;" />
            <!-- U+091D: "झ" DEVANAGARI LETTER JHA -->
            <Key latin:keySpec="&#x091D;" />
            <!-- U+0922: "ढ" DEVANAGARI LETTER DDHA -->
            <Key latin:keySpec="&#x0922;" />
        </case>
        <default>
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_au" />
            <!-- U+0967: "१" DEVANAGARI DIGIT ONE -->
            <Key
                latin:keyStyle="baseKeyDevanagariVowelSignAu"
                latin:keyHintLabel="1"
                latin:additionalMoreKeys="&#x0967;,1" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_ai" />
            <!-- U+0968: "२" DEVANAGARI DIGIT TWO -->
            <Key
                latin:keyStyle="baseKeyDevanagariVowelSignAi"
                latin:keyHintLabel="2"
                latin:additionalMoreKeys="&#x0968;,2" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_aa" />
            <!-- U+0969: "३" DEVANAGARI DIGIT THREE -->
            <Key
                latin:keyStyle="baseKeyDevanagariVowelSignAa"
                latin:keyHintLabel="3"
                latin:additionalMoreKeys="&#x0969;,3" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_ii" />
            <!-- U+096A: "४" DEVANAGARI DIGIT FOUR -->
            <Key
                latin:keyStyle="baseKeyDevanagariVowelSignIi"
                latin:keyHintLabel="4"
                latin:additionalMoreKeys="&#x096A;,4" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_uu" />
            <!-- U+096B: "५" DEVANAGARI DIGIT FIVE -->
            <Key
                latin:keyStyle="baseKeyDevanagariVowelSignUu"
                latin:keyHintLabel="5"
                latin:additionalMoreKeys="&#x096B;,5" />
            <!-- U+092C: "ब" DEVANAGARI LETTER BA
                 U+092C/U+0952: "ब॒" DEVANAGARI LETTER BA/DEVANAGARI STRESS SIGN ANUDATTA
                 U+096C: "६" DEVANAGARI DIGIT SIX -->
            <Key
                latin:keySpec="&#x092C;"
                latin:moreKeys="&#x092C;&#x0952;,%"
                latin:keyHintLabel="6"
                latin:additionalMoreKeys="&#x096C;,6" />
            <!-- U+0939: "ह" DEVANAGARI LETTER HA
                 U+096D: "७" DEVANAGARI DIGIT SEVEN -->
            <Key
                latin:keySpec="&#x0939;"
                latin:keyHintLabel="7"
                latin:additionalMoreKeys="&#x096D;,7" />
            <!-- U+0917: "ग" DEVANAGARI LETTER GA
                 U+091C/U+094D/U+091E: "ज्ञ" DEVANAGARI LETTER JA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER NYA
                 U+0917/U+093C: "ग़" DEVANAGARI LETTER GA/DEVANAGARI SIGN NUKTA
                 U+0917/U+0952: "ग॒" DEVANAGARI LETTER GA/DEVANAGARI STRESS SIGN ANUDATTA
                 U+096E: "८" DEVANAGARI DIGIT EIGHT -->
            <Key
                latin:keySpec="&#x0917;"
                latin:moreKeys="&#x091C;&#x094D;&#x091E;,&#x0917;&#x093C;,&#x0917;&#x0952;,%"
                latin:keyHintLabel="8"
                latin:additionalMoreKeys="&#x096E;,8" />
            <!-- U+0926: "द" DEVANAGARI LETTER DA
                 U+096F: "९" DEVANAGARI DIGIT NINE -->
            <Key
                latin:keySpec="&#x0926;"
                latin:keyHintLabel="9"
                latin:additionalMoreKeys="&#x096F;,9" />
            <!-- U+091C: "ज" DEVANAGARI LETTER JA
                 U+091C/U+0952: "ज॒" DEVANAGARI LETTER JA/DEVANAGARI STRESS SIGN ANUDATTA
                 U+091C/U+094D/U+091E: "ज्ञ" DEVANAGARI LETTER JA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER NYA
                 U+091C/U+093C: "ज़" DEVANAGARI LETTER JA/DEVANAGARI SIGN NUKTA
                 U+0966: "०" DEVANAGARI DIGIT ZERO -->
            <Key
                latin:keySpec="&#x091C;"
                latin:moreKeys="&#x091C;&#x0952;,&#x091C;&#x094D;&#x091E;,&#x091C;&#x093C;,%"
                latin:keyHintLabel="0"
                latin:additionalMoreKeys="&#x0966;,0" />
            <!-- U+0921: "ड" DEVANAGARI LETTER DDA
                 U+0921/U+0952: "ड॒" DEVANAGARI LETTER DDA/DEVANAGARI STRESS SIGN ANUDATTA
                 U+0921/U+093C: "ड़" DEVANAGARI LETTER DDA/DEVANAGARI SIGN NUKTA -->
            <Key
                latin:keySpec="&#x0921;"
                latin:moreKeys="&#x0921;&#x0952;,&#x0921;&#x093C;" />
        </default>
    </switch>
</merge>
