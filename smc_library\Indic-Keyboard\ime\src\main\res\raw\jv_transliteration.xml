<?xml version="1.0"?>
<inputmethod id="jv-transliteration" name="Javanese" description="Javanese transliteration" author="Bennylin" version="1.0" contextLength="1" maxKeyLength="2">
  <pattern input="\\([A-Za-z\&gt;_~\.0-9])" context="\\" replacement="$1"/>
  <pattern input="꧀ꦃa" context="" replacement="꧀​ꦲ"/>
  <pattern input="꧀ꦃe" context="" replacement="꧀​ꦲꦺ"/>
  <pattern input="꧀ꦃi" context="" replacement="꧀​ꦲꦶ"/>
  <pattern input="꧀ꦃo" context="" replacement="꧀​ꦲꦺꦴ"/>
  <pattern input="꧀ꦃu" context="" replacement="꧀​ꦲꦸ"/>
  <pattern input="꧀ꦂa" context="" replacement="꧀​ꦫ"/>
  <pattern input="꧀ꦂe" context="" replacement="꧀​ꦫꦺ"/>
  <pattern input="꧀ꦂi" context="" replacement="꧀​ꦫꦶ"/>
  <pattern input="꧀ꦂo" context="" replacement="꧀​ꦫꦺꦴ"/>
  <pattern input="꧀ꦂu" context="" replacement="꧀​ꦫꦸ"/>
  <pattern input="꧀ꦁa" context="" replacement="꧀​ꦔ"/>
  <pattern input="꧀ꦁe" context="" replacement="꧀​ꦔꦺ"/>
  <pattern input="꧀ꦁi" context="" replacement="꧀​ꦔꦶ"/>
  <pattern input="꧀ꦁo" context="" replacement="꧀​ꦔꦺꦴ"/>
  <pattern input="꧀ꦁu" context="" replacement="꧀​ꦔꦸ"/>
  <pattern input="ꦃa" context="" replacement="ꦲ​"/>
  <pattern input="ꦃe" context="" replacement="ꦲꦺ"/>
  <pattern input="ꦃi" context="" replacement="ꦲꦶ"/>
  <pattern input="ꦃo" context="" replacement="ꦲꦺꦴ"/>
  <pattern input="ꦃu" context="" replacement="ꦲꦸ"/>
  <pattern input="ꦂa" context="" replacement="ꦫ​"/>
  <pattern input="ꦂe" context="" replacement="ꦫꦺ"/>
  <pattern input="ꦂi" context="" replacement="ꦫꦶ"/>
  <pattern input="ꦂo" context="" replacement="ꦫꦺꦴ"/>
  <pattern input="ꦂu" context="" replacement="ꦫꦸ"/>
  <pattern input="ꦂy" context="" replacement="ꦫꦾ"/>
  <pattern input="ꦁa" context="" replacement="ꦔ​"/>
  <pattern input="ꦁe" context="" replacement="ꦔꦺ"/>
  <pattern input="ꦁi" context="" replacement="ꦔꦶ"/>
  <pattern input="ꦁo" context="" replacement="ꦔꦺꦴ"/>
  <pattern input="ꦁu" context="" replacement="ꦔꦸ"/>
  <pattern input="(​)h" context="" replacement="ꦃ"/>
  <pattern input="(​)r" context="" replacement="ꦂ"/>
  <pattern input="(​)ꦤg" context="" replacement="ꦁ"/>
  <pattern input="(ꦴ|ꦍ|ꦺ|ꦼ|ꦶ|ꦷ|ꦸ|ꦹ)h" context="" replacement="$1ꦃ"/>
  <pattern input="(ꦴ|ꦍ|ꦺ|ꦼ|ꦶ|ꦷ|ꦸ|ꦹ)r" context="" replacement="$1ꦂ"/>
  <pattern input="(ꦴ|ꦍ|ꦺ|ꦼ|ꦶ|ꦷ|ꦸ|ꦹ)ꦤg" context="" replacement="$1ꦁ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)ꦢh" context="" replacement="$1꧀ꦝ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)ꦤy" context="" replacement="$1꧀ꦚ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)ꦠh" context="" replacement="$1꧀ꦛ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)ꦤg" context="" replacement="$1꧀ꦔ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)b" context="" replacement="$1꧀ꦧ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)c" context="" replacement="$1꧀ꦤ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)d" context="" replacement="$1꧀ꦢ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)(f|v)" context="" replacement="$1꧀ꦥ꦳"/>
  <pattern input="(ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)g" context="" replacement="$1꧀ꦒ"/>
  <pattern input="(ꦤ|ꦕ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦧ)h" context="" replacement="$1꧀ꦲ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)j" context="" replacement="$1꧀ꦗ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)k" context="" replacement="$1꧀ꦏ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)l" context="" replacement="$1꧀ꦭ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)m" context="" replacement="$1꧀ꦩ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)n" context="" replacement="$1꧀ꦤ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)p" context="" replacement="$1꧀ꦥ"/>
  <pattern input="ꦿꦺ`" context="" replacement="ꦽ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)r" context="" replacement="$1ꦿ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)s" context="" replacement="$1꧀ꦱ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)t" context="" replacement="$1꧀ꦠ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)w" context="" replacement="$1꧀ꦮ"/>
  <pattern input="(ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦩ|ꦒ|ꦧ)y" context="" replacement="$1ꦾ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)z" context="" replacement="$1ꦾꦗ꦳"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)ꦤ(y|Y)" context="" replacement="$1꧀ꦘ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)B" context="" replacement="$1꧀ꦨ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)C" context="" replacement="$1꧀ꦖ"/>
  <pattern input="(ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)G" context="" replacement="$1꧀ꦓ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)K" context="" replacement="$1꧀ꦑ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)N" context="" replacement="$1꧀ꦟ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)P" context="" replacement="$1꧀ꦦ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)S" context="" replacement="$1꧀ꦯ"/>
  <pattern input="(ꦤ|ꦕ|ꦏ|ꦢ|ꦠ|ꦱ|ꦮ|ꦭ|ꦥ|ꦗ|ꦪ|ꦩ|ꦒ|ꦧ)T" context="" replacement="$1꧀ꦡ"/>
  <pattern input="​a" context="" replacement="ꦴ"/>
  <pattern input="​i" context="" replacement="ꦍ"/>
  <pattern input="ꦶi" context="" replacement="ꦷ"/>
  <pattern input="ꦸu" context="" replacement="ꦹ"/>
  <pattern input="ꦏh" context="" replacement="ꦏ꦳"/>
  <pattern input="ꦒh" context="" replacement="ꦒ꦳"/>
  <pattern input="ꦢz" context="" replacement="ꦢ꦳"/>
  <pattern input="ꦗ`" context="" replacement="ꦙ"/>
  <pattern input="ꦭꦺ`" context="" replacement="ꦊ"/>
  <pattern input="ꦫꦺ`" context="" replacement="ꦉ"/>
  <pattern input=" " context="" replacement="​"/>
  <pattern input="q`" context="" replacement="꧀"/>
  <pattern input="x`" context="" replacement="꦳"/>
  <pattern input="꦳`" context="" replacement="ꦀ"/>
  <pattern input="ꦀ`" context="" replacement="̈"/>
  <pattern input="̈`" context="" replacement="͜"/>
  <pattern input="ꦫ`" context="" replacement="ꦿ"/>
  <pattern input="ꦪ`" context="" replacement="ꦾ"/>
  <pattern input="ꦢh" context="" replacement="ꦝ"/>
  <pattern input="ꦠh" context="" replacement="ꦛ"/>
  <pattern input="ꦤy" context="" replacement="ꦚ"/>
  <pattern input="ꦤg" context="" replacement="ꦔ"/>
  <pattern input="ꦺ`" context="" replacement="ꦼ"/>
  <pattern input="a" context="" replacement="​"/>
  <pattern input="b" context="" replacement="ꦧ"/>
  <pattern input="c" context="" replacement="ꦕ"/>
  <pattern input="d" context="" replacement="ꦢ"/>
  <pattern input="e" context="" replacement="ꦺ"/>
  <pattern input="(f|v)" context="" replacement="ꦥ꦳"/>
  <pattern input="g" context="" replacement="ꦒ"/>
  <pattern input="h" context="" replacement="ꦲ"/>
  <pattern input="i" context="" replacement="ꦶ"/>
  <pattern input="j" context="" replacement="ꦗ"/>
  <pattern input="k" context="" replacement="ꦏ"/>
  <pattern input="l" context="" replacement="ꦭ"/>
  <pattern input="m" context="" replacement="ꦩ"/>
  <pattern input="n" context="" replacement="ꦤ"/>
  <pattern input="o" context="" replacement="ꦺꦴ"/>
  <pattern input="p" context="" replacement="ꦥ"/>
  <pattern input="r" context="" replacement="ꦫ"/>
  <pattern input="s" context="" replacement="ꦱ"/>
  <pattern input="t" context="" replacement="ꦠ"/>
  <pattern input="u" context="" replacement="ꦸ"/>
  <pattern input="w" context="" replacement="ꦮ"/>
  <pattern input="y" context="" replacement="ꦪ"/>
  <pattern input="z" context="" replacement="ꦗ꦳"/>
  <pattern input="ꦢ(h|H)" context="" replacement="ꦝ"/>
  <pattern input="ꦤ(y|Y)" context="" replacement="ꦘ"/>
  <pattern input="ꦠ(h|H)" context="" replacement="ꦛ"/>
  <pattern input="ꦤ(g|G)" context="" replacement="ꦔ"/>
  <pattern input="ꦌ`" context="" replacement="ꦄꦼ"/>
  <pattern input="A" context="" replacement="ꦄ"/>
  <pattern input="B" context="" replacement="ꦨ"/>
  <pattern input="C" context="" replacement="ꦖ"/>
  <pattern input="D" context="" replacement="ꦢ"/>
  <pattern input="E" context="" replacement="ꦌ"/>
  <pattern input="(F|V)" context="" replacement="ꦥ꦳"/>
  <pattern input="G" context="" replacement="ꦓ"/>
  <pattern input="H" context="" replacement="ꦲ"/>
  <pattern input="I" context="" replacement="ꦆ"/>
  <pattern input="J" context="" replacement="ꦗ"/>
  <pattern input="K" context="" replacement="ꦑ"/>
  <pattern input="L" context="" replacement="ꦭ"/>
  <pattern input="M" context="" replacement="ꦩ"/>
  <pattern input="N" context="" replacement="ꦟ"/>
  <pattern input="O" context="" replacement="ꦎ"/>
  <pattern input="P" context="" replacement="ꦦ"/>
  <pattern input="Q" context="" replacement=""/>
  <pattern input="R" context="" replacement="ꦫ"/>
  <pattern input="S" context="" replacement="ꦯ"/>
  <pattern input="T" context="" replacement="ꦡ"/>
  <pattern input="U" context="" replacement="ꦈ"/>
  <pattern input="W" context="" replacement="ꦮ"/>
  <pattern input="X" context="" replacement=""/>
  <pattern input="Y" context="" replacement="ꦪ"/>
  <pattern input="Z" context="" replacement="ꦗ꦳"/>
  <pattern input="0" context="" replacement="꧐"/>
  <pattern input="1" context="" replacement="꧑"/>
  <pattern input="2" context="" replacement="꧒"/>
  <pattern input="3" context="" replacement="꧓"/>
  <pattern input="4" context="" replacement="꧔"/>
  <pattern input="5" context="" replacement="꧕"/>
  <pattern input="6" context="" replacement="꧖"/>
  <pattern input="7" context="" replacement="꧗"/>
  <pattern input="8" context="" replacement="꧘"/>
  <pattern input="9" context="" replacement="꧙"/>
  <pattern input="," context="" replacement="꧈"/>
  <pattern input="\." context="" replacement="꧉"/>
  <pattern input="꧊\|" context="" replacement="꧋"/>
  <pattern input="\|" context="" replacement="꧊"/>
  <pattern input="\(" context="" replacement="꧌"/>
  <pattern input="\)" context="" replacement="꧍"/>
  <pattern input="(‌)*_" context="" replacement="‌"/>
</inputmethod>
