<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        android:id="@+id/setup_step_bullets"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/setup_step_vertical_padding"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/setup_step1_bullet"
            style="@style/setupStepBulletStyle"
            android:text="@string/setup_step1_bullet" />
        <TextView
            android:id="@+id/setup_step2_bullet"
            style="@style/setupStepBulletStyle"
            android:text="@string/setup_step2_bullet" />
        <TextView
            android:id="@+id/setup_step3_bullet"
            style="@style/setupStepBulletStyle"
            android:text="@string/setup_step3_bullet" />
        <TextView
            android:id="@+id/setup_step4_bullet"
            style="@style/setupStepBulletStyle"
            android:text="@string/setup_step4_bullet" />
    </LinearLayout>
    <in.androidtweak.inputmethod.indic.setup.SetupStepIndicatorView
        android:id="@+id/setup_step_indicator"
        style="@style/setupStepIndicatorStyle" />
    <FrameLayout
        android:id="@+id/setup_steps_pane"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <include
            android:id="@+id/setup_step1"
            layout="@layout/setup_step" />
        <include
            android:id="@+id/setup_step2"
            layout="@layout/setup_step" />
        <include
            android:id="@+id/setup_step3"
            layout="@layout/setup_step" />
        <include
            android:id="@+id/setup_step4"
            layout="@layout/setup_step" />
    </FrameLayout>
    <TextView
        android:id="@+id/setup_next"
        android:text="@string/setup_next_action"
        android:gravity="end|center_vertical"
        style="@style/setupStepActionLabelStyleCommon"
        android:layout_marginTop="@dimen/setup_step_horizontal_line_height" />
</merge>
