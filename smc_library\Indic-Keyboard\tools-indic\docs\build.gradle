apply plugin: 'java'
apply plugin: 'kotlin'

task runApp(type: JavaExec) {
    classpath = sourceSets.main.runtimeClasspath
    main = 'org.smc.inputmethod.docs.Main'
}

task debugApp(type: JavaExec) {
    jvmArgs '-Dorg.slf4j.simpleLogger.defaultLogLevel=debug'
    classpath sourceSets.main.runtimeClasspath
    main 'org.smc.inputmethod.docs.Main'
}

repositories {
    mavenCentral()
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation "xmlpull:xmlpull:1.1.3.4a"
    implementation "org.ogce:xpp3:1.1.6"
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation "io.github.microutils:kotlin-logging:1.7.9"
    implementation "org.slf4j:slf4j-simple:1.7.29"
}
buildscript {
    ext.kotlin_version = '1.3.72'
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}
compileKotlin {
    kotlinOptions {
        jvmTarget = "1.8"
    }
}
compileTestKotlin {
    kotlinOptions {
        jvmTarget = "1.8"
    }
}

jar {
    manifest {
        attributes 'Main-Class': 'org.smc.inputmethod.docs.Main'
    }

    from { configurations.compileClasspath.collect { it.isDirectory() ? it : zipTree(it) } }
}