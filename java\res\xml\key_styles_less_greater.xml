<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <!-- The less and greater keys' style which may have different label depending on locale. -->
    <!-- Kept as a separate file for cleaner overriding by an overlay.  -->
    <switch>
        <case
            latin:languageCode="fa"
        >
            <key-style
                latin:styleName="lessKeyStyle"
                latin:keySpec="!text/keyspec_left_double_angle_quote"
                latin:backgroundType="functional"
                latin:moreKeys="!text/morekeys_less_than" />
            <key-style
                latin:styleName="greaterKeyStyle"
                latin:keySpec="!text/keyspec_right_double_angle_quote"
                latin:backgroundType="functional"
                latin:moreKeys="!text/morekeys_greater_than" />
        </case>
        <default>
            <key-style
                latin:styleName="lessKeyStyle"
                latin:keySpec="!text/keyspec_less_than"
                latin:backgroundType="functional"
                latin:moreKeys="!text/morekeys_less_than" />
            <key-style
                latin:styleName="greaterKeyStyle"
                latin:keySpec="!text/keyspec_greater_than"
                latin:backgroundType="functional"
                latin:moreKeys="!text/morekeys_greater_than" />
        </default>
    </switch>
</merge>
