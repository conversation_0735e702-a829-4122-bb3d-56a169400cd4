( function ( $ ) {
	'use strict';

	var hyKbd = {
		id: 'hy-kbd',
		name: 'kbd',
		description: 'Eastern Armenian keyboard layout',
		date: '2013-02-11',
		URL: 'http://github.com/wikimedia/jquery.ime',
		author: '<PERSON><PERSON>',
		license: 'GPLv3',
		version: '1.0',
		patterns: [
			['1', ':'],
			['\\!', '1'],
			['2', 'ձ'],
			['\\@', 'Ձ'],
			['3', 'յ'],
			['\\#', 'Յ'],
			['4', '՛'],
			['\\$', '3'],
			['5', ','],
			['\\%', '4'],
			['6', '-'],
			['\\^', '9'],
			['7', '.'],
			['\\&', 'և'],
			['8', '«'],
			['\\*', '('],
			['9', '»'],
			['\\(', ')'],
			['0', 'օ'],
			['\\)', 'Օ'],
			['\\-', 'ռ'],
			['\\_', 'Ռ'],
			['\\=', 'ժ'],
			['\\+', 'Ժ'],
			['\\`', '՝'],
			['\\~', '՜'],
			['q', 'խ'],
			['Q', 'Խ'],
			['w', 'ւ'],
			['W', 'Ւ'],
			['e', 'է'],
			['E', 'Է'],
			['r', 'ր'],
			['R', 'Ր'],
			['t', 'տ'],
			['T', 'Տ'],
			['y', 'ե'],
			['Y', 'Ե'],
			['u', 'ը'],
			['U', 'Ը'],
			['i', 'ի'],
			['I', 'Ի'],
			['o', 'ո'],
			['O', 'Ո'],
			['p', 'պ'],
			['P', 'Պ'],
			['\\[', 'չ'],
			['\\{', 'Չ'],
			['\\]', 'ջ'],
			['\\}', 'Ջ'],
			['\\', '\''],
			['\\|', '՞'],
			['a', 'ա'],
			['A', 'Ա'],
			['s', 'ս'],
			['S', 'Ս'],
			['d', 'դ'],
			['D', 'Դ'],
			['f', 'ֆ'],
			['F', 'Ֆ'],
			['g', 'ք'],
			['G', 'Ք'],
			['h', 'հ'],
			['H', 'Հ'],
			['j', 'ճ'],
			['J', 'Ճ'],
			['k', 'կ'],
			['K', 'Կ'],
			['l', 'լ'],
			['L', 'Լ'],
			[';', 'թ'],
			[':', 'Թ'],
			['\'', 'փ'],
			['\"', 'Փ'],
			['z', 'զ'],
			['Z', 'Զ'],
			['x', 'ց'],
			['X', 'Ց'],
			['c', 'գ'],
			['C', 'Գ'],
			['v', 'վ'],
			['V', 'Վ'],
			['b', 'բ'],
			['B', 'Բ'],
			['n', 'ն'],
			['N', 'Ն'],
			['m', 'մ'],
			['M', 'Մ'],
			[',', 'շ'],
			['\\<', 'Շ'],
			['.', 'ղ'],
			['\\>', 'Ղ'],
			['/', 'ծ'],
			['\\?', 'Ծ']
		]
	};

	$.ime.register( hyKbd );

}( jQuery ) );
