<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Action keys. -->
    <item android:state_active="true" android:state_pressed="true"
          android:drawable="@drawable/btn_keyboard_key_pressed_klp_dark" />
    <item android:state_active="true"
          android:drawable="@drawable/btn_keyboard_key_active_klp_dark" />

    <!-- Toggle keys. Use checkable/checked state. -->
    <item android:state_checkable="true" android:state_checked="true" android:state_pressed="true"
          android:drawable="@drawable/btn_keyboard_key_pressed_on_klp_dark" />
    <item android:state_checkable="true" android:state_pressed="true"
          android:drawable="@drawable/btn_keyboard_key_pressed_off_klp_dark" />
    <item android:state_checkable="true" android:state_checked="true"
          android:drawable="@drawable/btn_keyboard_key_normal_on_klp_dark" />
    <item android:state_checkable="true"
          android:drawable="@drawable/btn_keyboard_key_normal_off_holo_dark" />

    <!-- Empty background keys. -->
    <item android:state_empty="true"
          android:drawable="@android:color/transparent" />

    <!-- Normal keys. -->
    <item android:state_pressed="true"
          android:drawable="@drawable/btn_keyboard_key_pressed_klp_light" />
    <item android:drawable="@drawable/btn_keyboard_key_normal_holo_light" />
</selector>
