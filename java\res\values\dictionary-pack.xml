<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="dictionary_pack_client_id" translatable="false">in.androidtweak.inputmethod.indic</string>
    <string name="dictionary_pack_metadata_uri" translatable="false"></string>
    <string name="dictionary_pack_settings_activity" translatable="false">in.androidtweak.inputmethod.dictionarypack.DictionarySettingsActivity</string>
    <string name="authority" translatable="false">in.androidtweak.inputmethod.dictionarypack.aosp</string>
    <string name="default_metadata_uri" translatable="false"></string>
    <string name="local_metadata_filename" translatable="false">metadata.json</string>
</resources>
