<?xml version="1.0"?>
<inputmethod id="or-inscript2" name="InScript2" description="Enhanced InScript keyboard for Odiya language" author="Parag Nemade" version="1.0" contextLength="0" maxKeyLength="1">
  <pattern input="1" replacement="‍" altGr="true"/>
  <pattern input="2" replacement="‌" altGr="true"/>
  <pattern input="4" replacement="₹" altGr="true"/>
  <pattern input="\+" replacement="ୠ" altGr="true"/>
  <pattern input="\=" replacement="ୄ" altGr="true"/>
  <pattern input="R" replacement="ୡ" altGr="true"/>
  <pattern input="r" replacement="ୣ" altGr="true"/>
  <pattern input="\{" replacement="ଢ଼" altGr="true"/>
  <pattern input="\[" replacement="ଡ଼" altGr="true"/>
  <pattern input="F" replacement="ଌ" altGr="true"/>
  <pattern input="f" replacement="ୢ" altGr="true"/>
  <pattern input="b" replacement="ଵ" altGr="true"/>
  <pattern input="\&gt;" replacement="ଽ" altGr="true"/>
  <pattern input="\." replacement="॥" altGr="true"/>
  <pattern input="x" replacement="୰" altGr="true"/>
  <pattern input="1" replacement="୧"/>
  <pattern input="2" replacement="୨"/>
  <pattern input="\#" replacement="୍ର"/>
  <pattern input="3" replacement="୩"/>
  <pattern input="\$" replacement="ର୍"/>
  <pattern input="4" replacement="୪"/>
  <pattern input="5" replacement="୫"/>
  <pattern input="6" replacement="୬"/>
  <pattern input="7" replacement="୭"/>
  <pattern input="8" replacement="୮"/>
  <pattern input="\(" replacement="("/>
  <pattern input="9" replacement="୯"/>
  <pattern input="\)" replacement=")"/>
  <pattern input="0" replacement="୦"/>
  <pattern input="\_" replacement="ଃ"/>
  <pattern input="\-" replacement="-"/>
  <pattern input="\+" replacement="ଋ"/>
  <pattern input="\=" replacement="ୃ"/>
  <pattern input="Q" replacement="ଔ"/>
  <pattern input="q" replacement="ୌ"/>
  <pattern input="W" replacement="ଐ"/>
  <pattern input="w" replacement="ୈ"/>
  <pattern input="E" replacement="ଆ"/>
  <pattern input="e" replacement="ା"/>
  <pattern input="R" replacement="ଈ"/>
  <pattern input="r" replacement="ୀ"/>
  <pattern input="T" replacement="ଊ"/>
  <pattern input="t" replacement="ୂ"/>
  <pattern input="Y" replacement="ଭ"/>
  <pattern input="y" replacement="ବ"/>
  <pattern input="U" replacement="ଙ"/>
  <pattern input="u" replacement="ହ"/>
  <pattern input="I" replacement="ଘ"/>
  <pattern input="i" replacement="ଗ"/>
  <pattern input="O" replacement="ଧ"/>
  <pattern input="o" replacement="ଦ"/>
  <pattern input="P" replacement="ଝ"/>
  <pattern input="p" replacement="ଜ"/>
  <pattern input="\{" replacement="ଢ"/>
  <pattern input="\[" replacement="ଡ"/>
  <pattern input="\}" replacement="ଞ"/>
  <pattern input="\]" replacement="଼"/>
  <pattern input="A" replacement="ଓ"/>
  <pattern input="a" replacement="ୋ"/>
  <pattern input="S" replacement="ଏ"/>
  <pattern input="s" replacement="େ"/>
  <pattern input="D" replacement="ଅ"/>
  <pattern input="d" replacement="୍"/>
  <pattern input="F" replacement="ଇ"/>
  <pattern input="f" replacement="ି"/>
  <pattern input="G" replacement="ଉ"/>
  <pattern input="g" replacement="ୁ"/>
  <pattern input="H" replacement="ଫ"/>
  <pattern input="h" replacement="ପ"/>
  <pattern input="j" replacement="ର"/>
  <pattern input="K" replacement="ଖ"/>
  <pattern input="k" replacement="କ"/>
  <pattern input="L" replacement="ଥ"/>
  <pattern input="l" replacement="ତ"/>
  <pattern input=":" replacement="ଛ"/>
  <pattern input=";" replacement="ଚ"/>
  <pattern input="&quot;" replacement="ଠ"/>
  <pattern input="&apos;" replacement="ଟ"/>
  <pattern input="X" replacement="ଁ"/>
  <pattern input="x" replacement="ଂ"/>
  <pattern input="C" replacement="ଣ"/>
  <pattern input="c" replacement="ମ"/>
  <pattern input="v" replacement="ନ"/>
  <pattern input="b" replacement="ୱ"/>
  <pattern input="N" replacement="ଳ"/>
  <pattern input="n" replacement="ଲ"/>
  <pattern input="M" replacement="ଶ"/>
  <pattern input="m" replacement="ସ"/>
  <pattern input="\&lt;" replacement="ଷ"/>
  <pattern input="," replacement=","/>
  <pattern input="\&gt;" replacement="।"/>
  <pattern input="\." replacement="."/>
  <pattern input="\?" replacement="ଯ"/>
  <pattern input="/" replacement="ୟ"/>
  <pattern input="\%" replacement="ଜ୍ଞ"/>
  <pattern input="\^" replacement="ତ୍ର"/>
  <pattern input="\&amp;" replacement="କ୍ଷ"/>
  <pattern input="\*" replacement="ଶ୍ର"/>
</inputmethod>
