<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright ©
**      2012-2013 <PERSON><PERSON><PERSON>
**      2013-2014 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC), International Centre for Free and Open Source Software (ICFOSS)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted"
        >
            <!-- ൗ, ൌ, , ഔ -->
            <Key
                latin:keySpec="&#x0d57;"
                latin:moreKeys="&#x0D14;,&#x0D4C;"
                latin:keyHintLabel="&#x0D4C;"
                latin:keyLabelFlags="fontNormal" />
            <!-- വ, വ്വ -->
            <Key
                latin:keySpec="&#x0D35;"
                latin:moreKeys="&#x0D35;&#x0D4D;&#x0D35;"
                latin:keyLabelFlags="fontNormal" />
            <!-- േ, ഏ -->
            <Key
                latin:keySpec="&#x0D47;"
                latin:moreKeys="&#x0D0F;"
                latin:keyHintLabel="&#x0D0F;"
                latin:keyLabelFlags="fontNormal" />
            <!-- റ, ഋ,ര്‍‍, ൎ(dot repha),റ്റ -->
            <Key
                latin:keySpec="&#x0D31;"
                latin:moreKeys="&#x0D0B;,&#x0D30;&#x0D4D;&#x200D;,&#x0D4E;,&#x0D31;&#x0D4D;&#x0D31;"
                latin:keyHintLabel="&#x0D0B;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ഥ, ഠ -->
            <Key
                latin:keySpec="&#x0D25;"
                latin:moreKeys="&#x0D20;"
                latin:keyHintLabel="&#x0D20;"
                latin:keyLabelFlags="fontNormal" />
            <!-- യ, യ്യ, ്യ  -->
            <Key
                latin:keySpec="&#x0D2F;"
                latin:moreKeys="&#x0D2F;&#x0D4D;&#x0D2F;,&#x0D4D;&#x0D2F;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ൂ, ഊ -->
            <Key
                latin:keySpec="&#x0D42;"
                latin:moreKeys="&#x0D0A;"
                latin:keyHintLabel="&#x0D0A;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ീ, ഈ -->
            <Key
                latin:keySpec="&#x0D40;"
                latin:moreKeys="&#x0D08;"
                latin:keyHintLabel="&#x0D08;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ോ, ഓ -->
            <Key
                latin:keySpec="&#x0D4B;"
                latin:moreKeys="&#x0D13;"
                latin:keyHintLabel="&#x0D13;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <!-- മ്പ -->
            <Key
                latin:keySpec="&#x0D2E;&#x0D4D;&#x0D2A;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
        </case>

        <default>
            <!--ൈ, ഐ -->
            <Key
                latin:keySpec="&#x0D10;"
                latin:moreKeys="&#x0D48;"
                latin:keyHintLabel="&#x0D48;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <!-- വ, വ്വ -->
            <Key
                latin:keySpec="&#x0D35;"
                latin:moreKeys="&#x0D35;&#x0D4D;&#x0D35;"
                latin:keyLabelFlags="fontNormal" />
            <!-- െ, എ -->
            <Key
                latin:keySpec="&#x0D46;"
                latin:moreKeys="&#x0D0E;"
                latin:keyHintLabel="&#x0D0E;"
                latin:keyLabelFlags="fontNormal" />
            <!--ര, ൃ -->
            <Key
                latin:keySpec="&#x0D30;"
                latin:moreKeys="&#x0D43;"
                latin:keyHintLabel="&#x0D43;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ത, ട, ട്ട, ത്ത, ണ്ട, ന്ത  -->
            <Key
                latin:keySpec="&#x0D24;"
                latin:moreKeys="&#x0D1F;,&#x0D1F;&#x0D4D;&#x0D1F;,&#x0D24;&#x0D4D;&#x0D24;,&#x0D23;&#x0D4D;&#x0D1F;,&#x0D28;&#x0D4D;&#x0D24;"
                latin:keyHintLabel="&#x0D1F;"
                latin:keyLabelFlags="fontNormal" />
            <!-- യ, യ്യ, ്യ  -->
            <Key
                latin:keySpec="&#x0D2F;"
                latin:moreKeys="&#x0D2F;&#x0D4D;&#x0D2F;,&#x0D4D;&#x0D2F;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ു, ഉ -->
            <Key
                latin:keySpec="&#x0D41;"
                latin:moreKeys="&#x0D09;"
                latin:keyHintLabel="&#x0D09;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ി, ഇ -->
            <Key
                latin:keySpec="&#x0D3F;"
                latin:moreKeys="&#x0D07;"
                latin:keyHintLabel="&#x0D07;"
                latin:keyLabelFlags="fontNormal" />
            <!-- ൊ, ഒ -->
            <Key
                latin:keySpec="&#x0D4A;"
                latin:moreKeys="&#x0D12;"
                latin:keyHintLabel="&#x0D12;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <!-- പ, പ്പ -->
            <Key
                latin:keySpec="&#x0D2A;"
                latin:moreKeys="&#x0D2A;&#x0D4D;&#x0D2A;"
                latin:keyLabelFlags="fontNormal" />
         </default>
    </switch>
</merge>
