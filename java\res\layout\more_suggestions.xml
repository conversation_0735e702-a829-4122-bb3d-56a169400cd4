<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2011, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
>
    <in.androidtweak.inputmethod.indic.suggestions.MoreSuggestionsView
        xmlns:latin="http://schemas.android.com/apk/res-auto"
        android:id="@+id/more_suggestions_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        latin:keyLetterSize="@dimen/config_suggestion_text_size"
        latin:keyLabelSize="@dimen/config_suggestion_text_size"
        latin:keyHintLetterRatio="@fraction/config_more_suggestions_info_ratio"
        latin:keyHintLetterColor="@android:color/white" />
</LinearLayout>
