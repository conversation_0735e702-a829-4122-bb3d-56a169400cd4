<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Mon Keyboard layout
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Layout design: Talachan
** Copyright © 2014 Ji<PERSON><PERSON> <PERSON>
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted" >
            <Key
                latin:keySpec="&#x100B;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x1025;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x1006;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x1013;"
                latin:keyLabelFlags="fontNormal" />

            <Key
                latin:keySpec="&#x1016;"
                latin:keyLabelFlags="fontNormal" />

            <Key
                latin:keySpec="&#x101A;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x105C;"
                latin:keyLabelFlags="fontNormal" />

            <Key
                latin:keySpec="&#x1018;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x1011;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x100C;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x103B;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x1033;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
        </case>
        <default>
            <Key
                latin:keySpec="&#x101E;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x101B;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x1019;"
                latin:keyLabelFlags="fontNormal" />

            <Key
                latin:keySpec="&#x1015;"
                latin:keyLabelFlags="fontNormal" />

            <Key
                latin:keySpec="&#x1010;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x1000;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x105A;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x1014;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x1002;"
                latin:keyLabelFlags="fontNormal" />

            <Key
                latin:keySpec="&#x1021;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x100A;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="&#x1001;"
                latin:keyLabelFlags="fontNormal" />
        </default>
    </switch>
</merge>
