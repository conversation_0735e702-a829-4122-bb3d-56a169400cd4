<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2011, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Note that correctionX is obsolete (See com.android.inputmethod.keyboard.internal.TouchPositionCorrection)
        An entry of the touch_position_correction word should be:
        1. correctionX: (touch_center_x - hitbox_center_x) / hitbox_width
        2. correctionY: (touch_center_y - hitbox_center_y) / hitbox_height
        3. correctionR: sweet_spot_radius / sqrt(hitbox_width^2 + hitbox_height^2)
    -->

    <string-array
        name="touch_position_correction_data_default"
        translatable="false"
    >
        <!-- The default touch position data (See com.android.inputmethod.keyboard.ProximityInfo)
             correctionX = 0.0f
             correctionY = 0.0f
             correctionR = DEFAULT_TOUCH_POSITION_CORRECTION_RADIUS
        -->
    </string-array>

    <string-array
        name="touch_position_correction_data_holo"
        translatable="false"
    >
        <!-- First row -->
        <item>0.0038756</item>
        <item>-0.0005677</item>
        <item>0.1577026</item>

        <!-- Second row -->
        <item>-0.0236678</item>
        <item>0.0381731</item>
        <item>0.1529972</item>

        <!-- Third row -->
        <item>-0.0086827</item>
        <item>0.0880847</item>
        <item>0.1522819</item>
    </string-array>
</resources>
