<?xml version="1.0"?>
<inputmethod id="ipa-sil" name="International Phonetic Alphabet - SIL" description="International Phonetic Alphabet - SIL" author="mapping by <PERSON> and <PERSON><PERSON><PERSON>; implementation by <PERSON>" version="1.0" contextLength="0" maxKeyLength="2">
  <pattern input="B=" replacement="ʙ"/>
  <pattern input="f=" replacement="ɸ"/>
  <pattern input="b=" replacement="β"/>
  <pattern input="m&gt;" replacement="ɱ"/>
  <pattern input="v&lt;" replacement="ⱱ"/>
  <pattern input="v=" replacement="ʋ"/>
  <pattern input="t=" replacement="θ"/>
  <pattern input="d=" replacement="ð"/>
  <pattern input="r&gt;" replacement="ɾ"/>
  <pattern input="l=" replacement="ɬ"/>
  <pattern input="l&gt;" replacement="ɮ"/>
  <pattern input="r=" replacement="ɹ"/>
  <pattern input="s=" replacement="ʃ"/>
  <pattern input="z=" replacement="ʒ"/>
  <pattern input="t&lt;" replacement="ʈ"/>
  <pattern input="d&lt;" replacement="ɖ"/>
  <pattern input="n&lt;" replacement="ɳ"/>
  <pattern input="r&lt;" replacement="ɽ"/>
  <pattern input="s&lt;" replacement="ʂ"/>
  <pattern input="z&lt;" replacement="ʐ"/>
  <pattern input="R&lt;" replacement="ɻ"/>
  <pattern input="l&lt;" replacement="ɭ"/>
  <pattern input="j=" replacement="ɟ"/>
  <pattern input="n=" replacement="ɲ"/>
  <pattern input="c=" replacement="ç"/>
  <pattern input="j&lt;" replacement="ʝ"/>
  <pattern input="L&lt;" replacement="ʎ"/>
  <pattern input="g&lt;" replacement="ɡ"/>
  <pattern input="n&gt;" replacement="ŋ"/>
  <pattern input="g=" replacement="ɣ"/>
  <pattern input="w&gt;" replacement="ɰ"/>
  <pattern input="L=" replacement="ʟ"/>
  <pattern input="G=" replacement="ɢ"/>
  <pattern input="N=" replacement="ɴ"/>
  <pattern input="R=" replacement="ʀ"/>
  <pattern input="x=" replacement="χ"/>
  <pattern input="R&gt;" replacement="ʁ"/>
  <pattern input="h&gt;" replacement="ħ"/>
  <pattern input="\?&lt;" replacement="ʕ"/>
  <pattern input="\?=" replacement="ʔ"/>
  <pattern input="h&lt;" replacement="ɦ"/>
  <pattern input="p=" replacement="ʘ"/>
  <pattern input="ǃ&lt;" replacement="ǀ"/>
  <pattern input="!" replacement="ǃ"/>
  <pattern input="ǃ=" replacement="ǂ"/>
  <pattern input="ǃ&gt;" replacement="ǁ"/>
  <pattern input="b&gt;" replacement="ɓ"/>
  <pattern input="d&gt;" replacement="ɗ"/>
  <pattern input="j&gt;" replacement="ʄ"/>
  <pattern input="g&gt;" replacement="ɠ"/>
  <pattern input="G&gt;" replacement="ʛ"/>
  <pattern input="w=" replacement="ʍ"/>
  <pattern input="y&lt;" replacement="ɥ"/>
  <pattern input="h=" replacement="ɥ"/>
  <pattern input="H=" replacement="ʜ"/>
  <pattern input="Q&lt;" replacement="ʢ"/>
  <pattern input="Q=" replacement="ʡ"/>
  <pattern input="c&lt;" replacement="ɕ"/>
  <pattern input="z&gt;" replacement="ʑ"/>
  <pattern input="L&gt;" replacement="ɺ"/>
  <pattern input="H&lt;" replacement="ɧ"/>
  <pattern input="i=" replacement="ɪ"/>
  <pattern input="e&lt;" replacement="ɛ"/>
  <pattern input="a&lt;" replacement="æ"/>
  <pattern input="y=" replacement="ʏ"/>
  <pattern input="o&gt;" replacement="ø"/>
  <pattern input="E&lt;" replacement="œ"/>
  <pattern input="E&gt;" replacement="ɶ"/>
  <pattern input="I=" replacement="ɨ"/>
  <pattern input="E=" replacement="ɘ"/>
  <pattern input="e=" replacement="ə"/>
  <pattern input="e&gt;" replacement="ɜ"/>
  <pattern input="a&gt;" replacement="ɐ"/>
  <pattern input="U=" replacement="ʉ"/>
  <pattern input="O=" replacement="ɵ"/>
  <pattern input="O&lt;" replacement="ɞ"/>
  <pattern input="u=" replacement="ɯ"/>
  <pattern input="O&gt;" replacement="ɤ"/>
  <pattern input="u&gt;" replacement="ʌ"/>
  <pattern input="a=" replacement="ɑ"/>
  <pattern input="u" replacement="u"/>
  <pattern input="u&lt;" replacement="ʊ"/>
  <pattern input="o&lt;" replacement="ɔ"/>
  <pattern input="o=" replacement="ɒ"/>
  <pattern input="ˈ}" replacement="ˌ"/>
  <pattern input="}" replacement="ˈ"/>
  <pattern input="ː:" replacement="ˑ"/>
  <pattern input="ˑ:" replacement="ːː"/>
  <pattern input=":" replacement="ː"/>
  <pattern input="\*\*\*" replacement="̆"/>
  <pattern input="\.&lt;" replacement="|"/>
  <pattern input="\.=" replacement="‖"/>
  <pattern input="#=" replacement="‿"/>
  <pattern input="([˥-˩])4" replacement="$1˥"/>
  <pattern input="([˥-˩])3" replacement="$1˦"/>
  <pattern input="([˥-˩])2" replacement="$1˧"/>
  <pattern input="([˥-˩])1" replacement="$1˨"/>
  <pattern input="([˥-˩])0" replacement="$1˩"/>
  <pattern input="#4" replacement="˥"/>
  <pattern input="#3" replacement="˦"/>
  <pattern input="#2" replacement="˧"/>
  <pattern input="#1" replacement="˨"/>
  <pattern input="#0" replacement="˩"/>
  <pattern input="(?:̀)3" replacement="̌"/>
  <pattern input="(?:́)1" replacement="̂"/>
  <pattern input="(?:̄)3" replacement="᷄"/>
  <pattern input="(?:̀)2" replacement="᷅"/>
  <pattern input="(?:̌)1" replacement="᷈"/>
  <pattern input="(?:̄)1" replacement="᷆"/>
  <pattern input="(?:́)2" replacement="᷇"/>
  <pattern input="(?:̂)3" replacement="᷉"/>
  <pattern input="(?:̊)4" replacement="̋"/>
  <pattern input="(?:̊)3" replacement="́"/>
  <pattern input="(?:̊)2" replacement="̄"/>
  <pattern input="(?:̊)1" replacement="̀"/>
  <pattern input="(?:̊)0" replacement="̏"/>
  <pattern input="\^0" replacement="⁰"/>
  <pattern input="\^1" replacement="¹"/>
  <pattern input="\^2" replacement="²"/>
  <pattern input="\^3" replacement="³"/>
  <pattern input="\^4" replacement="⁴"/>
  <pattern input="\^5" replacement="⁵"/>
  <pattern input="\^6" replacement="⁶"/>
  <pattern input="\^7" replacement="⁷"/>
  <pattern input="\^8" replacement="⁸"/>
  <pattern input="\^9" replacement="⁹"/>
  <pattern input="\^-" replacement="⁻"/>
  <pattern input="ꜛ&gt;" replacement="↗"/>
  <pattern input="ꜜ&lt;" replacement="↘"/>
  <pattern input="#&lt;" replacement="ꜜ"/>
  <pattern input="#&gt;" replacement="ꜛ"/>
  <pattern input="h\^" replacement="ʰ"/>
  <pattern input="w\^" replacement="ʷ"/>
  <pattern input="j\^" replacement="ʲ"/>
  <pattern input="ɣ\^" replacement="ˠ"/>
  <pattern input="ʕ\^" replacement="ˤ"/>
  <pattern input="n\^" replacement="ⁿ"/>
  <pattern input="l\^" replacement="ˡ"/>
  <pattern input="ʽ\[" replacement="˞"/>
  <pattern input="\[\[" replacement="ʽ"/>
  <pattern input="(?:̚)\]" replacement="‘"/>
  <pattern input="’\]" replacement="̚"/>
  <pattern input="\]\]" replacement="’"/>
  <pattern input="(?:̯)\$" replacement="̰"/>
  <pattern input="(?:̩)\$" replacement="̯"/>
  <pattern input="\$" replacement="̩"/>
  <pattern input="(?:̬)%" replacement="̤"/>
  <pattern input="(?:̥)%" replacement="̬"/>
  <pattern input="%" replacement="̥"/>
  <pattern input="@" replacement="̊"/>
  <pattern input="(?:̼)\{" replacement="̣"/>
  <pattern input="(?:̻)\{" replacement="̼"/>
  <pattern input="(?:̺)\{" replacement="̻"/>
  <pattern input="(?:̪)\{" replacement="̺"/>
  <pattern input="\{" replacement="̪"/>
  <pattern input="(?:̃)~" replacement="̴"/>
  <pattern input="~" replacement="̃"/>
  <pattern input="(?:̆)\*" replacement="̇"/>
  <pattern input="(?:̽)\*" replacement="̆"/>
  <pattern input="(?:̈)\*" replacement="̽"/>
  <pattern input="\*" replacement="̈"/>
  <pattern input="#&amp;" replacement="͡"/>
  <pattern input="(?:̊)&amp;" replacement="͜"/>
  <pattern input="(?:̘)\+" replacement="̹"/>
  <pattern input="(?:̙)_" replacement="̜"/>
  <pattern input="(?:̝)\+" replacement="̘"/>
  <pattern input="(?:̞)_" replacement="̙"/>
  <pattern input="(?:̟)\+" replacement="̝"/>
  <pattern input="(?:̠)_" replacement="̞"/>
  <pattern input="\+" replacement="̟"/>
  <pattern input="_" replacement="̠"/>
  <pattern input="=&gt;" replacement="→"/>
  <pattern input="s&gt;" replacement="σ"/>
  <pattern input="=&lt;" replacement="‍"/>
</inputmethod>
