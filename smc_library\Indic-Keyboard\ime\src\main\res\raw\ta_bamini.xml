<?xml version="1.0"?>
<inputmethod id="ta-bamini" name="பாமினி" description="Tamil Bamini input method" author="Junaid P V" version="1.0" contextLength="0" maxKeyLength="2">
  <pattern input="`" replacement="ஹ"/>
  <pattern input="q" replacement="ங"/>
  <pattern input="w" replacement="ற"/>
  <pattern input="e" replacement="ந"/>
  <pattern input="r" replacement="ச"/>
  <pattern input="t" replacement="வ"/>
  <pattern input="y" replacement="ல"/>
  <pattern input="u" replacement="ர"/>
  <pattern input="i" replacement="ை"/>
  <pattern input="o" replacement="ழ"/>
  <pattern input="p" replacement="ி"/>
  <pattern input="\[" replacement="ஜ"/>
  <pattern input="\]" replacement="ஸ"/>
  <pattern input="\\" replacement="ஷ"/>
  <pattern input="a" replacement="ய"/>
  <pattern input="s" replacement="ள"/>
  <pattern input="d" replacement="ன"/>
  <pattern input="f" replacement="க"/>
  <pattern input="g" replacement="ப"/>
  <pattern input="h" replacement="ா"/>
  <pattern input="j" replacement="த"/>
  <pattern input="k" replacement="ம"/>
  <pattern input="l" replacement="ட"/>
  <pattern input=";" replacement="்"/>
  <pattern input="z" replacement="ண"/>
  <pattern input="x" replacement="ஒ"/>
  <pattern input="c" replacement="உ"/>
  <pattern input="v" replacement="எ"/>
  <pattern input="b" replacement="டி"/>
  <pattern input="n" replacement="ெ"/>
  <pattern input="N" replacement="ே"/>
  <pattern input="m" replacement="அ"/>
  <pattern input="," replacement="இ"/>
  <pattern input="/" replacement="ஃ"/>
  <pattern input="@" replacement=";"/>
  <pattern input="#" replacement="சூ"/>
  <pattern input="\$" replacement="கூ"/>
  <pattern input="%" replacement="மூ"/>
  <pattern input="\^" replacement="டூ"/>
  <pattern input="&amp;" replacement="ரூ"/>
  <pattern input="\*" replacement="ழூ"/>
  <pattern input="=" replacement="ஸ்ரீ"/>
  <pattern input="Q" replacement="ஞ"/>
  <pattern input="W" replacement="று"/>
  <pattern input="E" replacement="நு"/>
  <pattern input="R" replacement="சு"/>
  <pattern input="T" replacement="வு"/>
  <pattern input="Y" replacement="லு"/>
  <pattern input="U" replacement="ரு"/>
  <pattern input="I" replacement="ஐ"/>
  <pattern input="O" replacement="ழு"/>
  <pattern input="P" replacement="ீ"/>
  <pattern input="\|" replacement="&apos;"/>
  <pattern input="A" replacement="யு"/>
  <pattern input="S" replacement="ளு"/>
  <pattern input="D" replacement="னு"/>
  <pattern input="F" replacement="கு"/>
  <pattern input="G" replacement="பு"/>
  <pattern input="H" replacement="ர்"/>
  <pattern input="J" replacement="து"/>
  <pattern input="K" replacement="மு"/>
  <pattern input="L" replacement="டு"/>
  <pattern input="Z" replacement="ணு"/>
  <pattern input="X" replacement="ஓ"/>
  <pattern input="C" replacement="ஊ"/>
  <pattern input="V" replacement="ஏ"/>
  <pattern input="B" replacement="டீ"/>
  <pattern input="M" replacement="ஆ"/>
  <pattern input="\&lt;" replacement="ஈ"/>
  <pattern input="\&gt;" replacement=","/>
  <pattern input="([ஜஷஸஹ])\{" replacement="$1ு"/>
  <pattern input="([ஜஷஸஹ])\_" replacement="$1ூ"/>
</inputmethod>
