<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:orientation="vertical" >

    <!-- To ensure that key preview popup is correctly placed when the current system locale is
         one of RTL locales, layoutDirection="ltr" is needed in the SDK version 17+. -->
    <in.androidtweak.inputmethod.indic.suggestions.SuggestionStripView
        android:id="@+id/suggestion_strip_view"
        android:layoutDirection="ltr"
        android:layout_width="match_parent"
        android:layout_height="@dimen/config_suggestions_strip_height"
        android:gravity="center_vertical"
        style="?attr/suggestionStripViewStyle" />

    <!-- To ensure that key preview popup is correctly placed when the current system locale is
         one of RTL locales, layoutDirection="ltr" is needed in the SDK version 17+. -->
    <com.android.inputmethod.keyboard.MainKeyboardView
        android:id="@+id/keyboard_view"
        android:layoutDirection="ltr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>
