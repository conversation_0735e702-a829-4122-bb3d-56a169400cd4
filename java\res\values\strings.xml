<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2008, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Title for Latin keyboard input options dialog [CHAR LIMIT=25] -->
    <string name="english_ime_input_options">Input options</string>

    <!-- Title for the spell checker option to turn on/off contact names lookup [CHAR LIMIT=25] -->
    <string name="use_contacts_for_spellchecking_option_title">Look up contact names</string>

    <!-- Description for the spell checker option to turn on/off contact names lookup. [CHAR LIMIT=65] -->
    <string name="use_contacts_for_spellchecking_option_summary">Spell checker uses entries from your contact list</string>

    <!-- Option to provide vibrate/haptic feedback on keypress -->
    <string name="vibrate_on_keypress">Vibrate on keypress</string>

    <!-- Option to play back sound on keypress in soft keyboard -->
    <string name="sound_on_keypress">Sound on keypress</string>

    <!-- Option to control whether or not to show a popup with a larger font on each key press. -->
    <string name="popup_on_keypress">Popup on keypress</string>

    <!-- Settings screen title for preferences [CHAR LIMIT=33]-->
    <string name="settings_screen_preferences">Preferences</string>
    <!-- Settings screen title for appearance & layouts preferences [CHAR LIMIT=33] -->
    <string name="settings_screen_appearance">Appearance &amp; layouts</string>
    <!-- Settings screen title for multilingual options [CHAR_LIMIT=33] -->
    <string name="settings_screen_multilingual">Multilingual options</string>
    <!-- Settings screen title for gesture typing preferences [CHAR_LIMIT=33] -->
    <string name="settings_screen_gesture">Gesture Typing</string>
    <!-- Settings screen title for text correction options [CHAR_LIMIT=33] -->
    <string name="settings_screen_correction">Text correction</string>
    <!-- Settings screen title for advanced settings [CHAR LIMIT=33] -->
    <string name="settings_screen_advanced">Advanced</string>
    <!-- Settings screen title for keyboard theme settings [CHAR LIMIT=33] -->
    <string name="settings_screen_theme">Theme</string>

    <!-- Option name for including other IMEs in the language switch list [CHAR LIMIT=30] -->
    <string name="include_other_imes_in_language_switch_list">Switch to other input methods</string>
    <!-- Option summary for including other IMEs in the language switch list [CHAR LIMIT=65] -->
    <string name="include_other_imes_in_language_switch_list_summary">Language switch key covers other input methods too</string>
    <!-- Option to show language switch key [CHAR LIMIT=30] -->
    <string name="show_language_switch_key">Language switch key</string>
    <!-- Option summary for showing language switch key [CHAR LIMIT=65] -->
    <string name="show_language_switch_key_summary">Show when multiple input languages are enabled</string>

    <!-- Option for the dismiss delay of the key popup [CHAR LIMIT=25] -->
    <string name="key_preview_popup_dismiss_delay">Key popup dismiss delay</string>
    <!-- Description for delay for dismissing a popup on keypress: no delay [CHAR LIMIT=15] -->
    <string name="key_preview_popup_dismiss_no_delay">No delay</string>
    <!-- Description for delay for dismissing a popup on screen: default value of the delay [CHAR LIMIT=15] -->
    <string name="key_preview_popup_dismiss_default_delay">Default</string>

    <!-- Units abbreviation for the duration (milliseconds) [CHAR LIMIT=10] -->
    <string name="abbreviation_unit_milliseconds"><xliff:g id="MILLISECONDS">%s</xliff:g>ms</string>
    <!-- The text that represents the current settings value is the system default [CHAR LIMIT=25] -->
    <string name="settings_system_default">System default</string>

    <!-- Option name for enabling or disabling the use of names of people in Contacts for suggestion and correction [CHAR LIMIT=25] -->
    <string name="use_contacts_dict">Suggest Contact names</string>
    <!-- Description for option enabling or disabling the use of names of people in Contacts for suggestion and correction [CHAR LIMIT=65] -->
    <string name="use_contacts_dict_summary">Use names from Contacts for suggestions and corrections</string>

    <!-- Option name for enabling the use by the keyboards of sent/received messages, e-mail and typing history to improve suggestion accuracy [CHAR LIMIT=25] -->
    <string name="use_personalized_dicts">Personalized suggestions</string>

    <!-- Option to enable sending usage statistics -->
    <string name="enable_metrics_logging">"Improve <xliff:g id="APPLICATION_NAME" example="Android Keyboard">%s</xliff:g>"</string>

    <!-- Option name for enabling or disabling the double-space period feature that lets double tap on spacebar insert a period followed by a space [CHAR LIMIT=30] -->
    <string name="use_double_space_period">Double-space period</string>
    <!-- Description for option enabling or disabling the double-space period feature that lets double tap on spacebar insert a period followed by a space [CHAR LIMIT=65] -->
    <string name="use_double_space_period_summary">Double tap on spacebar inserts a period followed by a space</string>

    <!-- Option to enable auto capitalization of sentences -->
    <string name="auto_cap">Auto-capitalization</string>
    <!-- Description for option to enable auto capitalization of sentences -->
    <string name="auto_cap_summary">Capitalize the first word of each sentence</string>

    <!-- Option to edit personal dictionary. [CHAR_LIMIT=30]-->
    <string name="edit_personal_dictionary">Personal dictionary</string>

    <!-- Option to configure dictionaries -->
    <string name="configure_dictionaries_title">Add-on dictionaries</string>
    <!-- Name of the main dictionary, as opposed to auxiliary dictionaries (medical/entertainment/sports...) -->
    <string name="main_dictionary">Main dictionary</string>

    <!-- Option to enable showing suggestions -->
    <string name="prefs_show_suggestions">Show correction suggestions</string>
    <!-- Description for show suggestions -->
    <string name="prefs_show_suggestions_summary">Display suggested words while typing</string>

    <!-- Option to block potentially offensive words to be shown [CHAR_LIMIT=30] -->
    <string name="prefs_block_potentially_offensive_title">Block offensive words</string>
    <!-- Summary for option to block potentially offensive words to be shown [CHAR_LIMIT=80 (two lines) or 40 (fits on one line, preferable)] -->
    <string name="prefs_block_potentially_offensive_summary">Do not suggest potentially offensive words</string>

    <!-- Option to decide the auto correction threshold score -->
    <!-- Option to enable auto correction [CHAR LIMIT=20]-->
    <string name="auto_correction">Auto-correction</string>
    <!-- Description for auto correction [CHAR LIMIT=65 (two lines) or 30 (fits on one line, preferable)] -->
    <string name="auto_correction_summary">Spacebar and punctuation automatically correct mistyped words</string>
    <!-- Option to disable auto correction. [CHAR LIMIT=20] -->
    <string name="auto_correction_threshold_mode_off">Off</string>
    <!-- Option to suggest auto correction suggestions modestly. Auto-corrects only to a word which has small edit distance from typed word. [CHAR LIMIT=20] -->
    <string name="auto_correction_threshold_mode_modest">Modest</string>
    <!-- Option to suggest auto correction suggestions aggressively. Auto-corrects to a word which has even large edit distance from typed word. [CHAR LIMIT=20] -->
    <string name="auto_correction_threshold_mode_aggressive">Aggressive</string>
    <!-- Option to suggest auto correction suggestions very aggressively. Auto-corrects to a word which has even large edit distance from typed word. [CHAR LIMIT=20] -->
    <string name="auto_correction_threshold_mode_very_aggressive">Very aggressive</string>

    <!-- Option to enable using next word suggestions. After the user types a space, with this option on, the keyboard will try to predict the next word. -->
    <string name="bigram_prediction">Next-word suggestions</string>
    <!-- Description for "next word suggestion" option. This displays suggestions even when there is no input, based on the previous word. -->
    <string name="bigram_prediction_summary">Use the previous word in making suggestions</string>

    <!-- Option to enable gesture input. The user can input a word by tracing the letters of a word without releasing the finger from the screen. [CHAR LIMIT=30]-->
    <string name="gesture_input">Enable gesture typing</string>
    <!-- Description for "gesture_input" option. The user can input a word by tracing the letters of a word without releasing the finger from the screen. [CHAR LIMIT=65]-->
    <string name="gesture_input_summary">Input a word by sliding through the letters</string>
    <!-- Option to enable gesture trail preview. The user can see a trail of the gesture during gesture input. [CHAR LIMIT=30]-->
    <string name="gesture_preview_trail">Show gesture trail</string>
    <!-- Option to enable gesture floating text preview. The user can see a suggested word floating under the moving finger during a gesture input. [CHAR LIMIT=30]-->
    <string name="gesture_floating_preview_text">Dynamic floating preview</string>
    <!-- Description for "gesture_floating_preview_text" option. The user can see a suggested word floating under the moving finger during a gesture input. [CHAR LIMIT=65]-->
    <string name="gesture_floating_preview_text_summary">See the suggested word while gesturing</string>
    <!-- Option to enable space aware gesture input. The user can input multiple words by gliding through the space key during a gesture input. [CHAR LIMIT=30]-->
    <string name="gesture_space_aware">Phrase gesture</string>
    <!-- Description for "gesture_space_aware" option. The user can input multiple words by gliding through the space key during a gesture input.[CHAR LIMIT=65]-->
    <string name="gesture_space_aware_summary">Input spaces during gestures by gliding to the space key</string>

    <!-- Preferences item for enabling speech input -->
    <string name="voice_input">Voice input key</string>
    <!-- The summary text to describe the reason why the "Voice input key" option is disabled. [CHAR LIMIT=100] -->
    <string name="voice_input_disabled_summary">No voice input methods enabled. Check Language &amp; input settings.</string>

    <!-- Title for configuring input method settings [CHAR LIMIT=35] -->
    <string name="configure_input_method">Configure input methods</string>

    <!-- Title for input language selection screen -->
    <string name="language_selection_title">Languages</string>

    <!-- Title for the 'Help & feedback' settings fragment which shows a help page and has a button
         for submitting feedback. [CHAR LIMIT=35] -->
    <string name="help_and_feedback">Help &amp; feedback</string>

    <!-- Preference for input language selection -->
    <string name="select_language">Languages</string>

    <!-- Add to dictionary hint -->
    <string name="hint_add_to_dictionary">Touch again to save</string>
    <!-- Add to dictionary hint -->
    <string name="hint_add_to_dictionary_without_word">Touch here to save</string>

    <!-- Inform the user that a particular language has an available dictionary -->
    <string name="has_dictionary">Dictionary available</string>

    <!-- Title of the item to change the keyboard theme [CHAR LIMIT=20]-->
    <string name="keyboard_layout">Keyboard theme</string>

    <!-- Description for English (UK) keyboard subtype [CHAR LIMIT=25]
         (UK) should be an abbreviation of United Kingdom to fit in the CHAR LIMIT. -->
    <string name="subtype_en_GB">English (UK)</string>
    <!-- Description for English (US) keyboard subtype [CHAR LIMIT=25]
         (US) should be an abbreviation of United States to fit in the CHAR LIMIT. -->
    <string name="subtype_en_US">English (US)</string>
    <!-- Description for Spanish (US) keyboard subtype [CHAR LIMIT=25]
         (US) should be an abbreviation of United States to fit in the CHAR LIMIT. -->
    <string name="subtype_es_US">Spanish (US)</string>
    <!-- Description for English (UK) keyboard subtype with explicit keyboard layout [CHAR LIMIT=25]
         (UK) should be an abbreviation of United Kingdom to fit in the CHAR LIMIT.
         This should be identical to subtype_en_GB aside from the trailing (%s). -->
    <string name="subtype_with_layout_en_GB">English (UK) (<xliff:g id="KEYBOARD_LAYOUT" example="QWERTY">%s</xliff:g>)</string>
    <!-- Description for English (US) keyboard subtype with explicit keyboard layout [CHAR LIMIT=25]
         (US) should be an abbreviation of United States to fit in the CHAR LIMIT.
         This should be identical to subtype_en_US aside from the trailing (%s). -->
    <string name="subtype_with_layout_en_US">English (US) (<xliff:g id="KEYBOARD_LAYOUT" example="QWERTY">%s</xliff:g>)</string>
    <!-- Description for Spanish (US) keyboard subtype with explicit keyboard layout [CHAR LIMIT=25]
         (US) should be an abbreviation of United Statesn to fit in the CHAR LIMIT.
         This should be identical to subtype_es_US aside from the trailing (%s). -->
    <string name="subtype_with_layout_es_US">Spanish (US) (<xliff:g id="KEYBOARD_LAYOUT" example="QWERTY">%s</xliff:g>)</string>
    <!-- Description for "LANGUAGE_NAME" (Traditional) keyboard subtype [CHAR LIMIT=25]
         (Traditional) can be an abbreviation to fit in the CHAR LIMIT. -->
    <string name="subtype_generic_traditional"><xliff:g id="LANGUAGE_NAME" example="Nepali">%s</xliff:g> (Traditional)</string>
    <!-- Description for "LANGUAGE_NAME" (Compact) keyboard subtype [CHAR LIMIT=25]
         (Compact) can be an abbreviation to fit in the CHAR LIMIT.
         TODO: Remove translatable=false once we are settled down with the naming. -->
    <string name="subtype_generic_compact" translatable="false"><xliff:g id="LANGUAGE_NAME" example="Hindi">%s</xliff:g> (Compact)</string>
    <!-- Description for "LANGUAGE_NAME" (Cyrillic) keyboard subtype [CHAR LIMIT=25]
         (Cyrillic) can be an abbreviation to fit in the CHAR LIMIT. -->
    <string name="subtype_generic_cyrillic"><xliff:g id="LANGUAGE_NAME" example="Serbian">%s</xliff:g> (Cyrillic)</string>
    <!-- Description for "LANGUAGE_NAME" (Latin) keyboard subtype [CHAR LIMIT=25]
         (Latin) can be an abbreviation to fit in the CHAR LIMIT. -->
    <string name="subtype_generic_latin"><xliff:g id="LANGUAGE_NAME" example="Serbian">%s</xliff:g> (Latin)</string>
    <!-- This string is displayed in a language list that allows to choose a language for
suggestions in a software keyboard. This setting won't give suggestions in any particular
language, hence "No language".
As for the "alphabet" mention, it refers specifically to the Latin alphabet, as opposed to
Cyrillic, Arabic, Hebrew or other scripts. This keyboard offers no suggestions, but it will
be a QWERTY, or AZERTY, or any other disposition that only offers Latin characters, so
you wouldn't be able to type, say, Arabic on it. Please translate it in a way that "alphabet"
would be understood to mean specifically the Latin alphabet, rather than any other
alphabet. [CHAR LIMIT=29] -->
    <string name="subtype_no_language">No language (Alphabet)</string>
    <!-- This string is displayed in the description for a keyboard type. It refers specifically to
the Latin alphabet, as opposed to Cyrillic, Arabic, Hebrew or other scripts.
When the device is configured to use a language using a script other than the Latin alphabet, the
user still needs a keyboard that can input Latin characters for passwords or login names for
example, and a way to switch to this Latin alphabet keyboard. This string is the description for
this keyboard, so users of other scripts should understand when they read this that it represents a
keyboard that is meant for them to be able to enter Latin characters as opposed to the script they
are used to. This keyboard does not provide a dictionary, and it is not tied to any specific
language among those that use the Latin alphabet. This keyboard is laid out in the QWERTY
disposition rather than other common dispositions for Latin languages. [CHAR LIMIT=25] -->
    <string name="subtype_no_language_qwerty">Alphabet (QWERTY)</string>
    <!-- This string is displayed in the description for a keyboard type. It refers specifically to
the Latin alphabet, as opposed to Cyrillic, Arabic, Hebrew or other scripts.
When the device is configured to use a language using a script other than the Latin alphabet, the
user still needs a keyboard that can input Latin characters for passwords or login names for
example, and a way to switch to this Latin alphabet keyboard. This string is the description for
this keyboard, so users of other scripts should understand when they read this that it represents a
keyboard that is meant for them to be able to enter Latin characters as opposed to the script they
are used to. This keyboard does not provide a dictionary, and it is not tied to any specific
language among those that use the Latin alphabet. This keyboard is laid out in the QWERTZ
disposition rather than other common dispositions for Latin languages. [CHAR LIMIT=25] -->
    <string name="subtype_no_language_qwertz">Alphabet (QWERTZ)</string>
    <!-- This string is displayed in the description for a keyboard type. It refers specifically to
the Latin alphabet, as opposed to Cyrillic, Arabic, Hebrew or other scripts.
When the device is configured to use a language using a script other than the Latin alphabet, the
user still needs a keyboard that can input Latin characters for passwords or login names for
example, and a way to switch to this Latin alphabet keyboard. This string is the description for
this keyboard, so users of other scripts should understand when they read this that it represents a
keyboard that is meant for them to be able to enter Latin characters as opposed to the script they
are used to. This keyboard does not provide a dictionary, and it is not tied to any specific
language among those that use the Latin alphabet. This keyboard is laid out in the AZERTY
disposition rather than other common dispositions for Latin languages. [CHAR LIMIT=25] -->
    <string name="subtype_no_language_azerty">Alphabet (AZERTY)</string>
    <!-- This string is displayed in the description for a keyboard type. It refers specifically to
the Latin alphabet, as opposed to Cyrillic, Arabic, Hebrew or other scripts.
When the device is configured to use a language using a script other than the Latin alphabet, the
user still needs a keyboard that can input Latin characters for passwords or login names for
example, and a way to switch to this Latin alphabet keyboard. This string is the description for
this keyboard, so users of other scripts should understand when they read this that it represents a
keyboard that is meant for them to be able to enter Latin characters as opposed to the script they
are used to. This keyboard does not provide a dictionary, and it is not tied to any specific
language among those that use the Latin alphabet. This keyboard is laid out in the Dvorak
disposition rather than other common dispositions for Latin languages. [CHAR LIMIT=25] -->
    <string name="subtype_no_language_dvorak">Alphabet (Dvorak)</string>
    <!-- This string is displayed in the description for a keyboard type. It refers specifically to
the Latin alphabet, as opposed to Cyrillic, Arabic, Hebrew or other scripts.
When the device is configured to use a language using a script other than the Latin alphabet, the
user still needs a keyboard that can input Latin characters for passwords or login names for
example, and a way to switch to this Latin alphabet keyboard. This string is the description for
this keyboard, so users of other scripts should understand when they read this that it represents a
keyboard that is meant for them to be able to enter Latin characters as opposed to the script they
are used to. This keyboard does not provide a dictionary, and it is not tied to any specific
language among those that use the Latin alphabet. This keyboard is laid out in the Colemak
disposition rather than other common dispositions for Latin languages. [CHAR LIMIT=25] -->
    <string name="subtype_no_language_colemak">Alphabet (Colemak)</string>
    <!-- This string is displayed in the description for a keyboard type. It refers specifically to
the Latin alphabet, as opposed to Cyrillic, Arabic, Hebrew or other scripts.
When the device is configured to use a language using a script other than the Latin alphabet, the
user still needs a keyboard that can input Latin characters for passwords or login names for
example, and a way to switch to this Latin alphabet keyboard. This string is the description for
this keyboard, so users of other scripts should understand when they read this that it represents a
keyboard that is meant for them to be able to enter Latin characters as opposed to the script they
are used to. This keyboard does not provide a dictionary, and it is not tied to any specific
language among those that use the Latin alphabet. This keyboard is laid out in the QWERTY PC
disposition that offers additional keys, but smaller keys compared to other common dispositions for
mobile devices. [CHAR LIMIT=25] -->
    <string name="subtype_no_language_pcqwerty">Alphabet (PC)</string>
    <!-- Description for Emoji keyboard subtype [CHAR LIMIT=25] -->
    <string name="subtype_emoji">Emoji</string>

    <!-- Title of the preference settings for switching keyboard theme [CHAR LIMIT=35] -->
    <string name="keyboard_theme">Keyboard theme</string>
    <!-- The keyboard theme name, Holo White. This is marked as translatable="false" because this is a proper name of system-wide UI Theme. -->
    <string name="keyboard_theme_holo_white" translatable="false">Holo White</string>
    <!-- The keyboard theme name, Holo Blue. This is marked as translatable="false" because this is a proper name of system-wide UI Theme.  -->
    <string name="keyboard_theme_holo_blue" translatable="false">Holo Blue</string>
    <!-- The keyboard theme name, Material Dark. This is marked as translatable="false" because this is a proper name of system-wide UI Theme.  -->
    <string name="keyboard_theme_material_dark" translatable="false">Material Dark</string>
    <!-- The keyboard theme name, Material Light. This is marked as translatable="false" because this is a proper name of system-wide UI Theme.  -->
    <string name="keyboard_theme_material_light" translatable="false">Material Light</string>

    <!-- Title of the preference settings for custom input styles (language and keyboard layout pairs) [CHAR LIMIT=35]-->
    <string name="custom_input_styles_title">Custom input styles</string>
    <!-- Title of the option menu to add a new style entry in the preference settings [CHAR LIMIT=16] -->
    <string name="add_style">Add style</string>
    <!-- Title of the button to add custom style entry in the settings dialog [CHAR LIMIT=15] -->
    <string name="add">Add</string>
    <!-- Title of the button to remove a custom style entry in the settings dialog [CHAR LIMIT=15] -->
    <string name="remove">Remove</string>
    <!-- Title of the button to save a custom style entry in the settings dialog [CHAR LIMIT=15] -->
    <string name="save">Save</string>
    <!-- Title of the spinner for choosing a language of custom style in the settings dialog [CHAR LIMIT=15] -->
    <string name="subtype_locale">Language</string>
    <!-- Title of the spinner for choosing a keyboard layout of custom style in the settings dialog [CHAR LIMIT=15] -->
    <string name="keyboard_layout_set">Layout</string>
    <!-- The message of the dialog to note that a custom input style needs to be enabled. [CHAR LIMIT=130] -->
    <string name="custom_input_style_note_message">"Your custom input style needs to be enabled before you start using it. Do you want to enable it now?"</string>
    <!-- Title of the button to enable a custom input style entry in the settings dialog [CHAR LIMIT=15] -->
    <string name="enable">Enable</string>
    <!-- Title of the button to postpone enabling a custom input style entry in the settings dialog [CHAR LIMIT=15] -->
    <string name="not_now">Not now</string>
    <!-- Toast text to describe the same input style already exists [CHAR LIMIT=64]-->
    <string name="custom_input_style_already_exists">"The same input style already exists: <xliff:g id="INPUT_STYLE_NAME" example="English (Dvorak)">%s</xliff:g>"</string>

    <!-- Title of the settings for keypress vibration duration [CHAR LIMIT=35] -->
    <string name="prefs_keypress_vibration_duration_settings">Keypress vibration duration</string>
    <!-- Title of the settings for keypress sound volume [CHAR LIMIT=35] -->
    <string name="prefs_keypress_sound_volume_settings">Keypress sound volume</string>

    <!-- Title of the button to revert to the default value of the device in the settings dialog [CHAR LIMIT=15] -->
    <string name="button_default">Default</string>

    <!-- Title of the setup wizard welcome screen. [CHAR LIMT=40] -->
    <string name="setup_welcome_title">"Welcome to <xliff:g id="application_name">%s</xliff:g>"</string>
    <!-- Additional title of the setup wizard welcome screen, just below the setup_welcome_title. [CHAR_LIMIT=64] -->
    <string name="setup_welcome_additional_description">"नमस्ते\nவணக்கம் \nനമസ്കാരം \nನಮಸ್ಕಾರ \nనమస్కారం \nনমস্কার \nਸਤਿ ਸ੍ਰੀ ਅਕਾਲ \nનમસ્તે"</string>
    <!-- The label of the button that starts the setup wizard. [CHAR_LIMIT=64] -->
    <string name="setup_start_action">Get started</string>
    <!-- The label of the button that navigates the user to the next step of the setup wizard. [CHAR_LIMIT=64] -->
    <string name="setup_next_action">Next step</string>
    <!-- Title of the setup wizard. [CHAR LIMT=40] -->
    <string name="setup_steps_title">"Setting up <xliff:g id="APPLICATION_NAME" example="Android Keyboard">%s</xliff:g>"</string>
    <!-- Ordinal number of the 1st step in the setup wizard. [CHAR LIMIT=5] -->
    <string name="setup_step1_bullet" translatable="false">1</string>
    <!-- Title of the 1st step in the setup wizard. [CHAR LIMIT=64] -->
    <string name="setup_step1_title">"Enable <xliff:g id="APPLICATION_NAME" example="Android Keyboard">%s</xliff:g>"</string>
    <!-- Detailed instruction of the 1st step in the setup wizard. [CHAR LIMIT=120] -->
    <string name="setup_step1_instruction">"Please check \"<xliff:g id="APPLICATION_NAME" example="Android Keyboard">%s</xliff:g>\" in your Language &amp; input settings. This will authorize it to run on your device."</string>
    <!-- Detailed instruction of the already finished 1st step in the setup wizard. [CHAR LIMIT=120] -->
    <string name="setup_step1_finished_instruction">"<xliff:g id="APPLICATION_NAME" example="Android Keyboard">%s</xliff:g> is already enabled in your Language &amp; input settings, so this step is done. On to the next one!"</string>
    <!-- The label of the button that triggers the Language & input settings in order to enable the keyboard. [CHAR_LIMIT=64] -->
    <string name="setup_step1_action">Enable in Settings</string>
    <!-- Ordinal number of the 2nd step in the setup wizard. [CHAR LIMIT=5] -->
    <string name="setup_step2_bullet" translatable="false">2</string>
    <!-- Title of the 2nd step in the setup wizard. [CHAR LIMIT=64] -->
    <string name="setup_step2_title">"Switch to <xliff:g id="APPLICATION_NAME" example="Android Keyboard">%s</xliff:g>"</string>
    <!-- Detailed instruction of the 2nd step in the setup wizard. [CHAR LIMIT=120] -->
    <string name="setup_step2_instruction">"Next, select \"<xliff:g id="APPLICATION_NAME" example="Android Keyboard">%s</xliff:g>\" as your active text-input method."</string>
    <!-- The label of the button that triggers the choose input method dialog in order to select the keyboard. [CHAR_LIMIT=64] -->
    <string name="setup_step2_action">Switch input methods</string>
    <!-- Ordinal number of the 3rd step in the setup wizard. [CHAR LIMIT=5] -->
    <string name="setup_step3_bullet" translatable="false">3</string>
    <!-- Title of the 3rd step in the setup wizard. [CHAR LIMIT=64] -->
    <string name="setup_step3_title">"Select languages"</string>
    <string name="setup_step3_instruction">Select the languages and layouts you want to use.</string>
    <string name="setup_step3_action">Configure languages</string>
    <!-- The label of the button that triggers the screen for configuaring additional languages of the keyboard. [CHAR_LIMIT=64] -->
    <string name="setup_step4_bullet" translatable="false">4</string>
    <string name="setup_step4_title">"Congratulations, you're all set!"</string>
    <!-- Detailed instruction of the 3rd step in the setup wizard. [CHAR LIMIT=120] -->
    <string name="setup_step4_instruction">Now you can type in all your favorite apps with <xliff:g id="APPLICATION_NAME" example="Android Keyboard">%s</xliff:g>.</string>
    <!-- The label of the button that finishes the setup wizard. [CHAR_LIMIT=64] -->
    <string name="setup_finish_action">Finished</string>
    <!-- Option to show setup wizard icon. [CHAR LIMIT=30]-->
    <string name="show_setup_wizard_icon">Show app icon</string>
    <!-- Description for the option to show setup wizard application icon of this IME in the laucher. [CHAR_LIMIT=65] -->
    <string name="show_setup_wizard_icon_summary">Display application icon in the launcher</string>

    <!-- The dictionary provider application name. Visible in Settings/Applications/Manage applications. -->
    <string name="app_name">Dictionary Provider</string>
    <!-- The dictionary provider ContentProvider name. Visible in Settings/Applications/Running services. -->
    <string name="dictionary_provider_name">Dictionary Provider</string>
    <!-- The dictionary provider Service name. Visible in Settings/Applications/Running services. -->
    <string name="dictionary_service_name">Dictionary Service</string>

    <!-- Downloadable dictionaries will get update information through the network. This describes the associated download. -->
    <string name="download_description">Dictionary update information</string>

    <!-- Title and summary of the dictionary settings screen -->
    <string name="dictionary_settings_title">Add-on dictionaries</string>
    <!-- Title for the prompt dialog which informs the user that a dictionary is available for the current language and asks to decide whether to download it over 3g -->
    <string name="dictionary_install_over_metered_network_prompt">Dictionary available</string>
    <string name="dictionary_settings_summary">Settings for dictionaries</string>
    <!-- Name of the user dictionaries settings category -->
    <string name="user_dictionaries">User dictionaries</string>
    <!-- Name for the "user dictionary" preference item when there is only one -->
    <string name="default_user_dict_pref_name">User dictionary</string>
    <!-- Message about some dictionary indicating it can be downloaded, but hasn't been yet -->
    <string name="dictionary_available">Dictionary available</string>
    <!-- Message about some dictionary indicating it is downloading and should be available soon -->
    <string name="dictionary_downloading">Currently downloading</string>
    <!-- Message about some dictionary indicating it is already installed -->
    <string name="dictionary_installed">Installed</string>
    <!-- Message about some dictionary indicating the file is installed, but the dictionary is disabled -->
    <string name="dictionary_disabled">Installed, disabled</string>

    <!-- Message to display in the dictionaries setting screen when some error prevented us to list installed dictionaries [CHAR LIMIT=20] -->
    <string name="cannot_connect_to_dict_service">Problem connecting to dictionary service</string>
    <!-- Message to display in the dictionaries setting screen when we found that no dictionaries are available [CHAR LIMIT=20]-->
    <string name="no_dictionaries_available">No dictionaries available</string>

    <!-- Title of the options to press to refresh the list (as in, check for updates now) [CHAR_LIMIT=50] -->
    <string name="check_for_updates_now">Refresh</string>
    <!-- Hint to tell when the data was last updated. Usage : "Last updated [date]", may contain a : or so. [CHAR LIMIT=45] -->
    <string name="last_update">Last updated</string>

    <!-- Message to display in a dialog box while we are actively updating the word list [CHAR LIMIT=60] -->
    <string name="message_updating">Checking for updates</string>
    <!-- Message to display while the add-on dictionary list is updating [no space constraints on this, there is plenty of space but shorter is better because it's only on the screen for a second] -->
    <string name="message_loading">Loading&#x2026;</string>

    <!-- String to explain this dictionary is the main dictionary for this language [CHAR_LIMIT=30] -->
    <string name="main_dict_description">Main dictionary</string>

    <!-- Standard message to dismiss a dialog box -->
    <string name="cancel">Cancel</string>
    <!-- Title of the button in a dialog box. The button takes the user to the keyboard settings. [CHAR LIMIT=15] -->
    <string name="go_to_settings">Settings</string>

    <!-- Action to download and install a dictionary [CHAR_LIMIT=15] -->
    <string name="install_dict">Install</string>
    <!-- Action to cancel the ongoing download of a dictionary file [CHAR_LIMIT=25] -->
    <string name="cancel_download_dict">Cancel</string>
    <!-- Action to delete a dictionary file [CHAR_LIMIT=15] -->
    <string name="delete_dict">Delete</string>

    <!-- Message in the popup informing the user a dictionary is available for their language, and asking for a decision to download over their mobile data plan or not. The reason we ask for this is, the data is large and may be downloaded over a paid-per-megabyte connection but a dictionary is also essential to type comfortably, so we ask the user. This only pops in selected cases, when there is no dictionary at all currently, and the only available network seems to be metered. The "Language & input" part should be set to the actual name of the option (message ID 5292716747264442359 in the translation console). [CHAR_LIMIT=700] -->
    <string name="should_download_over_metered_prompt">The selected language on your mobile device has an available dictionary.&lt;br/>
We recommend &lt;b>downloading&lt;/b> the <xliff:g id="LANGUAGE_NAME" example="English">%1$s</xliff:g> dictionary to improve your typing experience.&lt;br/>
&lt;br/>
The download could take a minute or two over 3G. Charges may apply if you don\'t have an &lt;b>unlimited data plan&lt;/b>.&lt;br/>
If you are not sure which data plan you have, we recommend finding a Wi-Fi connection to start the download automatically.&lt;br/>
&lt;br/>
Tip: You can download and remove dictionaries by going to &lt;b>Language &amp; input&lt;/b> in the &lt;b>Settings&lt;/b> menu of your mobile device.</string>
    <string name="download_over_metered">Download now (<xliff:g id="SIZE_IN_MEGABYTES" example="0.7">%1$.1f</xliff:g>MB)</string>
    <string name="do_not_download_over_metered">Download over Wi-Fi</string>
    <!-- The text of the "dictionary available" notification. -->
    <string name="dict_available_notification_title">A dictionary is available for <xliff:g id="LANGUAGE_NAME" example="English">%1$s</xliff:g></string>
    <!-- The small subtext in the "dictionary available" notification. -->
    <string name="dict_available_notification_description">Press to review and download</string>

    <!-- The text of the toast warning a download is starting automatically to enable suggestions for the selected language [CHAR LIMIT=100] -->
    <string name="toast_downloading_suggestions">Downloading: suggestions for <xliff:g id="LANGUAGE_NAME" example="English">%1$s</xliff:g> will be ready soon.</string>

    <!-- Version text [CHAR LIMIT=30]-->
    <string name="version_text">Version <xliff:g id="VERSION_NUMBER" example="1.0.1864.643521">%1$s</xliff:g></string>

    <!-- User dictionary settings -->
    <!-- User dictionary settings.  The summary of the listem item to go into the User dictionary settings screen. -->
    <string name="user_dict_settings_summary" translatable="false">""</string>
    <!-- User dictionary settings. The title of the menu item to add a new word to the user dictionary. -->
    <!-- This resource is corresponding to  msgid="4056762757149923551" -->
    <string name="user_dict_settings_add_menu_title">Add</string>
    <!-- User dictionary settings. The title of the dialog to add a new word to the user dictionary. [CHAR LIMIT=25] -->
    <!-- This resource is corresponding to msgid="4702613990174126482" -->
    <string name="user_dict_settings_add_dialog_title">Add to dictionary</string>
    <!-- User dictionary settings. The title of the screen to add/edit a new word to the user dictionary; it describes the phrase that will be added to the user dictionary. [CHAR LIMIT=25] -->
    <!-- This resource is corresponding to msgid="742580720124344291" -->
    <string name="user_dict_settings_add_screen_title">Phrase</string>
    <!-- User dictionary settings. Text on the dialog button to pop more options for adding a word. [CHAR LIMIT=16] -->
    <!-- This resource is corresponding to msgid="8848798370746019825" -->
    <string name="user_dict_settings_add_dialog_more_options">More options</string>
    <!-- User dictionary settings. Text on the dialog button mask advanced options. [CHAR LIMIT=15] -->
    <!-- This resource is corresponding to msgid="2441785268726036101" -->
    <string name="user_dict_settings_add_dialog_less_options">Less options</string>
    <!-- User dictionary settings. Text on the dialog button to confirm adding a word. [CHAR LIMIT=15] -->
    <!-- This resource is corresponding to msgid="6225823625332416144" -->
    <string name="user_dict_settings_add_dialog_confirm">OK</string>
    <!-- User dictionary settings. Label to put before the word field (that's the word that will actually be added to the user dictionary when OK is pressed). [CHAR LIMIT=20] -->
    <!-- This resource is corresponding to msgid="7868879174905963135" -->
    <string name="user_dict_settings_add_word_option_name">Word:</string>
    <!-- User dictionary settings. Label to put before the shortcut field (once a shortcut is registered, the user can type the shortcut and get the word it points to in the suggestions). [CHAR LIMIT=20] -->
    <!-- This resource is corresponding to msgid="660089258866063925" -->
    <string name="user_dict_settings_add_shortcut_option_name">Shortcut:</string>
    <!-- User dictionary settings. Label to put before the language field. [CHAR LIMIT=20] -->
    <!-- This resource is corresponding to msgid="5696358317061318532" -->
    <string name="user_dict_settings_add_locale_option_name">Language:</string>
    <!-- User dictionary settings. Hint for the text field to type the word to add to the user dictionary. [CHAR LIMIT=35] -->
    <!-- This resource is corresponding to msgid="5725254076556821247" -->
    <string name="user_dict_settings_add_word_hint">Type a word</string>
    <!-- User dictionary settings. Hint for the text field to type the optional shortcut to add to the user dictionary. [CHAR LIMIT=35] -->
    <!-- This resource is corresponding to msgid="7333763456561873445" -->
    <string name="user_dict_settings_add_shortcut_hint">Optional shortcut</string>
    <!-- User dictionary settings. The title of the dialog to edit an existing word in the user dictionary. -->
    <!-- This resource is corresponding to msgid="8967476444840548674" -->
    <string name="user_dict_settings_edit_dialog_title">Edit word</string>
    <!-- User dictionary settings. The title of the context menu item to edit the current word -->
    <!-- This resource is corresponding to msgid="2210564879320004837" -->
    <string name="user_dict_settings_context_menu_edit_title">Edit</string>
    <!-- User dictionary settings. The title of the context menu item to delete the current word -->
    <!-- This resource is corresponding to msgid="9140703913776549054" -->
    <string name="user_dict_settings_context_menu_delete_title">Delete</string>
    <!-- User dictionary settings. The text to show when there are no user-defined words in the dictionary  [CHAR LIMIT=200] -->
    <!-- This resource is corresponding to msgid="8165273379942105271" -->
    <string name="user_dict_settings_empty_text">You don\'t have any words in the user dictionary. Add a word by touching the Add (+) button.</string>
    <!-- User dictionary settings. The list item to choose to insert a word into the user dictionary for all languages -->
    <!-- This resource is corresponding to msgid="6742000040975959247" -->
    <string name="user_dict_settings_all_languages">For all languages</string>
    <!-- User dictionary settings. The text to show for the option that shows the entire list of supported locales to choose one [CHAR LIMIT=30] -->
    <!-- This resource is corresponding to msgid="7316375944684977910" -->
    <string name="user_dict_settings_more_languages">More languages…</string>
    <!-- User dictionary settings. Label to delete an entry in the user dictionary [CHAR LIMIT=30]
         This resource is copied from packages/apps/Settings/res/values/strings.xml -->
    <!-- This resource is corresponding to msgid="4219243412325163003" -->
    <string name="user_dict_settings_delete">Delete</string>
    <!-- User dictionary settings. Index of the user dictionary [CHAR LIMIT=30]
         This resource is copied from packages/apps/Settings/res/values/strings.xml -->
    <!-- This resource is corresponding to msgid="5433275485499039199" -->
    <string name="user_dict_fast_scroll_alphabet">\u0020ABCDEFGHIJKLMNOPQRSTUVWXYZ</string>
</resources>
