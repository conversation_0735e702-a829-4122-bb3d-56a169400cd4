<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted">
            <!-- U+1025: "ဥ" MYANMAR LETTER U -->
            <Key latin:keySpec="&#x1025;" />
            <!-- U+1026: "ဦ" MYANMAR LETTER UU -->
            <Key latin:keySpec="&#x1026;" />
            <!-- U+100C: "ဌ" MYANMAR LETTER TTHA -->
            <Key latin:keySpec="&#x100C;" />
            <!-- U+100B: "ဋ" MYANMAR LETTER TTA -->
            <Key latin:keySpec="&#x100B;" />
            <!-- U+100D: "ဍ" MYANMAR LETTER DDA -->
            <Key latin:keySpec="&#x100D;" />
            <!-- U+1020: "ဠ" MYANMAR LETTER LLA -->
            <Key latin:keySpec="&#x1020;" />
            <!-- U+100B/U+1039/U+100C: "ဋ္ဌ" MYANMAR LETTER TTA/MYANMAR SIGN VIRAMA/MYANMAR LETTER TTHA -->
            <Key
                latin:keySpec="&#x100B;&#x1039;&#x100C;"
                latin:keyLabelFlags="followKeyLetterRatio" />
            <!-- U+100F/U+1039/U+100D: "ဏ္ဍ" MYANMAR LETTER NNA/MYANMAR SIGN VIRAMA/MYANMAR LETTER DDA
                 U+100F/U+1039/U+100C: "ဏ္ဌ" MYANMAR LETTER NNA/MYANMAR SIGN VIRAMA/MYANMAR LETTER TTHA -->
            <Key
                latin:keySpec="&#x100F;&#x1039;&#x100D;"
                latin:moreKeys="&#x100F;&#x1039;&#x100C;"
                latin:keyLabelFlags="followKeyLetterRatio" />
        </case>
        <default>
            <!-- U+1016: "ဖ" MYANMAR LETTER PHA -->
            <Key latin:keySpec="&#x1016;" />
            <!-- U+1011: "ထ" MYANMAR LETTER THA
                 U+1039/U+1011: "္ထ" MYANMAR SIGN VIRAMA/MYANMAR LETTER THA -->
            <Key
                latin:keySpec="&#x1011;"
                latin:moreKeys="&#x1039;&#x1011;" />
            <!-- U+1001: "ခ" MYANMAR LETTER KHA
                 U+1039/U+1001: "္ခ" MYANMAR SIGN VIRAMA/MYANMAR LETTER KHA -->
            <Key
                latin:keySpec="&#x1001;"
                latin:moreKeys="&#x1039;&#x1001;" />
            <!-- U+101C: "လ" MYANMAR LETTER LA
                 U+1039/U+101C: "္လ" MYANMAR SIGN VIRAMA/MYANMAR LETTER LA -->
            <Key
                latin:keySpec="&#x101C;"
                latin:moreKeys="&#x1039;&#x101C;" />
            <!-- U+1018: "ဘ" MYANMAR LETTER BHA
                 U+1039/U+1018: "္ဘ" MYANMAR SIGN VIRAMA/MYANMAR LETTER BHA -->
            <Key
                latin:keySpec="&#x1018;"
                latin:moreKeys="&#x1039;&#x1018;" />
            <!-- U+100A: "ည" MYANMAR LETTER NNYA
                 U+1009: "ဉ" MYANMAR LETTER NYA -->
            <Key
                latin:keySpec="&#x100A;"
                latin:moreKeys="&#x1009;" />
            <!-- U+101B: "ရ" MYANMAR LETTER RA -->
            <Key latin:keySpec="&#x101B;" />
            <!-- U+101D: "ဝ" MYANMAR LETTER WA -->
            <Key latin:keySpec="&#x101D;" />
        </default>
    </switch>
</merge>
