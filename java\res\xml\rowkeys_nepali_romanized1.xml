<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted">
            <!-- U+0920: "ठ" DEVANAGARI LETTER TTHA -->
            <Key latin:keySpec="&#x0920;" />
            <!-- U+0914: "औ" DEVANAGARI LETTER AU -->
            <Key latin:keySpec="&#x0914;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_ai" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignAi" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_vocalic_r" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignVocalicR" />
            <!-- U+0925: "थ" DEVANAGARI LETTER THA -->
            <Key latin:keySpec="&#x0925;" />
            <!-- U+091E: "ञ" DEVANAGARI LETTER NYA -->
            <Key latin:keySpec="&#x091E;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_uu" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignUu" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_ii" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignIi" />
            <!-- U+0913: "ओ" DEVANAGARI LETTER O -->
            <Key latin:keySpec="&#x0913;" />
            <!-- U+092B: "फ" DEVANAGARI LETTER PHA -->
            <Key latin:keySpec="&#x092B;" />
            <!-- U+0908: "ई" DEVANAGARI LETTER II -->
            <Key latin:keySpec="&#x0908;" />
        </case>
        <default>
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_nukta" />
            <!-- U+091F: "ट" DEVANAGARI LETTER TTA
                 U+0967: "१" DEVANAGARI DIGIT ONE -->
            <Key
                latin:keySpec="&#x091F;"
                latin:keyHintLabel="1"
                latin:additionalMoreKeys="&#x0967;,1"
                latin:keyStyle="moreKeysDevanagariSignNukta" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_au" />
            <!-- U+0968: "२" DEVANAGARI DIGIT TWO -->
            <Key
                latin:keyStyle="baseKeyDevanagariVowelSignAu"
                latin:keyHintLabel="2"
                latin:additionalMoreKeys="&#x0968;,2" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_e" />
            <!-- U+0969: "३" DEVANAGARI DIGIT THREE -->
            <Key
                latin:keyStyle="baseKeyDevanagariVowelSignE"
                latin:keyHintLabel="3"
                latin:additionalMoreKeys="&#x0969;,3" />
            <!-- U+0930: "र" DEVANAGARI LETTER RA
                 U+096A: "४" DEVANAGARI DIGIT FOUR -->
            <Key
                latin:keySpec="&#x0930;"
                latin:keyHintLabel="4"
                latin:additionalMoreKeys="&#x096A;,4" />
            <!-- U+0924: "त" DEVANAGARI LETTER TA
                 U+096B: "५" DEVANAGARI DIGIT FIVE -->
            <Key
                latin:keySpec="&#x0924;"
                latin:keyHintLabel="5"
                latin:additionalMoreKeys="&#x096B;,5" />
            <!-- U+092F: "य" DEVANAGARI LETTER YA
                 U+096C: "६" DEVANAGARI DIGIT SIX -->
            <Key
                latin:keySpec="&#x092F;"
                latin:keyHintLabel="6"
                latin:additionalMoreKeys="&#x096C;,6" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_u" />
            <!-- U+096D: "७" DEVANAGARI DIGIT SEVEN -->
            <Key
                latin:keyStyle="baseKeyDevanagariVowelSignU"
                latin:keyHintLabel="7"
                latin:additionalMoreKeys="&#x096D;,7" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_i" />
            <!-- U+096E: "८" DEVANAGARI DIGIT EIGHT -->
            <Key
                latin:keyStyle="baseKeyDevanagariVowelSignI"
                latin:keyHintLabel="8"
                latin:additionalMoreKeys="&#x096E;,8" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_o" />
            <!-- U+096F: "९" DEVANAGARI DIGIT NINE -->
            <Key
                latin:keyStyle="baseKeyDevanagariVowelSignO"
                latin:keyHintLabel="9"
                latin:additionalMoreKeys="&#x096F;,9" />
            <!-- U+092A: "प" DEVANAGARI LETTER PA
                 U+0966: "०" DEVANAGARI DIGIT ZERO -->
            <Key
                latin:keySpec="&#x092A;"
                latin:keyHintLabel="0"
                latin:additionalMoreKeys="&#x0966;,0" />
            <!-- U+0907: "इ" DEVANAGARI LETTER I -->
            <Key latin:keySpec="&#x0907;" />
        </default>
    </switch>
</merge>
