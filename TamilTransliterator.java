import java.util.*;
import java.util.regex.Pattern;

/**
 * Standalone Tamil Transliteration Engine
 * Converts English phonetic input to Tamil script
 * Based on the logic from Indic Keyboard project
 */
public class TamilTransliterator {
    
    // Tamil Unicode ranges
    private static final char TAMIL_VOWEL_A = '\u0B85';  // அ
    private static final char TAMIL_CONSONANT_KA = '\u0B95'; // க
    
    // Vowel mappings
    private static final Map<String, String> VOWEL_MAP = new HashMap<>();
    
    // Consonant mappings  
    private static final Map<String, String> CONSONANT_MAP = new HashMap<>();
    
    // Vowel sign mappings (for combining with consonants)
    private static final Map<String, String> VOWEL_SIGN_MAP = new HashMap<>();
    
    // Complex combinations
    private static final Map<String, String> COMPLEX_MAP = new HashMap<>();
    
    static {
        initializeVowelMappings();
        initializeConsonantMappings();
        initializeVowelSignMappings();
        initializeComplexMappings();
    }
    
    private static void initializeVowelMappings() {
        VOWEL_MAP.put("a", "அ");    // U+0B85
        VOWEL_MAP.put("aa", "ஆ");   // U+0B86
        VOWEL_MAP.put("i", "இ");    // U+0B87
        VOWEL_MAP.put("ii", "ஈ");   // U+0B88
        VOWEL_MAP.put("u", "உ");    // U+0B89
        VOWEL_MAP.put("uu", "ஊ");   // U+0B8A
        VOWEL_MAP.put("e", "எ");    // U+0B8E
        VOWEL_MAP.put("ee", "ஏ");   // U+0B8F
        VOWEL_MAP.put("ai", "ஐ");   // U+0B90
        VOWEL_MAP.put("o", "ஒ");    // U+0B92
        VOWEL_MAP.put("oo", "ஓ");   // U+0B93
        VOWEL_MAP.put("au", "ஔ");   // U+0B94
    }
    
    private static void initializeConsonantMappings() {
        // Consonants (with inherent 'a' sound)
        CONSONANT_MAP.put("k", "க");    // U+0B95
        CONSONANT_MAP.put("ng", "ங");   // U+0B99
        CONSONANT_MAP.put("ch", "ச");   // U+0B9A
        CONSONANT_MAP.put("nj", "ஞ");   // U+0B9E
        CONSONANT_MAP.put("t", "ட");    // U+0B9F
        CONSONANT_MAP.put("n", "ந");    // U+0BA8
        CONSONANT_MAP.put("th", "த");   // U+0BA4
        CONSONANT_MAP.put("p", "ப");    // U+0BAA
        CONSONANT_MAP.put("m", "ம");    // U+0BAE
        CONSONANT_MAP.put("y", "ய");    // U+0BAF
        CONSONANT_MAP.put("r", "ர");    // U+0BB0
        CONSONANT_MAP.put("l", "ல");    // U+0BB2
        CONSONANT_MAP.put("v", "வ");    // U+0BB5
        CONSONANT_MAP.put("zh", "ழ");   // U+0BB4
        CONSONANT_MAP.put("ll", "ள");   // U+0BB3
        CONSONANT_MAP.put("rr", "ற");   // U+0BB1
        CONSONANT_MAP.put("nn", "ண");   // U+0BA3
        CONSONANT_MAP.put("s", "ஸ");    // U+0BB8
        CONSONANT_MAP.put("sh", "ஷ");   // U+0BB7
        CONSONANT_MAP.put("h", "ஹ");    // U+0BB9
        CONSONANT_MAP.put("j", "ஜ");    // U+0B9C
        CONSONANT_MAP.put("g", "க");    // Map g to k (common in Tamil)
        CONSONANT_MAP.put("d", "ட");    // Map d to t (common in Tamil)
        CONSONANT_MAP.put("b", "ப");    // Map b to p (common in Tamil)
    }
    
    private static void initializeVowelSignMappings() {
        // Vowel signs (matras) to combine with consonants
        VOWEL_SIGN_MAP.put("aa", "ா");   // U+0BBE
        VOWEL_SIGN_MAP.put("i", "ி");    // U+0BBF
        VOWEL_SIGN_MAP.put("ii", "ீ");   // U+0BC0
        VOWEL_SIGN_MAP.put("u", "ு");    // U+0BC1
        VOWEL_SIGN_MAP.put("uu", "ூ");   // U+0BC2
        VOWEL_SIGN_MAP.put("e", "ெ");    // U+0BC6
        VOWEL_SIGN_MAP.put("ee", "ே");   // U+0BC7
        VOWEL_SIGN_MAP.put("ai", "ை");   // U+0BC8
        VOWEL_SIGN_MAP.put("o", "ொ");    // U+0BCA
        VOWEL_SIGN_MAP.put("oo", "ோ");   // U+0BCB
        VOWEL_SIGN_MAP.put("au", "ௌ");   // U+0BCC
    }
    
    private static void initializeComplexMappings() {
        // Common Tamil words and combinations
        COMPLEX_MAP.put("vanakkam", "வணக்கம்");
        COMPLEX_MAP.put("nandri", "நன்றி");
        COMPLEX_MAP.put("tamil", "தமிழ்");
        COMPLEX_MAP.put("amma", "அம்மா");
        COMPLEX_MAP.put("appa", "அப்பா");
        COMPLEX_MAP.put("anna", "அண்ணா");
        COMPLEX_MAP.put("akka", "அக்கா");
        COMPLEX_MAP.put("thambi", "தம்பி");
        COMPLEX_MAP.put("thangai", "தங்கை");
        COMPLEX_MAP.put("naan", "நான்");
        COMPLEX_MAP.put("neenga", "நீங்க");
        COMPLEX_MAP.put("enna", "என்ன");
        COMPLEX_MAP.put("eppo", "எப்போ");
        COMPLEX_MAP.put("enga", "எங்க");
        COMPLEX_MAP.put("eppadi", "எப்படி");
        COMPLEX_MAP.put("sari", "சரி");
        COMPLEX_MAP.put("illa", "இல்ல");
        COMPLEX_MAP.put("irukku", "இருக்கு");
        COMPLEX_MAP.put("varen", "வரேன்");
        COMPLEX_MAP.put("poren", "போறேன்");
    }
    
    private int maxKeyLength = 8; // Maximum length to look back for transliteration
    
    /**
     * Main transliteration method - converts English input to Tamil
     */
    public String transliterate(String input, String context, boolean isComplete) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        
        String lowerInput = input.toLowerCase();
        
        // Check for exact complex word matches first
        if (COMPLEX_MAP.containsKey(lowerInput)) {
            return COMPLEX_MAP.get(lowerInput);
        }
        
        // Try progressive transliteration
        return progressiveTransliterate(lowerInput);
    }
    
    /**
     * Progressive transliteration that builds Tamil text character by character
     */
    private String progressiveTransliterate(String input) {
        StringBuilder result = new StringBuilder();
        int i = 0;
        
        while (i < input.length()) {
            String match = findLongestMatch(input, i);
            if (match != null) {
                result.append(match);
                i += getLengthOfMatchedInput(input, i, match);
            } else {
                // If no match found, append the character as-is
                result.append(input.charAt(i));
                i++;
            }
        }
        
        return result.toString();
    }
    
    /**
     * Find the longest possible Tamil match starting from position i
     */
    private String findLongestMatch(String input, int startPos) {
        // Try different lengths, starting from longest possible
        for (int len = Math.min(maxKeyLength, input.length() - startPos); len > 0; len--) {
            String substring = input.substring(startPos, startPos + len);
            
            // Check complex mappings first
            if (COMPLEX_MAP.containsKey(substring)) {
                return COMPLEX_MAP.get(substring);
            }
            
            // Try consonant + vowel combinations
            String consonantVowelMatch = tryConsonantVowelCombination(substring);
            if (consonantVowelMatch != null) {
                return consonantVowelMatch;
            }
            
            // Try standalone vowels
            if (VOWEL_MAP.containsKey(substring)) {
                return VOWEL_MAP.get(substring);
            }
            
            // Try standalone consonants
            if (CONSONANT_MAP.containsKey(substring)) {
                return CONSONANT_MAP.get(substring);
            }
        }
        
        return null;
    }
    
    /**
     * Try to match consonant + vowel combinations
     */
    private String tryConsonantVowelCombination(String input) {
        // Try different splits of consonant + vowel
        for (int split = 1; split < input.length(); split++) {
            String consonantPart = input.substring(0, split);
            String vowelPart = input.substring(split);
            
            if (CONSONANT_MAP.containsKey(consonantPart) && VOWEL_SIGN_MAP.containsKey(vowelPart)) {
                String consonant = CONSONANT_MAP.get(consonantPart);
                String vowelSign = VOWEL_SIGN_MAP.get(vowelPart);
                
                // Remove inherent 'a' from consonant if adding vowel sign
                if (consonant.length() > 0) {
                    consonant = consonant + "்"; // Add virama to remove inherent vowel
                    consonant = consonant.substring(0, consonant.length() - 1); // Remove virama, just get base
                }
                
                return consonant + vowelSign;
            }
        }
        
        return null;
    }
    
    /**
     * Get the length of input that was matched for a given Tamil output
     */
    private int getLengthOfMatchedInput(String input, int startPos, String tamilMatch) {
        // Try to find which input length produced this Tamil match
        for (int len = 1; len <= Math.min(maxKeyLength, input.length() - startPos); len++) {
            String testInput = input.substring(startPos, startPos + len);
            
            if (COMPLEX_MAP.containsKey(testInput) && COMPLEX_MAP.get(testInput).equals(tamilMatch)) {
                return len;
            }
            
            if (VOWEL_MAP.containsKey(testInput) && VOWEL_MAP.get(testInput).equals(tamilMatch)) {
                return len;
            }
            
            if (CONSONANT_MAP.containsKey(testInput) && CONSONANT_MAP.get(testInput).equals(tamilMatch)) {
                return len;
            }
            
            // Check consonant+vowel combinations
            String consonantVowelMatch = tryConsonantVowelCombination(testInput);
            if (consonantVowelMatch != null && consonantVowelMatch.equals(tamilMatch)) {
                return len;
            }
        }
        
        return 1; // Default to consuming one character
    }
    
    public int getMaxKeyLength() {
        return maxKeyLength;
    }
    
    /**
     * Test method to demonstrate transliteration
     */
    public static void main(String[] args) {
        TamilTransliterator transliterator = new TamilTransliterator();
        
        // Test words
        String[] testWords = {
            "vanakkam",
            "nandri", 
            "tamil",
            "amma",
            "appa",
            "ka",
            "ki",
            "ku",
            "ma",
            "mi",
            "mu",
            "hello",
            "naan",
            "neenga",
            "sari",
            "illa"
        };
        
        System.out.println("Tamil Transliteration Test");
        System.out.println("==========================");
        
        for (String word : testWords) {
            String result = transliterator.transliterate(word, "", false);
            System.out.printf("%-12s -> %s%n", word, result);
        }
        
        // Interactive mode
        Scanner scanner = new Scanner(System.in);
        System.out.println("\nEnter words to transliterate (type 'exit' to quit):");
        
        while (true) {
            System.out.print("English: ");
            String input = scanner.nextLine().trim();
            
            if ("exit".equalsIgnoreCase(input)) {
                break;
            }
            
            if (!input.isEmpty()) {
                String result = transliterator.transliterate(input, "", false);
                System.out.println("Tamil: " + result);
            }
        }
        
        scanner.close();
        System.out.println("Goodbye!");
    }
}
