<?xml version="1.0"?>
<inputmethod id="te-transliteration" name="లిప్యంతరీకరణ" description="Telugu Transliteration based on RTS" author="<PERSON><PERSON><PERSON> and Junaid P V" version="1.0" contextLength="1" maxKeyLength="4">
  <pattern input="(మ్|న్)b" replacement="ంబ్"/>
  <pattern input="(మ్|న్)B" replacement="ంభ్"/>
  <pattern input="(మ్|న్)c" replacement="ంచ్"/>
  <pattern input="(మ్|న్)C" replacement="ంఛ్"/>
  <pattern input="(మ్|న్)d" replacement="ంద్"/>
  <pattern input="(మ్|న్)D" replacement="ండ్"/>
  <pattern input="(మ్|న్)[fFP]" replacement="ంఫ్"/>
  <pattern input="(మ్|న్)g" replacement="ంగ్"/>
  <pattern input="(మ్|న్)h" replacement="ంహ్"/>
  <pattern input="(మ్|న్)j" replacement="ంజ్"/>
  <pattern input="(మ్|న్)J" replacement="ంఝ్"/>
  <pattern input="(మ్|న్)k" replacement="ంక్"/>
  <pattern input="(మ్|న్)L" replacement="ంళ్"/>
  <pattern input="(మ్|న్)p" replacement="ంప్"/>
  <pattern input="(మ్|న్)r" replacement="ంర్"/>
  <pattern input="(మ్|న్)s" replacement="ంస్"/>
  <pattern input="(మ్|న్)t" replacement="ంత్"/>
  <pattern input="(మ్|న్)T" replacement="ంట్"/>
  <pattern input="(మ్|న్)[vwVW]" replacement="ంవ్"/>
  <pattern input="(మ్|న్)[xX]" replacement="ంక్ష్"/>
  <pattern input="(మ్|న్)&amp;b" replacement="$1బ్"/>
  <pattern input="(మ్|న్)&amp;B" replacement="$1బ్"/>
  <pattern input="(మ్|న్)&amp;c" replacement="$1చ్"/>
  <pattern input="(మ్|న్)&amp;C" replacement="$1ఛ్"/>
  <pattern input="(మ్|న్)&amp;d" replacement="$1ద్"/>
  <pattern input="(మ్|న్)&amp;D" replacement="$1డ్"/>
  <pattern input="(మ్|న్)&amp;[fFP]" replacement="$1ఫ్"/>
  <pattern input="(మ్|న్)&amp;g" replacement="$1గ్"/>
  <pattern input="(మ్|న్)&amp;h" replacement="$1హ్"/>
  <pattern input="(మ్|న్)&amp;j" replacement="$1జ్"/>
  <pattern input="(మ్|న్)&amp;J" replacement="$1ఝ్"/>
  <pattern input="(మ్|న్)&amp;k" replacement="$1క్"/>
  <pattern input="(మ్|న్)&amp;L" replacement="$1ళ్"/>
  <pattern input="(మ్|న్)&amp;p" replacement="$1ప్"/>
  <pattern input="(మ్|న్)&amp;r" replacement="$1ర్"/>
  <pattern input="(మ్|న్)&amp;s" replacement="$1స్"/>
  <pattern input="(మ్|న్)&amp;t" replacement="$1త్"/>
  <pattern input="(మ్|న్)&amp;T" replacement="$1ట్"/>
  <pattern input="(మ్|న్)&amp;[vwVW]" replacement="$1వ్"/>
  <pattern input="(మ్|న్)&amp;[xX]" replacement="$1క్ష్"/>
  <pattern input="(మ్|న్)&amp;(\s|\.|\, |\))" replacement="$1$2"/>
  <pattern input="([క-హ]|ౘ|ౙ)్a" replacement="$1"/>
  <pattern input="([క-హ])(్A|a|&apos;)" replacement="$1ా"/>
  <pattern input="([క-హ])్i" replacement="$1ి"/>
  <pattern input="([క-హ])(్I|ిi|ి&apos;|ెe)" replacement="$1ీ"/>
  <pattern input="([క-హ])్u" replacement="$1ు"/>
  <pattern input="([క-హ])(ుu|ు&apos;|ుa|్U)" replacement="$1ూ"/>
  <pattern input="([క-హ])ొo" context="o" replacement="$1ూ"/>
  <pattern input="([క-హ])్R" replacement="$1ృ"/>
  <pattern input="([క-హ])్ర్&apos;" replacement="$1ృ"/>
  <pattern input="([క-హ])ృu" replacement="$1ౄ"/>
  <pattern input="([క-హ])్~l" replacement="$1ౢ"/>
  <pattern input="([క-హ])ౢ(l|&apos;)" replacement="$1ౣ"/>
  <pattern input="([క-హ])్e" replacement="$1ె"/>
  <pattern input="([క-హ])(్E|ెa|ె&apos;)" replacement="$1ే"/>
  <pattern input="([క-హ])e" context="a" replacement="$1ే"/>
  <pattern input="([క-హ])ెi" replacement="$1ై"/>
  <pattern input="([క-హ])i" context="a" replacement="$1ై"/>
  <pattern input="([క-హ])్o" replacement="$1ొ"/>
  <pattern input="([క-హ])(్O|ొa|ొ&apos;)" replacement="$1ో"/>
  <pattern input="([క-హ])(ొu|ొw)" replacement="$1ౌ"/>
  <pattern input="([క-హ])u" context="a" replacement="$1ౌ"/>
  <pattern input="(్)\^" replacement="$1‍"/>
  <pattern input="మ్(\s|\.|\, |\))" replacement="ం$1"/>
  <pattern input="అ(a|&apos;)" replacement="ఆ"/>
  <pattern input="ఇ(i|&apos;)" replacement="ఈ"/>
  <pattern input="ఎe" replacement="ఈ"/>
  <pattern input="ఉ(u|&apos;)" replacement="ఊ"/>
  <pattern input="ఒo" replacement="ఊ"/>
  <pattern input="ఎ(a|&apos;)" replacement="ఏ"/>
  <pattern input="అe" context="a" replacement="ఏ"/>
  <pattern input="ఎi" replacement="ఐ"/>
  <pattern input="అi" context="a" replacement="ఐ"/>
  <pattern input="ఒ(a|&apos;)" context="" replacement="ఓ"/>
  <pattern input="అo" context="a" replacement="ఓ"/>
  <pattern input="ఒ(u|w)" replacement="ఔ"/>
  <pattern input="అu" context="a" replacement="ఔ"/>
  <pattern input="ఋ(u|&apos;)" replacement="ౠ"/>
  <pattern input="ర్&apos;" replacement="ఋ"/>
  <pattern input="క్h" replacement="ఖ్"/>
  <pattern input="గ్h" replacement="ఘ్"/>
  <pattern input="జ్h" replacement="ఝ్"/>
  <pattern input="చ్&apos;" replacement="ఛ్"/>
  <pattern input="చ్h" replacement="చ్"/>
  <pattern input="స్h" replacement="ష్"/>
  <pattern input="స్&apos;" replacement="శ్"/>
  <pattern input="త్&apos;" replacement="ట్"/>
  <pattern input="ట్h" replacement="ఠ్"/>
  <pattern input="ద్&apos;" replacement="డ్"/>
  <pattern input="డ్h" replacement="ఢ్"/>
  <pattern input="న్&apos;" replacement="ణ్"/>
  <pattern input="త్h" replacement="థ్"/>
  <pattern input="ద్h" replacement="ధ్"/>
  <pattern input="ప్h" replacement="ఫ్"/>
  <pattern input="బ్h" replacement="భ్"/>
  <pattern input="ల్&apos;" replacement="ళ్"/>
  <pattern input="a" replacement="అ"/>
  <pattern input="b" replacement="బ్"/>
  <pattern input="~c" replacement="ౘ్"/>
  <pattern input="c" replacement="చ్"/>
  <pattern input="d" replacement="ద్"/>
  <pattern input="e" replacement="ఎ"/>
  <pattern input="(f|F|P)" replacement="ఫ్"/>
  <pattern input="g" replacement="గ్"/>
  <pattern input="@h" replacement="ః"/>
  <pattern input="h" replacement="హ్"/>
  <pattern input="i" replacement="ఇ"/>
  <pattern input="~j" replacement="ౙ్"/>
  <pattern input="j" replacement="జ్"/>
  <pattern input="k" replacement="క్"/>
  <pattern input="~l" replacement="ఌ"/>
  <pattern input="l" replacement="ల్"/>
  <pattern input="@m" replacement="ఁ"/>
  <pattern input="~m" replacement="ఙ"/>
  <pattern input="m" replacement="మ్"/>
  <pattern input="~n" replacement="ఞ"/>
  <pattern input="n" replacement="న్"/>
  <pattern input="o" replacement="ఒ"/>
  <pattern input="p" replacement="ప్"/>
  <pattern input="q" replacement="క్వ్"/>
  <pattern input="~r" replacement="ఱ్"/>
  <pattern input="r" replacement="ర్"/>
  <pattern input="s" replacement="స్"/>
  <pattern input="t" replacement="త్"/>
  <pattern input="u" replacement="ఉ"/>
  <pattern input="(v|w|V|W)" replacement="వ్"/>
  <pattern input="(x|X)" replacement="క్ష్"/>
  <pattern input="(y|Y)" replacement="య్"/>
  <pattern input="(z|Z)" replacement="జ్"/>
  <pattern input="A" replacement="ఆ"/>
  <pattern input="B" replacement="భ్"/>
  <pattern input="C" replacement="ఛ్"/>
  <pattern input="D" replacement="డ్"/>
  <pattern input="E" replacement="ఏ"/>
  <pattern input="G" replacement="ఘ్"/>
  <pattern input="H" replacement="ః"/>
  <pattern input="I" replacement="ఈ"/>
  <pattern input="J" replacement="ఝ్"/>
  <pattern input="K" replacement="ఖ్"/>
  <pattern input="L" replacement="ళ్"/>
  <pattern input="M" replacement="ం"/>
  <pattern input="N" replacement="ణ్"/>
  <pattern input="O" replacement="ఓ"/>
  <pattern input="Q" replacement="క్వ్"/>
  <pattern input="R" replacement="ఋ"/>
  <pattern input="S" replacement="శ్"/>
  <pattern input="T" replacement="ట్"/>
  <pattern input="U" replacement="ఊ"/>
  <pattern input="~0" replacement="౦"/>
  <pattern input="~1" replacement="౧"/>
  <pattern input="~2" replacement="౨"/>
  <pattern input="~3" replacement="౩"/>
  <pattern input="~4" replacement="౪"/>
  <pattern input="~5" replacement="౫"/>
  <pattern input="~6" replacement="౬"/>
  <pattern input="~7" replacement="౭"/>
  <pattern input="~8" replacement="౮"/>
  <pattern input="~9" replacement="౯"/>
  <pattern input="।~\." replacement="॥"/>
  <pattern input="~\." replacement="।"/>
  <pattern input="//" replacement="ఽ"/>
  <pattern input="~\$" replacement="₹"/>
</inputmethod>
