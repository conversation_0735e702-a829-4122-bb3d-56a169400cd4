<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<com.android.inputmethod.keyboard.emoji.EmojiPalettesView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:orientation="vertical"
    style="?attr/emojiPalettesViewStyle"
>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="@dimen/config_suggestions_strip_height"
        style="?attr/suggestionStripViewStyle"
    >
        <TabHost
            android:id="@+id/emoji_category_tabhost"
            android:layout_width="0dip"
            android:layout_weight="87.5"
            android:layout_height="match_parent"
        >
            <TabWidget
                android:id="@android:id/tabs"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:divider="@null" />
            <FrameLayout
                android:id="@android:id/tabcontent"
                android:layout_width="0dip"
                android:layout_height="0dip"
            >
                <!-- Empty placeholder that TabHost requires. But we don't use it to actually
                     display anything. We monitor the tab changes and change the ViewPager.
                     Similarly the ViewPager swipes are intercepted and passed to the TabHost. -->
                <View
                    android:id="@+id/emoji_keyboard_dummy"
                    android:layout_width="0dip"
                    android:layout_height="0dip"
                    android:visibility="gone" />
            </FrameLayout>
        </TabHost>
        <include layout="@layout/suggestion_divider" />
        <!-- TODO: Implement KeyView and replace this. -->
        <!-- Provide audio and haptic feedback by ourselves based on the keyboard settings.
             We just need to ignore the system's audio and haptic feedback settings. -->
        <ImageButton
            android:id="@+id/emoji_keyboard_delete"
            android:layout_width="0dip"
            android:layout_weight="12.5"
            android:layout_height="match_parent"
            android:hapticFeedbackEnabled="false"
            android:soundEffectsEnabled="false"
            android:contentDescription="@string/spoken_description_delete" />
    </LinearLayout>
    <android.support.v4.view.ViewPager
        android:id="@+id/emoji_keyboard_pager"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
    <com.android.inputmethod.keyboard.emoji.EmojiCategoryPageIndicatorView
        android:id="@+id/emoji_category_page_id_view"
        android:layout_width="match_parent"
        android:layout_height="2dip" />
    <LinearLayout
        android:id="@+id/emoji_action_bar"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="0dip"
        android:layout_weight="1"
    >
        <!-- TODO: Implement a KeyView and replace this. -->
        <!-- Provide audio and haptic feedback by ourselves based on the keyboard settings.
             We just need to ignore the system's audio and haptic feedback settings. -->
        <TextView
            android:id="@+id/emoji_keyboard_alphabet_left"
            android:layout_width="0dip"
            android:layout_weight="0.15"
            android:gravity="center"
            android:layout_height="match_parent"
            android:hapticFeedbackEnabled="false"
            android:soundEffectsEnabled="false" />
        <!-- TODO: Implement KeyView and replace this. -->
        <!-- Provide audio and haptic feedback by ourselves based on the keyboard settings.
             We just need to ignore the system's audio and haptic feedback settings. -->
        <RelativeLayout
            android:id="@+id/emoji_keyboard_space"
            android:layout_width="0dip"
            android:layout_weight="0.70"
            android:layout_height="match_parent"
            android:hapticFeedbackEnabled="false"
            android:soundEffectsEnabled="false"
            android:contentDescription="@string/spoken_description_space">
            <!-- WORKAROUND: Show the spacebar icon as a bacground of this View. -->
            <View
                android:id="@+id/emoji_keyboard_space_icon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:layout_centerInParent="true" />
        </RelativeLayout>
        <!-- TODO: Implement KeyView and replace this. -->
        <!-- Provide audio and haptic feedback by ourselves based on the keyboard settings.
             We just need to ignore the system's audio and haptic feedback settings. -->
        <TextView
            android:id="@+id/emoji_keyboard_alphabet_right"
            android:layout_width="0dip"
            android:layout_weight="0.15"
            android:gravity="center"
            android:layout_height="match_parent"
            android:hapticFeedbackEnabled="false"
            android:soundEffectsEnabled="false" />
    </LinearLayout>
</com.android.inputmethod.keyboard.emoji.EmojiPalettesView>
