<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright © 2013-2014 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC), International Centre for Free and Open Source Software (ICFOSS)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
 >
    <switch>
        <case
            latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted"
            >
            <!-- U+0C9F: "ಠ" kannada TTA
                 +0CA0: "ಠ   Kannada TTHA  -->
            <Key
                latin:keySpec="&#x0C20;"
                latin:keyLabelFlags="fontNormal" />
            <!-- U+0CA1: "ಡ" kannada DA
                 +0CA2: "ಢ"   Kannada DHA  -->
            <Key
                latin:keySpec="&#x0C22;"
                latin:keyLabelFlags="fontNormal" />
            <!-- E: matras, short E, long E
                 Vowels and Consonats  -->
            <Key
                latin:keySpec="&#x0C47;"
                latin:moreKeys="&#x0C0F;"
                latin:keyLabelFlags="fontNormal" />
            <!-- R: ra, vocalic R matra, vocalic R, RRA
                 Vowels and Consonats  -->
            <Key
                latin:keySpec="&#x0C43;"
                latin:moreKeys="&#x0C31;"
                latin:keyLabelFlags="fontNormal" />
            <!-- T: dental ta, tha -->
            <Key
                latin:keySpec="&#x0C25;"
                latin:keyLabelFlags="fontNormal" />
            <!-- // Y: ya, ai matra, ai -->
            <Key
                latin:keySpec="&#x0C48;"
                latin:keyLabelFlags="fontNormal" />
            <!-- // U: matras, u, uu -->
            <Key
                latin:keySpec="&#x0C42;"
                latin:moreKeys="&#x0C0A;"
                latin:keyLabelFlags="fontNormal" />
            <!-- // // I: matras, i, ii -->
            <Key
                latin:keySpec="&#x0C40;"
                latin:moreKeys="&#x0C08;"
                latin:keyLabelFlags="fontNormal" />
            <!--  // O: matras, short o, long o -->
            <Key
                latin:keySpec="&#x0C4B;"
                latin:moreKeys="&#x0C13;"
                latin:keyLabelFlags="fontNormal" />
            <!--  // O: P: pa, pha -->
            <Key
                latin:keySpec="&#x0C2B;"
                latin:keyLabelFlags="fontNormal" />
        </case>

        <default>
            <!-- U+0C9F: "ಠ" kannada TTA
                 +0CA0: "ಠ   Kannada TTHA  -->
            <Key
                latin:keySpec="&#x0C1F;"
                latin:keyLabelFlags="fontNormal" />
            <!-- U+0CA1: "ಡ" kannada DA
                 +0CA2: "ಢ"   Kannada DHA  -->
            <Key
                latin:keySpec="&#x0C21;"
                latin:keyLabelFlags="fontNormal" />
            <!-- E: matras, short E, long E
                 Vowels and Consonats  -->
            <Key
                latin:keySpec="&#x0C46;"
                latin:moreKeys="&#x0C0E;"
                latin:keyLabelFlags="fontNormal" />
            <!-- R: ra, vocalic R matra, vocalic R, RRA
                 Vowels and Consonats  -->
            <Key
                latin:keySpec="&#x0C30;"
                latin:moreKeys="&#x0C0B;"
                latin:keyLabelFlags="fontNormal" />
            <!-- T: dental ta, tha -->
            <Key
                latin:keySpec="&#x0C24;"
                latin:keyLabelFlags="fontNormal" />
            <!-- // Y: ya, ai matra, ai -->
            <Key
                latin:keySpec="&#x0C2F;"
                latin:moreKeys="&#x0C10;"
                latin:keyLabelFlags="fontNormal" />
            <!-- // U: matras, u, uu -->
            <Key
                latin:keySpec="&#x0C41;"
                latin:moreKeys="&#x0C09;"
                latin:keyLabelFlags="fontNormal" />
            <!-- // // I: matras, i, ii -->
            <Key
                latin:keySpec="&#x0C3F;"
                latin:moreKeys="&#x0C07;"
                latin:keyLabelFlags="fontNormal" />
            <!--  // O: matras, short o, long o -->
            <Key
                latin:keySpec="&#x0C4A;"
                latin:moreKeys="&#x0C12;"
                latin:keyLabelFlags="fontNormal" />
            <!--  // O: P: pa, pha -->
            <Key
                latin:keySpec="&#x0C2A;"
                latin:keyLabelFlags="fontNormal" />
        </default>
    </switch>
</merge>
