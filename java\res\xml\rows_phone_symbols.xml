<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2011, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <include
        latin:keyboardLayout="@xml/key_styles_common" />
    <include
        latin:keyboardLayout="@xml/key_styles_number" />
    <Row>
        <Key
            latin:keySpec="("
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keySpec="/"
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keySpec=")"
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keySpec="-"
            latin:moreKeys="+"
            latin:keyLabelFlags="hasPopupHint"
            latin:keyStyle="numFunctionalKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
    <Row>
        <Key
            latin:keySpec="N"
            latin:keyStyle="numKeyBaseStyle" />
        <!-- Pause is a comma. Check PhoneNumberUtils.java to see if this
            has changed. -->
        <Key
            latin:keyStyle="numPauseKeyStyle" />
        <Key
            latin:keySpec=","
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keySpec="."
            latin:keyStyle="numFunctionalKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
    <Row>
        <Key
            latin:keyStyle="numStarKeyStyle" />
        <!-- Wait is a semicolon. -->
        <Key
            latin:keyStyle="numWaitKeyStyle" />
        <Key
            latin:keySpec="\#"
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keyStyle="deleteKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
    <Row>
        <Key
            latin:keyStyle="numPhoneToNumericKeyStyle" />
        <Key
            latin:keySpec="+"
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keyStyle="numSpaceKeyStyle" />
        <Key
            latin:keyStyle="enterKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
</merge>
