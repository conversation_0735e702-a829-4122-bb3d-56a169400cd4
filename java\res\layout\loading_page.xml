<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2011, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:id="@+id/loading_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" >
  <ProgressBar android:id="@+id/loading_progress_bar"
               style="?android:attr/progressBarStyleLarge"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_centerInParent="true" />
  <TextView android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/loading_progress_bar"
            android:layout_centerHorizontal="true"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:text="@string/message_loading"
            android:paddingTop="4dip"
            android:singleLine="true" />
</RelativeLayout>
