<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2009, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- For backward compatibility.
         See {@link SettingsValues#needsToShowVoiceInputKey(SharedPreferences,Resources)} -->
    <string name="voice_mode_main">0</string>

    <!-- Subtype locale display name exceptions.
         For each exception, there should be related string resources for display name that may have
         explicit keyboard layout. The string resource name must be "subtype_<locale>" or
         "subtype_with_layout_<locale>. Please refer to strings.xml for these resources. -->
    <string-array name="subtype_locale_exception_keys">
        <item>en_US</item>
        <item>en_GB</item>
        <item>es_US</item>
        <item>mnw</item>
    </string-array>

    <!-- Generic subtype label -->
    <string name="subtype_generic">%s</string>

    <!-- Predefined keyboard layouts for additional subtype -->
    <string-array name="predefined_layouts">
        <item>qwerty</item>
    </string-array>
    <!-- Predefined keyboard layout display names -->
    <string-array name="predefined_layout_display_names">
        <item>English</item>
    </string-array>
    <!-- Description for generic subtype that has predefined layout.
         The string resource name must be "subtype_generic_<layout name>".
         The string resource with "No language" also must be added to strings.xml and the resource
         name must be "subtype_no_language_<layout name>" -->
    <string name="subtype_generic_qwerty">%s (QWERTY)</string>
    <string name="subtype_mnw">Mon</string>

    <!-- Description for Bulgarian (BDS) subtype. -->
    <string name="subtype_bulgarian_bds">%s (BDS)</string>

    <!-- Compatibility map from subtypeLocale:subtypeExtraValue to keyboardLayoutSet -->
    <string-array name="locale_and_extra_value_to_keyboard_layout_set_map">
        <item>en_US:TrySuppressingImeSwitcher,AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>qwerty</item>
        <item>en_GB:TrySuppressingImeSwitcher,AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>qwerty</item>
        <item>ar:SupportTouchPositionCorrection,EmojiCapable</item>
        <item>arabic</item>
        <item>cs:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>qwertz</item>
        <item>da:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>nordic</item>
        <item>de:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>qwertz</item>
        <item>es:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>spanish</item>
        <item>fi:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>nordic</item>
        <item>fr:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>azerty</item>
        <item>fr_CA:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>qwerty</item>
        <item>hr:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>qwertz</item>
        <item>hu:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>qwertz</item>
        <item>it:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>qwerty</item>
        <item>iw:SupportTouchPositionCorrection,EmojiCapable</item>
        <item>hebrew</item>
        <item>nb:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>nordic</item>
        <item>nl:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>qwerty</item>
        <item>pl:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>qwerty</item>
        <item>ru:SupportTouchPositionCorrection,EmojiCapable</item>
        <item>east_slavic</item>
        <item>sr:SupportTouchPositionCorrection,EmojiCapable</item>
        <item>south_slavic</item>
        <item>sv:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>nordic</item>
        <item>tr:AsciiCapable,SupportTouchPositionCorrection,EmojiCapable</item>
        <item>qwerty</item>
    </string-array>
</resources>
