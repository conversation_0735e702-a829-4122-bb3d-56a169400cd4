<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2011, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:passwordInput="true">
            <include latin:keyboardLayout="@xml/key_styles_currency_dollar" />
        </case>
        <!-- Countries using Euro currency, 23 countries as of November 2012.
              1. Andorra (ca_AD, ca_ES)
              2. Austria (de_AT)
              3. Belgium (nl_BE, fr_BE, de_BE)
              4. Cyprus (el_CY, tr_CY)
              5. Estonia (et_EE)
              6. Finland (fi_FI, sv_FI)
              7. France (fr_FR)
              8. Germany (de_DE)
              9. Greece (el_GR)
             10. Ireland (ga_IE, en_IE)
             11. Italy (it_IT)
             12. Kosovo (sq_XK, sr_XK)
             13. Luxembourg (lb_LU, fr_LU, de_LU)
             14. Malta (mt_MT, en_MT)
             15. Monaco (fr_MO)
             16. Montenegro (??_ME)
             17. Netherlands (nl_NL)
             18. Portugal (pt_PT)
             19. San Marino (it_SM)
             20. Slovakia (sk_SK)
             21. Slovenia (sl_SI)
             22. Spain (es_ES, ca_ES, eu_ES, gl_ES)
             23. Vatican City (it_VA) -->
        <case latin:countryCode="AD|AT|BE|CY|EE|FI|FR|DE|GR|IE|IT|XK|LU|MT|MO|ME|NL|PT|SM|SK|SI|ES|VA">
            <include latin:keyboardLayout="@xml/key_styles_currency_euro" />
        </case>
        <!-- Note: Some subtype locale may not have country code, and it it supposed to indicate the
             country where the language originally/mainly spoken. -->
        <!-- Though Denmark, Sweden and Turkey don't use Euro as their currency, having the Euro
             sign on the symbol keyboard might be useful.  Especially Danish krone (kr), Swedish
             krona (kr) and Turkish lira (TL) can be represented by usual alphabet letters. -->
        <!-- TODO: The currency sign of Turkish Lira was created in 2012 and assigned U+20BA for
             its unicode, although there is no font glyph for it as of November 2012. -->
        <!-- da: Denmark (da_DK)
             de: Germany (de_DE)
             es: Spain (es_ES)
             fi: Finland (fi_FI)
             fr: France(fr_FR)
             it: Italy (it_IT)
             nl: Netherlands (nl_NL)
             sk: Slovakia (sk_SK)
             sl: Slovenia (sl_SL)
             sv: Sweden (sv_SV)
             tr: Trukey (tr_TR) -->
        <case latin:localeCode="da|de|es|el|fi|fr|it|nl|sk|sl|sv|tr">
            <include latin:keyboardLayout="@xml/key_styles_currency_euro" />
        </case>
        <!-- ca: Catalan (Andorra, Spain)
             et: Estonian (Estonia)
             lb: Luxembougish (Luxembourg)
             mt: Maltese (Malta) -->
        <case latin:languageCode="ca|et|lb|mt">
            <include latin:keyboardLayout="@xml/key_styles_currency_euro" />
        </case>
        <!-- fa: Persian (Rial and Afgahni)
             hi: Hindi (Indian Rupee)
             iw: Hebrew (New Sheqel)
             lo: Lao (Kip)
             mn: Mongolian (Tugrik)
             ne: Nepali (Nepalese Rupee)
             th: Thai (Baht)
             uk: Ukrainian (Hryvnia)
             vi: Vietnamese (Dong)  -->
        <!-- TODO: The currency sign of Turkish Lira was created in 2012 and assigned U+20BA for
             its unicode, although there is no font glyph for it as of November 2012. -->
        <!-- TODO: The currency sign of Armenian Dram was created in 2012 and assigned U+058F for
             its unicode, although there is no font glyph for it as of September 2013. -->
        <!-- TODO: The currency sign of Russian Ruble was created in 2014 and assigned U+20BD for
             its unicode, although there is no font glyph for it as of August 2014. -->
        <case latin:languageCode="fa|hi|iw|lo|mn|ne|th|uk|vi|en">
            <include latin:keyboardLayout="@xml/key_styles_currency_generic" />
        </case>
        <!-- si_LK: Sinhala (Sri Lanka) (Sri Lanka Rupee)
             ta_LK: Tamil (Sri Lanka) (Sri Lanka Rupee) -->
        <case latin:countryCode="LK">
            <include latin:keyboardLayout="@xml/key_styles_currency_generic" />
        </case>
        <!-- bn_IN: Bengali (India) (Indian Rupee)
             en_IN: English (India) (Indian Rupee)
             kn_IN: Kannada (India) (Indian Rupee)
             ml_IN: Malayalam (India) (Indian Rupee)
             mr_IN: Marathi (India) (Indian Rupee)
             ta_IN: Tamil (India) (Indian Rupee)
             te_IN: Telugu (India) (Indian Rupee)
         -->
        <case latin:countryCode="IN">
            <!-- U+20B9: "₹" INDIAN RUPEE SIGN
                 U+00A3: "£" POUND SIGN
                 U+20AC: "€" EURO SIGN
                 U+00A2: "¢" CENT SIGN -->
            <key-style
                latin:styleName="currencyKeyStyle"
                latin:keySpec="&#x20B9;"
                latin:moreKeys="!text/morekeys_currency_generic" />
            <key-style
                latin:styleName="moreCurrency1KeyStyle"
                latin:keySpec="&#x00A3;" />
            <key-style
                latin:styleName="moreCurrency2KeyStyle"
                latin:keySpec="&#x20AC;" />
            <key-style
                latin:styleName="moreCurrency3KeyStyle"
                latin:keySpec="$"
                latin:moreKeys="&#x00A2;" />
            <key-style
                latin:styleName="moreCurrency4KeyStyle"
                latin:keySpec="&#x00A2;" />
        </case>
        <!-- GB: United Kingdom (Pound) -->
        <case latin:countryCode="GB">
            <!-- U+00A3: "£" POUND SIGN
                 U+20AC: "€" EURO SIGN
                 U+00A5: "¥" YEN SIGN
                 U+00A2: "¢" CENT SIGN
                 U+20B1: "₱" PESO SIGN -->
            <key-style
                latin:styleName="currencyKeyStyle"
                latin:keySpec="&#x00A3;"
                latin:moreKeys="&#x00A2;,$,&#x20AC;,&#x00A5;,&#x20B1;" />
            <key-style
                latin:styleName="moreCurrency1KeyStyle"
                latin:keySpec="&#x20AC;" />
            <key-style
                latin:styleName="moreCurrency2KeyStyle"
                latin:keySpec="&#x00A5;" />
            <key-style
                latin:styleName="moreCurrency3KeyStyle"
                latin:keySpec="$"
                latin:moreKeys="&#x00A2;" />
            <key-style
                latin:styleName="moreCurrency4KeyStyle"
                latin:keySpec="&#x00A2;" />
        </case>
        <!-- ar: Arabic (Dollar and Rial) -->
        <default>
            <include latin:keyboardLayout="@xml/key_styles_currency_dollar" />
        </default>
    </switch>
</merge>
