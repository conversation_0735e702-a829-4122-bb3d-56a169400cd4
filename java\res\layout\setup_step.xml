<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <TextView
        android:id="@+id/setup_step_title"
        style="@style/setupStepTitleStyle"
        android:paddingTop="@dimen/setup_step_vertical_padding"
        android:paddingBottom="@dimen/setup_step_vertical_padding" />
    <TextView
        android:id="@+id/setup_step_instruction"
        style="@style/setupStepInstructionStyle"
        android:paddingBottom="@dimen/setup_step_vertical_padding" />
    <TextView
        android:id="@+id/setup_step_action_label"
        style="@style/setupStepActionLabelStyle"
        android:layout_marginTop="@dimen/setup_step_horizontal_line_height" />
</LinearLayout>
