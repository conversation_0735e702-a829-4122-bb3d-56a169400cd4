<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_o" />
    <!-- U+0913: "ओ" DEVANAGARI LETTER O -->
    <Key
        latin:keySpec="&#x0913;"
        latin:keyStyle="moreKeysDevanagariVowelSignO" />
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_e" />
    <!-- U+090F: "ए" DEVANAGARI LETTER E -->
    <Key
        latin:keySpec="&#x090F;"
        latin:keyStyle="moreKeysDevanagariVowelSignE" />
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_virama" />
    <!-- U+0905: "अ" DEVANAGARI LETTER A -->
    <Key
        latin:keySpec="&#x0905;"
        latin:keyStyle="moreKeySpecDevanagariSignVirama" />
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_i" />
    <!-- U+0907: "इ" DEVANAGARI LETTER I -->
    <Key
        latin:keySpec="&#x0907;"
        latin:keyStyle="moreKeysDevanagariVowelSignI" />
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_u" />
    <!-- U+0909: "उ" DEVANAGARI LETTER U -->
    <Key
        latin:keySpec="&#x0909;"
        latin:keyStyle="moreKeysDevanagariVowelSignU" />
    <!-- U+092A: "प" DEVANAGARI LETTER PA
         U+092B: "फ" DEVANAGARI LETTER PHA -->
    <Key
        latin:keySpec="&#x092A;"
        latin:moreKeys="&#x092B;" />
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_vocalic_r" />
    <!-- U+0930: "र" DEVANAGARI LETTER RA -->
    <Key
        latin:keySpec="&#x0930;"
        latin:keyStyle="moreKeysDevanagariVowelSignVocalicR" />
    <!-- U+0915: "क" DEVANAGARI LETTER KA
         U+0916: "ख" DEVANAGARI LETTER KHA -->
    <Key
        latin:keySpec="&#x0915;"
        latin:moreKeys="&#x0916;" />
    <!-- U+0924: "त" DEVANAGARI LETTER TA
         U+0925: "थ" DEVANAGARI LETTER THA
         U+0924/U+094D/U+0930: "त्र" DEVANAGARI LETTER TA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER RA -->
    <Key
        latin:keySpec="&#x0924;"
        latin:moreKeys="&#x0925;,&#x0924;&#x094D;&#x0930;" />
    <!-- U+091A: "च" DEVANAGARI LETTER CA
         U+091B: "छ" DEVANAGARI LETTER CHA -->
    <Key
        latin:keySpec="&#x091A;"
        latin:moreKeys="&#x091B;" />
    <!-- U+091F: "ट" DEVANAGARI LETTER TTA
         U+0920: "ठ" DEVANAGARI LETTER TTHA -->
    <Key
        latin:keySpec="&#x091F;"
        latin:moreKeys="&#x0920;" />
</merge>
