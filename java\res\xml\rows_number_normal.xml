<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <Row>
        <Key
            latin:keySpec="1"
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keySpec="2"
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keySpec="3"
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keySpec="-"
            latin:moreKeys="+"
            latin:keyLabelFlags="hasPopupHint"
            latin:keyStyle="numFunctionalKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
    <Row>
        <Key
            latin:keySpec="4"
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keySpec="5"
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keySpec="6"
            latin:keyStyle="numKeyStyle" />
        <switch>
            <case
                latin:mode="date"
            >
                <Key
                    latin:keySpec="."
                    latin:keyStyle="numFunctionalKeyStyle"
                    latin:keyWidth="fillRight" />
            </case>
            <case
                latin:mode="time|datetime"
            >
                <Key
                    latin:keySpec="."
                    latin:keyLabelFlags="hasPopupHint"
                    latin:moreKeys="!text/morekeys_am_pm"
                    latin:keyStyle="numFunctionalKeyStyle"
                    latin:keyWidth="fillRight" />
            </case>
            <default>
                <Key
                    latin:keySpec=","
                    latin:keyStyle="numFunctionalKeyStyle"
                    latin:keyWidth="fillRight" />
            </default>
        </switch>
    </Row>
    <Row>
        <Key
            latin:keySpec="7"
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keySpec="8"
            latin:keyStyle="numKeyStyle"/>
        <Key
            latin:keySpec="9"
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keyStyle="deleteKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
    <Row>
        <switch>
            <case
                latin:mode="date"
            >
                <Key
                    latin:keySpec="/"
                    latin:keyStyle="numKeyStyle" />
            </case>
            <case
                latin:mode="time"
            >
                <Key
                    latin:keySpec=":"
                    latin:keyStyle="numKeyStyle" />
            </case>
            <case
                latin:mode="datetime"
            >
                <Key
                    latin:keySpec="/ :|/"
                    latin:moreKeys="!noPanelAutoMoreKey!,:"
                    latin:keyStyle="numKeyStyle" />
            </case>
            <default>
                <Key
                    latin:keySpec="."
                    latin:keyStyle="numKeyStyle" />
            </default>
        </switch>
        <Key
            latin:keySpec="0"
            latin:keyStyle="numKeyStyle" />
        <Key
            latin:keyStyle="numSpaceKeyStyle" />
        <Key
            latin:keyStyle="enterKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
</merge>
