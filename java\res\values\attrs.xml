<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2010, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources>
    <declare-styleable name="KeyboardTheme">
        <!-- Keyboard style -->
        <attr name="keyboardStyle" format="reference" />
        <!-- KeyboardView style -->
        <attr name="keyboardViewStyle" format="reference" />
        <!-- MainKeyboardView style -->
        <attr name="mainKeyboardViewStyle" format="reference" />
        <!-- EmojiPalettesView style -->
        <attr name="emojiPalettesViewStyle" format="reference" />
        <!-- MoreKeysKeyboard style -->
        <attr name="moreKeysKeyboardStyle" format="reference" />
        <!-- MoreKeysKeyboardView style -->
        <attr name="moreKeysKeyboardViewStyle" format="reference" />
        <!-- MoreKeysKeyboardView style for action key -->
        <attr name="moreKeysKeyboardViewForActionStyle" format="reference" />
        <!-- Suggestions strip style -->
        <attr name="suggestionStripViewStyle" format="reference" />
        <!-- Suggestion word style -->
        <attr name="suggestionWordStyle" format="reference" />
    </declare-styleable>

    <declare-styleable name="KeyboardView">
        <!-- Background image for the key. This image needs to be a {@link StateListDrawable},
             with the following possible states: normal, pressed, checkable, checkable+pressed,
             checkable+checked, checkable+checked+pressed. -->
        <attr name="keyBackground" format="reference" />
        <!-- Background image for the functional key. This image needs to be a
             {@link StateListDrawable}, with the following possible states: normal, pressed. -->
        <attr name="functionalKeyBackground" format="reference" />
        <!-- Background image for the spacebar.  This image needs to be a
             {@link StateListDrawable}, with the following possible states: normal, pressed. -->
        <attr name="spacebarBackground" format="reference" />
        <attr name="spacebarIconWidthRatio" format="float" />
        <!-- Right padding of hint letter to the edge of the key.-->
        <attr name="keyHintLetterPadding" format="dimension" />
        <!-- Popup hint letter string-->
        <attr name="keyPopupHintLetter" format="string" />
        <!-- Bottom padding of popup hint letter to the edge of the key.-->
        <attr name="keyPopupHintLetterPadding" format="dimension" />
        <!-- Right padding of shifted letter hint to the edge of the key.-->
        <attr name="keyShiftedLetterHintPadding" format="dimension" />
        <!-- Blur radius of key text shadow. -->
        <attr name="keyTextShadowRadius" format="float" />

        <!-- Key preview background states -->
        <attr name="state_left_edge" format="boolean" />
        <attr name="state_right_edge" format="boolean" />
        <attr name="state_has_morekeys" format="boolean" />

        <!-- Amount to offset the touch Y coordinate by, for bias correction. -->
        <attr name="verticalCorrection" format="dimension" />
    </declare-styleable>

    <declare-styleable name="MainKeyboardView">
        <!-- Size of the text for spacebar language label, in the proportion of key height. -->
        <attr name="languageOnSpacebarTextRatio" format="fraction" />
        <attr name="languageOnSpacebarTextColor" format="color" />
        <attr name="languageOnSpacebarTextShadowRadius" format="float" />
        <attr name="languageOnSpacebarTextShadowColor" format="color" />
        <!-- Fadeout animator for spacebar language label. -->
        <attr name="languageOnSpacebarFinalAlpha" format="integer" />
        <attr name="languageOnSpacebarFadeoutAnimator" format="reference" />
        <!-- Fadeout and fadein animator for altCodeWhileTyping keys. -->
        <attr name="altCodeKeyWhileTypingFadeoutAnimator" format="reference" />
        <attr name="altCodeKeyWhileTypingFadeinAnimator" format="reference" />
        <!-- Key detection hysteresis distance. -->
        <attr name="keyHysteresisDistance" format="dimension" />
        <!-- Key detection hysteresis distance for shift/symbols sliding input. -->
        <attr name="keyHysteresisDistanceForSlidingModifier" format="dimension" />
        <!-- Touch noise threshold time in millisecond -->
        <attr name="touchNoiseThresholdTime" format="integer" />
        <!-- Touch noise threshold distance in millimeter -->
        <attr name="touchNoiseThresholdDistance" format="dimension" />
        <!-- Enable key selection by dragging finger -->
        <attr name="keySelectionByDraggingFinger" format="boolean" />
        <attr name="slidingKeyInputPreviewColor" format="color" />
        <attr name="slidingKeyInputPreviewWidth" format="dimension" />
        <attr name="slidingKeyInputPreviewBodyRatio" format="integer" />
        <attr name="slidingKeyInputPreviewShadowRatio" format="integer" />
        <!-- Key repeat start timeout -->
        <attr name="keyRepeatStartTimeout" format="integer" />
        <!-- Key repeat interval in millisecond. -->
        <attr name="keyRepeatInterval" format="integer" />
        <!-- Long press timeout of shift key to shift lock in millisecond. -->
        <attr name="longPressShiftLockTimeout" format="integer" />
        <!-- Ignore special key timeout while typing in millisecond. -->
        <attr name="ignoreAltCodeKeyTimeout" format="integer" />
        <!-- Background resource for key press feedback.-->
        <attr name="keyPreviewBackground" format="reference" />
        <!-- Vertical offset of the key press feedback from the key. -->
        <attr name="keyPreviewOffset" format="dimension" />
        <!-- Height of the key press feedback popup. -->
        <attr name="keyPreviewHeight" format="dimension" />
        <!-- TODO: consolidate key preview linger timeout with the key preview animation parameters. -->
        <!-- Delay after key releasing and key press feedback dismissing in millisecond -->
        <attr name="keyPreviewLingerTimeout" format="integer" />
        <!-- Key preview show up animator -->
        <attr name="keyPreviewShowUpAnimator" format="reference" />
        <!-- Key preview dismiss animator -->
        <attr name="keyPreviewDismissAnimator" format="reference" />
        <!-- Layout resource for more keys keyboard -->
        <attr name="moreKeysKeyboardLayout" format="reference" />
        <!-- Layout resource for more keys keyboard of action key -->
        <attr name="moreKeysKeyboardForActionLayout" format="reference" />
        <attr name="backgroundDimAlpha" format="integer" />
        <!-- More keys keyboard will shown at touched point. -->
        <attr name="showMoreKeysKeyboardAtTouchedPoint" format="boolean" />
        <!-- Minimum distance between gesture trail sampling points. -->
        <attr name="gestureTrailMinSamplingDistance" format="dimension" />
        <!-- Maximum angular threshold between gesture trail interpolation segments in degree. -->
        <attr name="gestureTrailMaxInterpolationAngularThreshold" format="integer" />
        <!-- Maximum distance threshold between gesture trail interpolation segments. -->
        <attr name="gestureTrailMaxInterpolationDistanceThreshold" format="dimension" />
        <!-- Maximum number of gesture trail interpolation segments. -->
        <attr name="gestureTrailMaxInterpolationSegments" format="integer" />
        <!-- Delay after gesture trail starts fading out in millisecond. -->
        <attr name="gestureTrailFadeoutStartDelay" format="integer" />
        <!-- Duration while gesture trail is fading out in millisecond. -->
        <attr name="gestureTrailFadeoutDuration" format="integer" />
        <!-- Interval of updating gesture trail in millisecond. -->
        <attr name="gestureTrailUpdateInterval" format="integer" />
        <attr name="gestureTrailColor" format="color" />
        <attr name="gestureTrailStartWidth" format="dimension" />
        <attr name="gestureTrailEndWidth" format="dimension" />
        <attr name="gestureTrailBodyRatio" format="integer" />
        <attr name="gestureTrailShadowRatio" format="integer" />
        <!-- Delay after gesture input and gesture floating preview text dismissing in millisecond -->
        <attr name="gestureFloatingPreviewTextLingerTimeout" format="integer" />
        <!-- Attributes for GestureFloatingPreviewText -->
        <attr name="gestureFloatingPreviewTextSize" format="dimension" />
        <attr name="gestureFloatingPreviewTextColor" format="color" />
        <attr name="gestureFloatingPreviewTextOffset" format="dimension" />
        <attr name="gestureFloatingPreviewColor" format="color" />
        <attr name="gestureFloatingPreviewHorizontalPadding" format="dimension" />
        <attr name="gestureFloatingPreviewVerticalPadding" format="dimension" />
        <attr name="gestureFloatingPreviewRoundRadius" format="dimension" />
        <!-- Static threshold for gesture after fast typing (msec) -->
        <attr name="gestureStaticTimeThresholdAfterFastTyping" format="integer" />
        <!-- Static threshold for starting gesture detection (keyWidth%/sec) -->
        <attr name="gestureDetectFastMoveSpeedThreshold" format="fraction" />
        <!-- Dynamic threshold for gesture after fast typing (msec) -->
        <attr name="gestureDynamicThresholdDecayDuration" format="integer" />
        <!-- Time based threshold values for gesture detection (msec) -->
        <attr name="gestureDynamicTimeThresholdFrom" format="integer" />
        <attr name="gestureDynamicTimeThresholdTo" format="integer" />
        <!-- Distance based threshold values for gesture detection (keyWidth%/sec) -->
        <attr name="gestureDynamicDistanceThresholdFrom" format="fraction" />
        <attr name="gestureDynamicDistanceThresholdTo" format="fraction" />
        <!-- Parameter for gesture sampling (keyWidth%/sec) -->
        <attr name="gestureSamplingMinimumDistance" format="fraction" />
        <!-- Parameters for gesture recognition (msec) and (keyWidth%/sec) -->
        <attr name="gestureRecognitionMinimumTime" format="integer" />
        <attr name="gestureRecognitionUpdateTime" format="integer" />
        <attr name="gestureRecognitionSpeedThreshold" format="fraction" />
        <!-- Suppress showing key preview duration after batch input in millisecond -->
        <attr name="suppressKeyPreviewAfterBatchInputDuration" format="integer" />
    </declare-styleable>

    <declare-styleable name="MoreKeysKeyboardView">
        <attr name="divider" format="reference" />
    </declare-styleable>

    <declare-styleable name="EmojiPalettesView">
        <attr name="categoryIndicatorEnabled" format="boolean" />
        <attr name="categoryIndicatorDrawable" format="reference" />
        <attr name="categoryIndicatorBackground" format="reference" />
        <attr name="categoryPageIndicatorColor" format="color" />
        <attr name="categoryPageIndicatorBackground" format="color" />
        <attr name="iconEmojiRecentsTab" format="reference" />
        <attr name="iconEmojiCategory1Tab" format="reference" />
        <attr name="iconEmojiCategory2Tab" format="reference" />
        <attr name="iconEmojiCategory3Tab" format="reference" />
        <attr name="iconEmojiCategory4Tab" format="reference" />
        <attr name="iconEmojiCategory5Tab" format="reference" />
        <attr name="iconEmojiCategory6Tab" format="reference" />
    </declare-styleable>

    <declare-styleable name="SuggestionStripView">
        <attr name="suggestionStripOptions" format="integer">
            <!-- This should be aligned with SuggestionStripLayoutHelper.AUTO_CORRECT_* and etc. -->
            <flag name="autoCorrectBold" value="0x01" />
            <flag name="autoCorrectUnderline" value="0x02" />
            <flag name="validTypedWordBold" value="0x04" />
        </attr>
        <attr name="colorValidTypedWord" format="color" />
        <attr name="colorTypedWord" format="color" />
        <attr name="colorAutoCorrect" format="color" />
        <attr name="colorSuggested" format="color" />
        <attr name="alphaObsoleted" format="fraction" />
        <attr name="suggestionsCountInStrip" format="integer" />
        <attr name="centerSuggestionPercentile" format="fraction" />
        <attr name="maxMoreSuggestionsRow" format="integer" />
        <attr name="minMoreSuggestionsWidth" format="float" />
    </declare-styleable>

    <declare-styleable name="Keyboard">
        <attr name="themeId" format="enum">
            <!-- This should be aligned with KeyboardTheme.THEME_ID_*. -->
            <enum name="ICS" value="0" />
            <enum name="KLP" value="2" />
            <enum name="LXXLight" value="3" />
            <enum name="LXXDark" value="4" />
        </attr>
        <!-- Touch position correction -->
        <attr name="touchPositionCorrectionData" format="reference" />
        <!-- Keyboard top, bottom, left, right edges paddings, in propotion of keyboard height. -->
        <attr name="keyboardTopPadding" format="fraction" />
        <attr name="keyboardBottomPadding" format="fraction" />
        <attr name="keyboardLeftPadding" format="fraction" />
        <attr name="keyboardRightPadding" format="fraction" />
        <!-- Default height of a row (key height + vertical gap), in pixels or in the proportion of
             keyboard height. -->
        <attr name="rowHeight" format="dimension|fraction" />
        <!-- Default horizontal gap between keys, in the proportion of keyboard width. -->
        <attr name="horizontalGap" format="fraction" />
        <!-- Default vertical gap between rows of keys, in the proportion of keyboard height. -->
        <attr name="verticalGap" format="fraction" />
        <!-- More keys keyboard layout template -->
        <attr name="moreKeysTemplate" format="reference" />
        <!-- Icon set for key top and key preview.
             These should be aligned with KeyboardIconsSet.NAMES_AND_ATTR_IDS[] -->
        <attr name="iconShiftKey" format="reference" />
        <attr name="iconDeleteKey" format="reference" />
        <attr name="iconSettingsKey" format="reference" />
        <attr name="iconSpaceKey" format="reference" />
        <attr name="iconEnterKey" format="reference" />
        <attr name="iconGoKey" format="reference" />
        <attr name="iconSearchKey" format="reference" />
        <attr name="iconSendKey" format="reference" />
        <attr name="iconNextKey" format="reference" />
        <attr name="iconDoneKey" format="reference" />
        <attr name="iconPreviousKey" format="reference" />
        <attr name="iconTabKey" format="reference" />
        <attr name="iconShortcutKey" format="reference" />
        <attr name="iconSpaceKeyForNumberLayout" format="reference" />
        <attr name="iconShiftKeyShifted" format="reference" />
        <attr name="iconShortcutKeyDisabled" format="reference" />
        <attr name="iconLanguageSwitchKey" format="reference" />
        <attr name="iconZwnjKey" format="reference" />
        <attr name="iconZwjKey" format="reference" />
        <attr name="iconImeKey" format="reference" />
        <attr name="iconEmojiActionKey" format="reference" />
        <attr name="iconEmojiNormalKey" format="reference" />
    </declare-styleable>

    <declare-styleable name="Keyboard_GridRows">
        <attr name="codesArray" format="reference" />
        <attr name="textsArray" format="reference" />
    </declare-styleable>

    <declare-styleable name="Keyboard_Key">
        <!-- The alternate unicode value that this key outputs while typing.
             Code value represented in hexadecimal prefixed with "0x" or code value reference using
             "!code/<code_name>" notation. -->
        <attr name="altCode" format="string" />
        <!-- The keys to display in the more keys keyboard. -->
        <attr name="moreKeys" format="string" />
        <!-- The keys to display in the more keys keyboard in addition to moreKeys.
             The additional more keys are inserted at the '%' markers in the moreKeys if any.
             They are inserted at the head of moreKeys if none.
             If there are remaining entries of additionalMoreKeys even after all '%' markers have
             been replaced, those remaining entries are appended at the end of moreKeys. -->
        <attr name="additionalMoreKeys" format="string" />
        <!-- Maximum column of more keys keyboard -->
        <attr name="maxMoreKeysColumn" format="integer" />
        <attr name="backgroundType" format="enum">
            <!-- This should be aligned with Key.BACKGROUND_TYPE_* -->
            <enum name="empty" value="0" />
            <enum name="normal" value="1" />
            <enum name="functional" value="2" />
            <enum name="stickyOff" value="3" />
            <enum name="stickyOn" value="4" />
            <enum name="action" value="5" />
            <enum name="spacebar" value="6" />
        </attr>
        <!-- The key action flags. -->
        <attr name="keyActionFlags" format="integer">
            <!-- This should be aligned with Key.ACTION_FLAGS_* -->
            <flag name="isRepeatable" value="0x01" />
            <flag name="noKeyPreview" value="0x02" />
            <flag name="altCodeWhileTyping" value="0x04" />
            <flag name="enableLongPress" value="0x08" />
        </attr>
        <!-- The label, icon to display on the key. And code, outputText of the key. -->
        <attr name="keySpec" format="string" />
        <!-- The hint label to display on the key in conjunction with the label. -->
        <attr name="keyHintLabel" format="string" />
        <!-- The vertical adjustment of key hint label in proportion to its height. -->
        <attr name="keyHintLabelVerticalAdjustment" format="fraction" />
        <!-- The key label flags. -->
        <attr name="keyLabelFlags" format="integer">
            <!-- This should be aligned with Key.LABEL_FLAGS__* -->
            <flag name="alignHintLabelToBottom" value="0x02" />
            <flag name="alignIconToBottom" value="0x04" />
            <flag name="alignLabelOffCenter" value="0x08" />
            <flag name="fontNormal" value="0x10" />
            <flag name="fontMonoSpace" value="0x20" />
            <flag name="fontDefault" value="0x30" />
            <flag name="followKeyLargeLetterRatio" value="0x40" />
            <flag name="followKeyLetterRatio" value="0x80" />
            <flag name="followKeyLabelRatio" value="0xC0" />
            <flag name="followKeyHintLabelRatio" value="0x140" />
            <flag name="hasPopupHint" value="0x200" />
            <flag name="hasShiftedLetterHint" value="0x400" />
            <flag name="hasHintLabel" value="0x800" />
            <flag name="autoXScale" value="0x4000" />
            <!-- The autoScale value implies autoXScale bit on to optimize scaling code path. -->
            <flag name="autoScale" value="0xc000" />
            <!-- If true, character case of code, altCode, moreKeys, keyOutputText, keyLabel,
                 or keyHintLabel will never be subject to change. -->
            <flag name="preserveCase" value="0x10000" />
            <!-- If true, use keyShiftedLetterHintActivatedColor for the shifted letter hint and
                 keyTextInactivatedColor for the primary key top label. -->
            <flag name="shiftedLetterActivated" value="0x20000" />
            <!-- If true, use EditorInfo.actionLabel for the key label. -->
            <flag name="fromCustomActionLabel" value="0x40000" />
            <!-- If true, use functionalTextColor instead of ketTextColor to drawing the label on
                 the key -->
            <flag name="followFunctionalTextColor" value="0x80000" />
            <!-- Keep aspect ratio of key background. -->
            <flag name="keepBackgroundAspectRatio" value="0x100000" />
            <!-- If true, disable keyHintLabel. -->
            <flag name="disableKeyHintLabel" value="0x40000000" />
            <!-- If true, disable additionalMoreKeys. -->
            <flag name="disableAdditionalMoreKeys" value="0x80000000" />
        </attr>
        <!-- The icon for disabled key -->
        <attr name="keyIconDisabled" format="string" />
        <!-- The key style to specify a set of key attributes defined by <key_style/> -->
        <attr name="keyStyle" format="string" />
        <!-- Visual insets, in the proportion of keyboard width. -->
        <attr name="visualInsetsLeft" format="fraction" />
        <attr name="visualInsetsRight" format="fraction" />
        <!-- Width of the key, in the proportion of keyboard width.
             If the value is fillRight, the actual key width will be determined to fill out the
             area up to the right edge of the keyboard. -->
        <!-- This should be aligned with KeyboardBuilder.Row.KEYWIDTH_* -->
        <attr name="keyWidth" format="fraction|enum">
            <enum name="fillRight" value="-1" />
        </attr>
        <!-- The X-coordinate of upper right corner of this key including horizontal gap, in the
             proportion of keyboard width.
             If the value is negative, the origin is the right edge of the keyboard. -->
        <attr name="keyXPos" format="fraction" />

        <!-- Key top visual attributes -->
        <attr name="keyTypeface" format="enum">
            <!-- This should be aligned with Typeface.NORMAL etc. -->
            <enum name="normal" value="0" />
            <enum name="bold" value="1" />
            <enum name="italic" value="2" />
            <enum name="boldItalic" value="3" />
        </attr>
        <!-- Size of the text for one letter keys. If specified as fraction, the text size is
             measured in the proportion of key height. -->
        <attr name="keyLetterSize" format="dimension|fraction" />
        <!-- Size of the text for keys with multiple letters. If specified as fraction, the text
             size is measured in the proportion of key height. -->
        <attr name="keyLabelSize" format="dimension|fraction" />
        <!-- Large size of the text for one letter keys, in the proportion of key height. -->
        <attr name="keyLargeLetterRatio" format="fraction" />
        <!-- Size of the text for hint letter (= one character hint label), in the proportion of
             key height. -->
        <attr name="keyHintLetterRatio" format="fraction" />
        <!-- Size of the text for hint label, in the proportion of key height. -->
        <attr name="keyHintLabelRatio" format="fraction" />
        <!-- Size of the text for shifted letter hint, in the proportion of key height. -->
        <attr name="keyShiftedLetterHintRatio" format="fraction" />
        <!-- The label's horizontal offset to the center of the key. Negative is to left and
             positive is to right. The value is in proportion of the width of
             TypefaceUtils.KEY_LABEL_REFERENCE_CHAR. -->
        <attr name="keyLabelOffCenterRatio" format="fraction" />
        <!-- The hint label's horizontal offset to the center of the key. Negative is to left and
             positive is to right. The value is in proportion of the width of
             TypefaceUtils.KEY_LABEL_REFERENCE_CHAR. -->
        <attr name="keyHintLabelOffCenterRatio" format="fraction" />
        <!-- Color to use for the label in a key. -->
        <attr name="keyTextColor" format="color" />
        <attr name="keyTextShadowColor" format="color" />
        <!-- Color to use for the label in a key when in inactivated state. -->
        <attr name="keyTextInactivatedColor" format="color" />
        <!-- Color to use for the label in a key that has followFunctionalTextColor keyLabelFlags. -->
        <attr name="functionalTextColor" format="color" />
        <!-- Key hint letter (= one character hint label) color -->
        <attr name="keyHintLetterColor" format="color" />
        <!-- Key hint label color -->
        <attr name="keyHintLabelColor" format="color" />
        <!-- Shifted letter hint colors -->
        <attr name="keyShiftedLetterHintInactivatedColor" format="color" />
        <attr name="keyShiftedLetterHintActivatedColor" format="color" />

        <!-- Key preview visual parameters -->
        <!-- The text color for key press feedback. -->
        <attr name="keyPreviewTextColor" format="color" />
        <!-- Size of the text for key press feedback popup, in the proportion of key height. -->
        <attr name="keyPreviewTextRatio" format="fraction" />
    </declare-styleable>

    <declare-styleable name="Keyboard_Include">
        <attr name="keyboardLayout" format="reference" />
    </declare-styleable>

    <declare-styleable name="Keyboard_Case">
        <attr name="keyboardLayoutSet" format="string" />
        <!-- This should be aligned with KeyboardLayoutSet_Element's elementName. -->
        <attr name="keyboardLayoutSetElement" format="enum|string">
            <enum name="alphabet" value="0" />
            <enum name="alphabetManualShifted" value="1" />
            <enum name="alphabetAutomaticShifted" value="2" />
            <enum name="alphabetShiftLocked" value="3" />
            <enum name="alphabetShiftLockShifted" value="4" />
            <enum name="symbols" value="5" />
            <enum name="symbolsShifted" value="6"  />
            <enum name="phone" value="7"  />
            <enum name="phoneSymbols" value="8"  />
            <enum name="number" value="9"  />
            <enum name="emojiRecents" value="10" />
            <enum name="emojiCategory1" value="11" />
            <enum name="emojiCategory2" value="12" />
            <enum name="emojiCategory3" value="13" />
            <enum name="emojiCategory4" value="14" />
            <enum name="emojiCategory5" value="15" />
            <enum name="emojiCategory6" value="16" />
        </attr>
        <!-- This should be aligned with Keyboard.themeId and KeyboardTheme.THEME_ID_* -->
        <attr name="keyboardTheme" format="enum|string">
            <enum name="ICS" value="0" />
            <enum name="KLP" value="2" />
            <enum name="LXXLight" value="3" />
            <enum name="LXXDark" value="4" />
        </attr>
        <!-- This should be aligned with KeyboardId.MODE_* -->
        <attr name="mode" format="enum|string">
            <enum name="text" value="0" />
            <enum name="url" value="1" />
            <enum name="email" value="2" />
            <enum name="im" value="3" />
            <enum name="phone" value="4" />
            <enum name="number" value="5" />
        </attr>
        <attr name="navigateNext" format="boolean" />
        <attr name="navigatePrevious" format="boolean" />
        <attr name="passwordInput" format="boolean" />
        <attr name="clobberSettingsKey" format="boolean" />
        <attr name="hasShortcutKey" format="boolean" />
        <attr name="languageSwitchKeyEnabled" format="boolean" />
        <attr name="isMultiLine" format="boolean" />
        <attr name="imeAction" format="enum">
            <!-- This should be aligned with EditorInfo.IME_ACTION_* -->
            <enum name="actionUnspecified" value="0" />
            <enum name="actionNone" value="1" />
            <enum name="actionGo" value="2" />
            <enum name="actionSearch" value="3" />
            <enum name="actionSend" value="4" />
            <enum name="actionNext" value="5" />
            <enum name="actionDone" value="6" />
            <enum name="actionPrevious" value="7" />
            <!--  This should be aligned with KeyboardId.IME_ACTION_* -->
            <enum name="actionCustomLabel" value="0x100" />
        </attr>
        <attr name="isIconDefined" format="string" />
        <attr name="localeCode" format="string" />
        <attr name="languageCode" format="string" />
        <attr name="countryCode" format="string" />
    </declare-styleable>

    <declare-styleable name="Keyboard_KeyStyle">
        <attr name="styleName" format="string" />
        <attr name="parentStyle" format="string" />
    </declare-styleable>

    <declare-styleable name="KeyboardLayoutSet_Element">
        <!-- This should be aligned with KeyboardId.ELEMENT_* -->
        <attr name="elementName" format="enum">
            <enum name="alphabet" value="0" />
            <enum name="alphabetManualShifted" value="1" />
            <enum name="alphabetAutomaticShifted" value="2" />
            <enum name="alphabetShiftLocked" value="3" />
            <enum name="alphabetShiftLockShifted" value="4" />
            <enum name="symbols" value="5" />
            <enum name="symbolsShifted" value="6"  />
            <enum name="phone" value="7"  />
            <enum name="phoneSymbols" value="8"  />
            <enum name="number" value="9"  />
            <enum name="emojiRecents" value="10" />
            <enum name="emojiCategory1" value="11" />
            <enum name="emojiCategory2" value="12" />
            <enum name="emojiCategory3" value="13" />
            <enum name="emojiCategory4" value="14" />
            <enum name="emojiCategory5" value="15" />
            <enum name="emojiCategory6" value="16" />
        </attr>
        <attr name="elementKeyboard" format="reference"/>
        <!-- Enable proximity characters correction. Disabled by default. -->
        <attr name="enableProximityCharsCorrection" format="boolean" />
    </declare-styleable>

    <declare-styleable name="KeyboardLayoutSet_Feature">
        <!-- This should be aligned with ScriptUtils.SCRIPT_* -->
        <attr name="supportedScript" format="enum">
            <enum name="arabic" value="0" />
            <enum name="armenian" value="1" />
            <enum name="bengali" value="2" />
            <enum name="cyrillic" value="3" />
            <enum name="devanagari" value="4" />
            <enum name="georgian" value="5" />
            <enum name="greek" value="6" />
            <enum name="hebrew" value="7" />
            <enum name="kannada" value="8" />
            <enum name="khmer" value="9" />
            <enum name="lao" value="10" />
            <enum name="latin" value="11" />
            <enum name="malayalam" value="12" />
            <enum name="myanmar" value="13" />
            <enum name="sinhala" value="14" />
            <enum name="tamil" value="15" />
            <enum name="telugu" value="16" />
            <enum name="thai" value="17" />
        </attr>
    </declare-styleable>

    <declare-styleable name="SeekBarDialogPreference">
        <attr name="maxValue" format="integer" />
        <attr name="minValue" format="integer" />
        <attr name="stepValue" format="integer" />
    </declare-styleable>
</resources>
