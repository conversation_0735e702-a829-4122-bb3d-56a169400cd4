<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<!--
    These accented letters (spoken_accented_letter_*) are unsupported by TTS.
    These symbols (spoken_symbol_*) are also unsupported by TTS.
    TODO: Remove these string resources when TTS/TalkBack support these letters.
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Spoken description for Unicode code point U+00AA: "ª" FEMININE ORDINAL INDICATOR -->
    <string name="spoken_accented_letter_00AA">Feminine ordinal indicator</string>
    <!-- Spoken description for Unicode code point U+00B5: "µ" MICRO SIGN -->
    <string name="spoken_accented_letter_00B5">Micro sign</string>
    <!-- Spoken description for Unicode code point U+00BA: "º" MASCULINE ORDINAL INDICATOR -->
    <string name="spoken_accented_letter_00BA">Masculine ordinal indicator</string>
    <!-- Spoken description for Unicode code point U+00DF: "ß" LATIN SMALL LETTER SHARP S -->
    <string name="spoken_accented_letter_00DF">Sharp S</string>
    <!-- Spoken description for Unicode code point U+00E0: "à" LATIN SMALL LETTER A WITH GRAVE -->
    <string name="spoken_accented_letter_00E0">A, grave</string>
    <!-- Spoken description for Unicode code point U+00E1: "á" LATIN SMALL LETTER A WITH ACUTE -->
    <string name="spoken_accented_letter_00E1">A, acute</string>
    <!-- Spoken description for Unicode code point U+00E2: "â" LATIN SMALL LETTER A WITH CIRCUMFLEX -->
    <string name="spoken_accented_letter_00E2">A, circumflex</string>
    <!-- Spoken description for Unicode code point U+00E3: "ã" LATIN SMALL LETTER A WITH TILDE -->
    <string name="spoken_accented_letter_00E3">A, tilde</string>
    <!-- Spoken description for Unicode code point U+00E4: "ä" LATIN SMALL LETTER A WITH DIAERESIS -->
    <string name="spoken_accented_letter_00E4">A, diaeresis</string>
    <!-- Spoken description for Unicode code point U+00E5: "å" LATIN SMALL LETTER A WITH RING ABOVE -->
    <string name="spoken_accented_letter_00E5">A, ring above</string>
    <!-- Spoken description for Unicode code point U+00E6: "æ" LATIN SMALL LETTER AE -->
    <string name="spoken_accented_letter_00E6">A, E, ligature</string>
    <!-- Spoken description for Unicode code point U+00E7: "ç" LATIN SMALL LETTER C WITH CEDILLA -->
    <string name="spoken_accented_letter_00E7">C, cedilla</string>
    <!-- Spoken description for Unicode code point U+00E8: "è" LATIN SMALL LETTER E WITH GRAVE -->
    <string name="spoken_accented_letter_00E8">E, grave</string>
    <!-- Spoken description for Unicode code point U+00E9: "é" LATIN SMALL LETTER E WITH ACUTE -->
    <string name="spoken_accented_letter_00E9">E, acute</string>
    <!-- Spoken description for Unicode code point U+00EA: "ê" LATIN SMALL LETTER E WITH CIRCUMFLEX -->
    <string name="spoken_accented_letter_00EA">E, circumflex</string>
    <!-- Spoken description for Unicode code point U+00EB: "ë" LATIN SMALL LETTER E WITH DIAERESIS -->
    <string name="spoken_accented_letter_00EB">E, diaeresis</string>
    <!-- Spoken description for Unicode code point U+00EC: "ì" LATIN SMALL LETTER I WITH GRAVE -->
    <string name="spoken_accented_letter_00EC">I, grave</string>
    <!-- Spoken description for Unicode code point U+00ED: "í" LATIN SMALL LETTER I WITH ACUTE -->
    <string name="spoken_accented_letter_00ED">I, acute</string>
    <!-- Spoken description for Unicode code point U+00EE: "î" LATIN SMALL LETTER I WITH CIRCUMFLEX -->
    <string name="spoken_accented_letter_00EE">I, circumflex</string>
    <!-- Spoken description for Unicode code point U+00EF: "ï" LATIN SMALL LETTER I WITH DIAERESIS -->
    <string name="spoken_accented_letter_00EF">I, diaeresis</string>
    <!-- Spoken description for Unicode code point U+00F0: "ð" LATIN SMALL LETTER ETH -->
    <string name="spoken_accented_letter_00F0">Eth</string>
    <!-- Spoken description for Unicode code point U+00F1: "ñ" LATIN SMALL LETTER N WITH TILDE -->
    <string name="spoken_accented_letter_00F1">N, tilde</string>
    <!-- Spoken description for Unicode code point U+00F2: "ò" LATIN SMALL LETTER O WITH GRAVE -->
    <string name="spoken_accented_letter_00F2">O, grave</string>
    <!-- Spoken description for Unicode code point U+00F3: "ó" LATIN SMALL LETTER O WITH ACUTE -->
    <string name="spoken_accented_letter_00F3">O, acute</string>
    <!-- Spoken description for Unicode code point U+00F4: "ô" LATIN SMALL LETTER O WITH CIRCUMFLEX -->
    <string name="spoken_accented_letter_00F4">O, circumflex</string>
    <!-- Spoken description for Unicode code point U+00F5: "õ" LATIN SMALL LETTER O WITH TILDE -->
    <string name="spoken_accented_letter_00F5">O, tilde</string>
    <!-- Spoken description for Unicode code point U+00F6: "ö" LATIN SMALL LETTER O WITH DIAERESIS -->
    <string name="spoken_accented_letter_00F6">O, diaeresis</string>
    <!-- Spoken description for Unicode code point U+00F8: "ø" LATIN SMALL LETTER O WITH STROKE -->
    <string name="spoken_accented_letter_00F8">O, stroke</string>
    <!-- Spoken description for Unicode code point U+00F9: "ù" LATIN SMALL LETTER U WITH GRAVE -->
    <string name="spoken_accented_letter_00F9">U, grave</string>
    <!-- Spoken description for Unicode code point U+00FA: "ú" LATIN SMALL LETTER U WITH ACUTE -->
    <string name="spoken_accented_letter_00FA">U, acute</string>
    <!-- Spoken description for Unicode code point U+00FB: "û" LATIN SMALL LETTER U WITH CIRCUMFLEX -->
    <string name="spoken_accented_letter_00FB">U, circumflex</string>
    <!-- Spoken description for Unicode code point U+00FC: "ü" LATIN SMALL LETTER U WITH DIAERESIS -->
    <string name="spoken_accented_letter_00FC">U, diaeresis</string>
    <!-- Spoken description for Unicode code point U+00FD: "ý" LATIN SMALL LETTER Y WITH ACUTE -->
    <string name="spoken_accented_letter_00FD">Y, acute</string>
    <!-- Spoken description for Unicode code point U+00FE: "þ" LATIN SMALL LETTER THORN -->
    <string name="spoken_accented_letter_00FE">Thorn</string>
    <!-- Spoken description for Unicode code point U+00FF: "ÿ" LATIN SMALL LETTER Y WITH DIAERESIS -->
    <string name="spoken_accented_letter_00FF">Y, diaeresis</string>
    <!-- Spoken description for Unicode code point U+0101: "ā" LATIN SMALL LETTER A WITH MACRON -->
    <string name="spoken_accented_letter_0101">A, macron</string>
    <!-- Spoken description for Unicode code point U+0103: "ă" LATIN SMALL LETTER A WITH BREVE -->
    <string name="spoken_accented_letter_0103">A, breve</string>
    <!-- Spoken description for Unicode code point U+0105: "ą" LATIN SMALL LETTER A WITH OGONEK -->
    <string name="spoken_accented_letter_0105">A, ogonek</string>
    <!-- Spoken description for Unicode code point U+0107: "ć" LATIN SMALL LETTER C WITH ACUTE -->
    <string name="spoken_accented_letter_0107">C, acute</string>
    <!-- Spoken description for Unicode code point U+0109: "ĉ" LATIN SMALL LETTER C WITH CIRCUMFLEX -->
    <string name="spoken_accented_letter_0109">C, circumflex</string>
    <!-- Spoken description for Unicode code point U+010B: "ċ" LATIN SMALL LETTER C WITH DOT ABOVE -->
    <string name="spoken_accented_letter_010B">C, dot above</string>
    <!-- Spoken description for Unicode code point U+010D: "č" LATIN SMALL LETTER C WITH CARON -->
    <string name="spoken_accented_letter_010D">C, caron</string>
    <!-- Spoken description for Unicode code point U+010F: "ď" LATIN SMALL LETTER D WITH CARON -->
    <string name="spoken_accented_letter_010F">D, caron</string>
    <!-- Spoken description for Unicode code point U+0111: "đ" LATIN SMALL LETTER D WITH STROKE -->
    <string name="spoken_accented_letter_0111">D, stroke</string>
    <!-- Spoken description for Unicode code point U+0113: "ē" LATIN SMALL LETTER E WITH MACRON -->
    <string name="spoken_accented_letter_0113">E, macron</string>
    <!-- Spoken description for Unicode code point U+0115: "ĕ" LATIN SMALL LETTER E WITH BREVE -->
    <string name="spoken_accented_letter_0115">E, breve</string>
    <!-- Spoken description for Unicode code point U+0117: "ė" LATIN SMALL LETTER E WITH DOT ABOVE -->
    <string name="spoken_accented_letter_0117">E, dot above</string>
    <!-- Spoken description for Unicode code point U+0119: "ę" LATIN SMALL LETTER E WITH OGONEK -->
    <string name="spoken_accented_letter_0119">E, ogonek</string>
    <!-- Spoken description for Unicode code point U+011B: "ě" LATIN SMALL LETTER E WITH CARON -->
    <string name="spoken_accented_letter_011B">E, caron</string>
    <!-- Spoken description for Unicode code point U+011D: "ĝ" LATIN SMALL LETTER G WITH CIRCUMFLEX -->
    <string name="spoken_accented_letter_011D">G, circumflex</string>
    <!-- Spoken description for Unicode code point U+011F: "ğ" LATIN SMALL LETTER G WITH BREVE -->
    <string name="spoken_accented_letter_011F">G, breve</string>
    <!-- Spoken description for Unicode code point U+0121: "ġ" LATIN SMALL LETTER G WITH DOT ABOVE -->
    <string name="spoken_accented_letter_0121">G, dot above</string>
    <!-- Spoken description for Unicode code point U+0123: "ģ" LATIN SMALL LETTER G WITH CEDILLA -->
    <string name="spoken_accented_letter_0123">G, cedilla</string>
    <!-- Spoken description for Unicode code point U+0125: "ĥ" LATIN SMALL LETTER H WITH CIRCUMFLEX -->
    <string name="spoken_accented_letter_0125">H, circumflex</string>
    <!-- Spoken description for Unicode code point U+0127: "ħ" LATIN SMALL LETTER H WITH STROKE -->
    <string name="spoken_accented_letter_0127">H, stroke</string>
    <!-- Spoken description for Unicode code point U+0129: "ĩ" LATIN SMALL LETTER I WITH TILDE -->
    <string name="spoken_accented_letter_0129">I, tilde</string>
    <!-- Spoken description for Unicode code point U+012B: "ī" LATIN SMALL LETTER I WITH MACRON -->
    <string name="spoken_accented_letter_012B">I, macron</string>
    <!-- Spoken description for Unicode code point U+012D: "ĭ" LATIN SMALL LETTER I WITH BREVE -->
    <string name="spoken_accented_letter_012D">I, breve</string>
    <!-- Spoken description for Unicode code point U+012F: "į" LATIN SMALL LETTER I WITH OGONEK -->
    <string name="spoken_accented_letter_012F">I, ogonek</string>
    <!-- Spoken description for Unicode code point U+0131: "ı" LATIN SMALL LETTER DOTLESS I -->
    <string name="spoken_accented_letter_0131">Dotless I</string>
    <!-- Spoken description for Unicode code point U+0133: "ĳ" LATIN SMALL LIGATURE IJ -->
    <string name="spoken_accented_letter_0133">I, J, ligature</string>
    <!-- Spoken description for Unicode code point U+0135: "ĵ" LATIN SMALL LETTER J WITH CIRCUMFLEX -->
    <string name="spoken_accented_letter_0135">J, circumflex</string>
    <!-- Spoken description for Unicode code point U+0137: "ķ" LATIN SMALL LETTER K WITH CEDILLA -->
    <string name="spoken_accented_letter_0137">K, cedilla</string>
    <!-- Spoken description for Unicode code point U+0138: "ĸ" LATIN SMALL LETTER KRA -->
    <string name="spoken_accented_letter_0138">Kra</string>
    <!-- Spoken description for Unicode code point U+013A: "ĺ" LATIN SMALL LETTER L WITH ACUTE -->
    <string name="spoken_accented_letter_013A">L, acute</string>
    <!-- Spoken description for Unicode code point U+013C: "ļ" LATIN SMALL LETTER L WITH CEDILLA -->
    <string name="spoken_accented_letter_013C">L, cedilla</string>
    <!-- Spoken description for Unicode code point U+013E: "ľ" LATIN SMALL LETTER L WITH CARON -->
    <string name="spoken_accented_letter_013E">L, caron</string>
    <!-- Spoken description for Unicode code point U+0140: "ŀ" LATIN SMALL LETTER L WITH MIDDLE DOT -->
    <string name="spoken_accented_letter_0140">L, middle dot</string>
    <!-- Spoken description for Unicode code point U+0142: "ł" LATIN SMALL LETTER L WITH STROKE -->
    <string name="spoken_accented_letter_0142">L, stroke</string>
    <!-- Spoken description for Unicode code point U+0144: "ń" LATIN SMALL LETTER N WITH ACUTE -->
    <string name="spoken_accented_letter_0144">N, acute</string>
    <!-- Spoken description for Unicode code point U+0146: "ņ" LATIN SMALL LETTER N WITH CEDILLA -->
    <string name="spoken_accented_letter_0146">N, cedilla</string>
    <!-- Spoken description for Unicode code point U+0148: "ň" LATIN SMALL LETTER N WITH CARON -->
    <string name="spoken_accented_letter_0148">N, caron</string>
    <!-- Spoken description for Unicode code point U+0149: "ŉ" LATIN SMALL LETTER N PRECEDED BY APOSTROPHE -->
    <string name="spoken_accented_letter_0149">N, preceded by apostrophe</string>
    <!-- Spoken description for Unicode code point U+014B: "ŋ" LATIN SMALL LETTER ENG -->
    <string name="spoken_accented_letter_014B">Eng</string>
    <!-- Spoken description for Unicode code point U+014D: "ō" LATIN SMALL LETTER O WITH MACRON -->
    <string name="spoken_accented_letter_014D">O, macron</string>
    <!-- Spoken description for Unicode code point U+014F: "ŏ" LATIN SMALL LETTER O WITH BREVE -->
    <string name="spoken_accented_letter_014F">O, breve</string>
    <!-- Spoken description for Unicode code point U+0151: "ő" LATIN SMALL LETTER O WITH DOUBLE ACUTE -->
    <string name="spoken_accented_letter_0151">O, double acute</string>
    <!-- Spoken description for Unicode code point U+0153: "œ" LATIN SMALL LIGATURE OE -->
    <string name="spoken_accented_letter_0153">O, E, ligature</string>
    <!-- Spoken description for Unicode code point U+0155: "ŕ" LATIN SMALL LETTER R WITH ACUTE -->
    <string name="spoken_accented_letter_0155">R, acute</string>
    <!-- Spoken description for Unicode code point U+0157: "ŗ" LATIN SMALL LETTER R WITH CEDILLA -->
    <string name="spoken_accented_letter_0157">R, cedilla</string>
    <!-- Spoken description for Unicode code point U+0159: "ř" LATIN SMALL LETTER R WITH CARON -->
    <string name="spoken_accented_letter_0159">R, caron</string>
    <!-- Spoken description for Unicode code point U+015B: "ś" LATIN SMALL LETTER S WITH ACUTE -->
    <string name="spoken_accented_letter_015B">S, acute</string>
    <!-- Spoken description for Unicode code point U+015D: "ŝ" LATIN SMALL LETTER S WITH CIRCUMFLEX -->
    <string name="spoken_accented_letter_015D">S, circumflex</string>
    <!-- Spoken description for Unicode code point U+015F: "ş" LATIN SMALL LETTER S WITH CEDILLA -->
    <string name="spoken_accented_letter_015F">S, cedilla</string>
    <!-- Spoken description for Unicode code point U+0161: "š" LATIN SMALL LETTER S WITH CARON -->
    <string name="spoken_accented_letter_0161">S, caron</string>
    <!-- Spoken description for Unicode code point U+0163: "ţ" LATIN SMALL LETTER T WITH CEDILLA -->
    <string name="spoken_accented_letter_0163">T, cedilla</string>
    <!-- Spoken description for Unicode code point U+0165: "ť" LATIN SMALL LETTER T WITH CARON -->
    <string name="spoken_accented_letter_0165">T, caron</string>
    <!-- Spoken description for Unicode code point U+0167: "ŧ" LATIN SMALL LETTER T WITH STROKE -->
    <string name="spoken_accented_letter_0167">T, stroke</string>
    <!-- Spoken description for Unicode code point U+0169: "ũ" LATIN SMALL LETTER U WITH TILDE -->
    <string name="spoken_accented_letter_0169">U, tilde</string>
    <!-- Spoken description for Unicode code point U+016B: "ū" LATIN SMALL LETTER U WITH MACRON -->
    <string name="spoken_accented_letter_016B">U, macron</string>
    <!-- Spoken description for Unicode code point U+016D: "ŭ" LATIN SMALL LETTER U WITH BREVE -->
    <string name="spoken_accented_letter_016D">U, breve</string>
    <!-- Spoken description for Unicode code point U+016F: "ů" LATIN SMALL LETTER U WITH RING ABOVE -->
    <string name="spoken_accented_letter_016F">U, ring above</string>
    <!-- Spoken description for Unicode code point U+0171: "ű" LATIN SMALL LETTER U WITH DOUBLE ACUTE -->
    <string name="spoken_accented_letter_0171">U, double acute</string>
    <!-- Spoken description for Unicode code point U+0173: "ų" LATIN SMALL LETTER U WITH OGONEK -->
    <string name="spoken_accented_letter_0173">U, ogonek</string>
    <!-- Spoken description for Unicode code point U+0175: "ŵ" LATIN SMALL LETTER W WITH CIRCUMFLEX -->
    <string name="spoken_accented_letter_0175">W, circumflex</string>
    <!-- Spoken description for Unicode code point U+0177: "ŷ" LATIN SMALL LETTER Y WITH CIRCUMFLEX -->
    <string name="spoken_accented_letter_0177">Y, circumflex</string>
    <!-- Spoken description for Unicode code point U+017A: "ź" LATIN SMALL LETTER Z WITH ACUTE -->
    <string name="spoken_accented_letter_017A">Z, acute</string>
    <!-- Spoken description for Unicode code point U+017C: "ż" LATIN SMALL LETTER Z WITH DOT ABOVE -->
    <string name="spoken_accented_letter_017C">Z, dot above</string>
    <!-- Spoken description for Unicode code point U+017E: "ž" LATIN SMALL LETTER Z WITH CARON -->
    <string name="spoken_accented_letter_017E">Z, caron</string>
    <!-- Spoken description for Unicode code point U+017F: "ſ" LATIN SMALL LETTER LONG S -->
    <string name="spoken_accented_letter_017F">Long S</string>
    <!-- Spoken description for Unicode code point U+01A1: "ơ" LATIN SMALL LETTER O WITH HORN -->
    <string name="spoken_accented_letter_01A1">O, horn</string>
    <!-- Spoken description for Unicode code point U+01B0: "ư" LATIN SMALL LETTER U WITH HORN -->
    <string name="spoken_accented_letter_01B0">U, horn</string>
    <!-- Spoken description for Unicode code point U+0219: "ș" LATIN SMALL LETTER S WITH COMMA BELOW -->
    <string name="spoken_accented_letter_0219">S, comma below</string>
    <!-- Spoken description for Unicode code point U+021B: "ț" LATIN SMALL LETTER T WITH COMMA BELOW -->
    <string name="spoken_accented_letter_021B">T, comma below</string>
    <!-- Spoken description for Unicode code point U+0259: "ə" LATIN SMALL LETTER SCHWA -->
    <string name="spoken_accented_letter_0259">Schwa</string>
    <!-- Spoken description for Unicode code point U+1EA1: "ạ" LATIN SMALL LETTER A WITH DOT BELOW -->
    <string name="spoken_accented_letter_1EA1">A, dot below</string>
    <!-- Spoken description for Unicode code point U+1EA3: "ả" LATIN SMALL LETTER A WITH HOOK ABOVE -->
    <string name="spoken_accented_letter_1EA3">A, hook above</string>
    <!-- Spoken description for Unicode code point U+1EA5: "ấ" LATIN SMALL LETTER A WITH CIRCUMFLEX AND ACUTE -->
    <string name="spoken_accented_letter_1EA5">A, circumflex and acute</string>
    <!-- Spoken description for Unicode code point U+1EA7: "ầ" LATIN SMALL LETTER A WITH CIRCUMFLEX AND GRAVE -->
    <string name="spoken_accented_letter_1EA7">A, circumflex and grave</string>
    <!-- Spoken description for Unicode code point U+1EA9: "ẩ" LATIN SMALL LETTER A WITH CIRCUMFLEX AND HOOK ABOVE -->
    <string name="spoken_accented_letter_1EA9">A, circumflex and hook above</string>
    <!-- Spoken description for Unicode code point U+1EAB: "ẫ" LATIN SMALL LETTER A WITH CIRCUMFLEX AND TILDE -->
    <string name="spoken_accented_letter_1EAB">A, circumflex and tilde</string>
    <!-- Spoken description for Unicode code point U+1EAD: "ậ" LATIN SMALL LETTER A WITH CIRCUMFLEX AND DOT BELOW -->
    <string name="spoken_accented_letter_1EAD">A, circumflex and dot below</string>
    <!-- Spoken description for Unicode code point U+1EAF: "ắ" LATIN SMALL LETTER A WITH BREVE AND ACUTE -->
    <string name="spoken_accented_letter_1EAF">A, breve and acute</string>
    <!-- Spoken description for Unicode code point U+1EB1: "ằ" LATIN SMALL LETTER A WITH BREVE AND GRAVE -->
    <string name="spoken_accented_letter_1EB1">A, breve and grave</string>
    <!-- Spoken description for Unicode code point U+1EB3: "ẳ" LATIN SMALL LETTER A WITH BREVE AND HOOK ABOVE -->
    <string name="spoken_accented_letter_1EB3">A, breve and hook above</string>
    <!-- Spoken description for Unicode code point U+1EB5: "ẵ" LATIN SMALL LETTER A WITH BREVE AND TILDE -->
    <string name="spoken_accented_letter_1EB5">A, breve and tilde</string>
    <!-- Spoken description for Unicode code point U+1EB7: "ặ" LATIN SMALL LETTER A WITH BREVE AND DOT BELOW -->
    <string name="spoken_accented_letter_1EB7">A, breve and dot below</string>
    <!-- Spoken description for Unicode code point U+1EB9: "ẹ" LATIN SMALL LETTER E WITH DOT BELOW -->
    <string name="spoken_accented_letter_1EB9">E, dot below</string>
    <!-- Spoken description for Unicode code point U+1EBB: "ẻ" LATIN SMALL LETTER E WITH HOOK ABOVE -->
    <string name="spoken_accented_letter_1EBB">E, hook above</string>
    <!-- Spoken description for Unicode code point U+1EBD: "ẽ" LATIN SMALL LETTER E WITH TILDE -->
    <string name="spoken_accented_letter_1EBD">E, tilde</string>
    <!-- Spoken description for Unicode code point U+1EBF: "ế" LATIN SMALL LETTER E WITH CIRCUMFLEX AND ACUTE -->
    <string name="spoken_accented_letter_1EBF">E, circumflex and acute</string>
    <!-- Spoken description for Unicode code point U+1EC1: "ề" LATIN SMALL LETTER E WITH CIRCUMFLEX AND GRAVE -->
    <string name="spoken_accented_letter_1EC1">E, circumflex and grave</string>
    <!-- Spoken description for Unicode code point U+1EC3: "ể" LATIN SMALL LETTER E WITH CIRCUMFLEX AND HOOK ABOVE -->
    <string name="spoken_accented_letter_1EC3">E, circumflex and hook above</string>
    <!-- Spoken description for Unicode code point U+1EC5: "ễ" LATIN SMALL LETTER E WITH CIRCUMFLEX AND TILDE -->
    <string name="spoken_accented_letter_1EC5">E, circumflex and tilde</string>
    <!-- Spoken description for Unicode code point U+1EC7: "ệ" LATIN SMALL LETTER E WITH CIRCUMFLEX AND DOT BELOW -->
    <string name="spoken_accented_letter_1EC7">E, circumflex and dot below</string>
    <!-- Spoken description for Unicode code point U+1EC9: "ỉ" LATIN SMALL LETTER I WITH HOOK ABOVE -->
    <string name="spoken_accented_letter_1EC9">I, hook above</string>
    <!-- Spoken description for Unicode code point U+1ECB: "ị" LATIN SMALL LETTER I WITH DOT BELOW -->
    <string name="spoken_accented_letter_1ECB">I, dot below</string>
    <!-- Spoken description for Unicode code point U+1ECD: "ọ" LATIN SMALL LETTER O WITH DOT BELOW -->
    <string name="spoken_accented_letter_1ECD">O, dot below</string>
    <!-- Spoken description for Unicode code point U+1ECF: "ỏ" LATIN SMALL LETTER O WITH HOOK ABOVE -->
    <string name="spoken_accented_letter_1ECF">O, hook above</string>
    <!-- Spoken description for Unicode code point U+1ED1: "ố" LATIN SMALL LETTER O WITH CIRCUMFLEX AND ACUTE -->
    <string name="spoken_accented_letter_1ED1">O, circumflex and acute</string>
    <!-- Spoken description for Unicode code point U+1ED3: "ồ" LATIN SMALL LETTER O WITH CIRCUMFLEX AND GRAVE -->
    <string name="spoken_accented_letter_1ED3">O, circumflex and grave</string>
    <!-- Spoken description for Unicode code point U+1ED5: "ổ" LATIN SMALL LETTER O WITH CIRCUMFLEX AND HOOK ABOVE -->
    <string name="spoken_accented_letter_1ED5">O, circumflex and hook above</string>
    <!-- Spoken description for Unicode code point U+1ED7: "ỗ" LATIN SMALL LETTER O WITH CIRCUMFLEX AND TILDE -->
    <string name="spoken_accented_letter_1ED7">O, circumflex and tilde</string>
    <!-- Spoken description for Unicode code point U+1ED9: "ộ" LATIN SMALL LETTER O WITH CIRCUMFLEX AND DOT BELOW -->
    <string name="spoken_accented_letter_1ED9">O, circumflex and dot below</string>
    <!-- Spoken description for Unicode code point U+1EDB: "ớ" LATIN SMALL LETTER O WITH HORN AND ACUTE -->
    <string name="spoken_accented_letter_1EDB">O, horn and acute</string>
    <!-- Spoken description for Unicode code point U+1EDD: "ờ" LATIN SMALL LETTER O WITH HORN AND GRAVE -->
    <string name="spoken_accented_letter_1EDD">O, horn and grave</string>
    <!-- Spoken description for Unicode code point U+1EDF: "ở" LATIN SMALL LETTER O WITH HORN AND HOOK ABOVE -->
    <string name="spoken_accented_letter_1EDF">O, horn and hook above</string>
    <!-- Spoken description for Unicode code point U+1EE1: "ỡ" LATIN SMALL LETTER O WITH HORN AND TILDE -->
    <string name="spoken_accented_letter_1EE1">O, horn and tilde</string>
    <!-- Spoken description for Unicode code point U+1EE3: "ợ" LATIN SMALL LETTER O WITH HORN AND DOT BELOW -->
    <string name="spoken_accented_letter_1EE3">O, horn and dot below</string>
    <!-- Spoken description for Unicode code point U+1EE5: "ụ" LATIN SMALL LETTER U WITH DOT BELOW -->
    <string name="spoken_accented_letter_1EE5">U, dot below</string>
    <!-- Spoken description for Unicode code point U+1EE7: "ủ" LATIN SMALL LETTER U WITH HOOK ABOVE -->
    <string name="spoken_accented_letter_1EE7">U, hook above</string>
    <!-- Spoken description for Unicode code point U+1EE9: "ứ" LATIN SMALL LETTER U WITH HORN AND ACUTE -->
    <string name="spoken_accented_letter_1EE9">U, horn and acute</string>
    <!-- Spoken description for Unicode code point U+1EEB: "ừ" LATIN SMALL LETTER U WITH HORN AND GRAVE -->
    <string name="spoken_accented_letter_1EEB">U, horn and grave</string>
    <!-- Spoken description for Unicode code point U+1EED: "ử" LATIN SMALL LETTER U WITH HORN AND HOOK ABOVE -->
    <string name="spoken_accented_letter_1EED">U, horn and hook above</string>
    <!-- Spoken description for Unicode code point U+1EEF: "ữ" LATIN SMALL LETTER U WITH HORN AND TILDE -->
    <string name="spoken_accented_letter_1EEF">U, horn and tilde</string>
    <!-- Spoken description for Unicode code point U+1EF1: "ự" LATIN SMALL LETTER U WITH HORN AND DOT BELOW -->
    <string name="spoken_accented_letter_1EF1">U, horn and dot below</string>
    <!-- Spoken description for Unicode code point U+1EF3: "ỳ" LATIN SMALL LETTER Y WITH GRAVE -->
    <string name="spoken_accented_letter_1EF3">Y, grave</string>
    <!-- Spoken description for Unicode code point U+1EF5: "ỵ" LATIN SMALL LETTER Y WITH DOT BELOW -->
    <string name="spoken_accented_letter_1EF5">Y, dot below</string>
    <!-- Spoken description for Unicode code point U+1EF7: "ỷ" LATIN SMALL LETTER Y WITH HOOK ABOVE -->
    <string name="spoken_accented_letter_1EF7">Y, hook above</string>
    <!-- Spoken description for Unicode code point U+1EF9: "ỹ" LATIN SMALL LETTER Y WITH TILDE -->
    <string name="spoken_accented_letter_1EF9">Y, tilde</string>
    <!-- Spoken description for Unicode code point U+00A1: "¡" INVERTED EXCLAMATION MARK -->
    <string name="spoken_symbol_00A1">Inverted exclamation mark</string>
    <!-- Spoken description for Unicode code point U+00AB: "«" LEFT-POINTING DOUBLE ANGLE QUOTATION MARK -->
    <string name="spoken_symbol_00AB">Left-pointing double angle quotation mark</string>
    <!-- Spoken description for Unicode code point U+00B7: "·" MIDDLE DOT -->
    <string name="spoken_symbol_00B7">Middle dot</string>
    <!-- Spoken description for Unicode code point U+00B9: "¹" SUPERSCRIPT ONE -->
    <string name="spoken_symbol_00B9">Superscript one</string>
    <!-- Spoken description for Unicode code point U+00BB: "»" RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK -->
    <string name="spoken_symbol_00BB">Right-pointing double angle quotation mark</string>
    <!-- Spoken description for Unicode code point U+00BF: "¿" INVERTED QUESTION MARK -->
    <string name="spoken_symbol_00BF">Inverted question mark</string>
    <!-- Spoken description for Unicode code point U+2018: "‘" LEFT SINGLE QUOTATION MARK -->
    <string name="spoken_symbol_2018">Left single quotation mark</string>
    <!-- Spoken description for Unicode code point U+2019: "’" RIGHT SINGLE QUOTATION MARK -->
    <string name="spoken_symbol_2019">Right single quotation mark</string>
    <!-- Spoken description for Unicode code point U+201A: "‚" SINGLE LOW-9 QUOTATION MARK -->
    <string name="spoken_symbol_201A">Single low-9 quotation mark</string>
    <!-- Spoken description for Unicode code point U+201C: "“" LEFT DOUBLE QUOTATION MARK -->
    <string name="spoken_symbol_201C">Left double quotation mark</string>
    <!-- Spoken description for Unicode code point U+201D: "”" RIGHT DOUBLE QUOTATION MARK -->
    <string name="spoken_symbol_201D">Right double quotation mark</string>
    <!-- Spoken description for Unicode code point U+2020: "†" DAGGER -->
    <string name="spoken_symbol_2020">Dagger</string>
    <!-- Spoken description for Unicode code point U+2021: "‡" DOUBLE DAGGER -->
    <string name="spoken_symbol_2021">Double dagger</string>
    <!-- Spoken description for Unicode code point U+2030: "‰" PER MILLE SIGN -->
    <string name="spoken_symbol_2030">Per mille sign</string>
    <!-- Spoken description for Unicode code point U+2032: "′" PRIME -->
    <string name="spoken_symbol_2032">Prime</string>
    <!-- Spoken description for Unicode code point U+2033: "″" DOUBLE PRIME -->
    <string name="spoken_symbol_2033">Double prime</string>
    <!-- Spoken description for Unicode code point U+2039: "‹" SINGLE LEFT-POINTING ANGLE QUOTATION MARK -->
    <string name="spoken_symbol_2039">Single left-pointing angle quotation mark</string>
    <!-- Spoken description for Unicode code point U+203A: "›" SINGLE RIGHT-POINTING ANGLE QUOTATION MARK -->
    <string name="spoken_symbol_203A">Single right-pointing angle quotation mark</string>
    <!-- Spoken description for Unicode code point U+2074: "⁴" SUPERSCRIPT FOUR -->
    <string name="spoken_symbol_2074">Superscript four</string>
    <!-- Spoken description for Unicode code point U+207F: "ⁿ" SUPERSCRIPT LATIN SMALL LETTER N -->
    <string name="spoken_symbol_207F">Superscript latin small letter n</string>
    <!-- Spoken description for Unicode code point U+20B1: "₱" PESO SIGN -->
    <string name="spoken_symbol_20B1">Peso sign</string>
    <!-- Spoken description for Unicode code point U+2105: "℅" CARE OF -->
    <string name="spoken_symbol_2105">Care of</string>
    <!-- Spoken description for Unicode code point U+2192: "→" RIGHTWARDS ARROW -->
    <string name="spoken_symbol_2192">Rightwards arrow</string>
    <!-- Spoken description for Unicode code point U+2193: "↓" DOWNWARDS ARROW -->
    <string name="spoken_symbol_2193">Downwards arrow</string>
    <!-- Spoken description for Unicode code point U+2205: "∅" EMPTY SET -->
    <string name="spoken_symbol_2205">Empty set</string>
    <!-- Spoken description for Unicode code point U+2206: "∆" INCREMENT -->
    <string name="spoken_symbol_2206">Increment</string>
    <!-- Spoken description for Unicode code point U+2264: "≤" LESS-THAN OR EQUAL TO -->
    <string name="spoken_symbol_2264">Less-than or equal to</string>
    <!-- Spoken description for Unicode code point U+2265: "≥" GREATER-THAN OR EQUAL TO -->
    <string name="spoken_symbol_2265">Greater-than or equal to</string>
    <!-- Spoken description for Unicode code point U+2605: "★" BLACK STAR -->
    <string name="spoken_symbol_2605">Black star</string>
</resources>
