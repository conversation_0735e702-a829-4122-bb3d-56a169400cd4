<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/user_dict_settings_add_dialog_top"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical" >

    <EditText
        android:id="@+id/user_dictionary_add_word_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="fill_horizontal|center_vertical"
        android:layout_marginBottom="8dip"
        android:layout_marginStart="8dip"
        android:layout_marginTop="8dip"
        android:hint="@string/user_dict_settings_add_word_hint"
        android:imeOptions="flagNoFullscreen"
        android:inputType="textNoSuggestions"
        android:maxLength="@integer/config_user_dictionary_max_word_length" >

        <requestFocus />
    </EditText>

    <GridLayout
        android:id="@+id/user_dictionary_add_word_grid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dip"
        android:layout_marginStart="8dip"
        android:columnCount="2" >

        <TextView
            android:id="@+id/user_dictionary_add_shortcut_label"
            style="?android:attr/textAppearanceSmall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|center_vertical"
            android:text="@string/user_dict_settings_add_shortcut_option_name" />

        <EditText
            android:id="@+id/user_dictionary_add_shortcut"
            android:layout_width="wrap_content"
            android:layout_gravity="fill_horizontal|center_vertical"
            android:layout_marginBottom="8dip"
            android:layout_marginStart="8dip"
            android:layout_marginTop="8dip"
            android:hint="@string/user_dict_settings_add_shortcut_hint"
            android:imeOptions="flagNoFullscreen"
            android:inputType="textNoSuggestions"
            android:maxLength="@integer/config_user_dictionary_max_word_length" />

        <TextView
            android:id="@+id/user_dictionary_add_locale_label"
            style="?android:attr/textAppearanceSmall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|center_vertical"
            android:text="@string/user_dict_settings_add_locale_option_name"
            android:visibility="gone" />

        <Spinner
            android:id="@+id/user_dictionary_add_locale"
            android:layout_width="wrap_content"
            android:layout_gravity="fill_horizontal|center_vertical"
            android:layout_marginBottom="8dip"
            android:layout_marginStart="8dip"
            android:layout_marginTop="8dip"
            android:visibility="gone" />
    </GridLayout>

</LinearLayout>