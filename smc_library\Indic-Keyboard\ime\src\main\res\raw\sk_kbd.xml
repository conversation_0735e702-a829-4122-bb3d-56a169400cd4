<?xml version="1.0"?>
<inputmethod id="sk-kbd" name="Slovak kbd" description="Slovak kbd keyboard layout" author="Parag Nemade" version="1.0" contextLength="0" maxKeyLength="1">
  <pattern input="+&apos;a" replacement="ä"/>
  <pattern input="+&apos;o" replacement="ö"/>
  <pattern input="+&apos;u" replacement="ü"/>
  <pattern input="+&apos;A" replacement="Ä"/>
  <pattern input="+&apos;O" replacement="Ö"/>
  <pattern input="+&apos;U" replacement="Ü"/>
  <pattern input="&apos;a" replacement="á"/>
  <pattern input="+a" replacement="ä"/>
  <pattern input="+c" replacement="č"/>
  <pattern input="+d" replacement="ď"/>
  <pattern input="&apos;e" replacement="é"/>
  <pattern input="+e" replacement="ě"/>
  <pattern input="&apos;i" replacement="í"/>
  <pattern input="&apos;l" replacement="ĺ"/>
  <pattern input="+l" replacement="ľ"/>
  <pattern input="+n" replacement="ň"/>
  <pattern input="&apos;o" replacement="ó"/>
  <pattern input="+o" replacement="ô"/>
  <pattern input="^o" replacement="ô"/>
  <pattern input="&apos;r" replacement="ŕ"/>
  <pattern input="+r" replacement="ř"/>
  <pattern input="&apos;s" replacement="ß"/>
  <pattern input="+s" replacement="š"/>
  <pattern input="+t" replacement="ť"/>
  <pattern input="&apos;u" replacement="ú"/>
  <pattern input="+u" replacement="ů"/>
  <pattern input="&apos;z" replacement="ý"/>
  <pattern input="+y" replacement="ž"/>
  <pattern input="&apos;A" replacement="Á"/>
  <pattern input="+A" replacement="Ä"/>
  <pattern input="+C" replacement="Č"/>
  <pattern input="+D" replacement="Ď"/>
  <pattern input="&apos;E" replacement="É"/>
  <pattern input="+E" replacement="Ě"/>
  <pattern input="&apos;I" replacement="Í"/>
  <pattern input="&apos;L" replacement="Ĺ"/>
  <pattern input="+L" replacement="Ľ"/>
  <pattern input="+N" replacement="Ň"/>
  <pattern input="&apos;O" replacement="Ó"/>
  <pattern input="+O" replacement="Ô"/>
  <pattern input="~O" replacement="Ô"/>
  <pattern input="&apos;R" replacement="Ŕ"/>
  <pattern input="+R" replacement="Ř"/>
  <pattern input="&apos;S" replacement="ß"/>
  <pattern input="+S" replacement="Š"/>
  <pattern input="+T" replacement="Ť"/>
  <pattern input="&apos;U" replacement="Ú"/>
  <pattern input="+U" replacement="Ů"/>
  <pattern input="&apos;Z" replacement="Ý"/>
  <pattern input="+Y" replacement="Ž"/>
  <pattern input="&apos;q" replacement="`"/>
  <pattern input="&apos;2" replacement="@"/>
  <pattern input="&apos;3" replacement="#"/>
  <pattern input="&apos;4" replacement="$"/>
  <pattern input="&apos;5" replacement="%"/>
  <pattern input="&apos;6" replacement="^"/>
  <pattern input="&apos;7" replacement="&amp;"/>
  <pattern input="&apos;8" replacement="*"/>
  <pattern input="&apos;9" replacement="("/>
  <pattern input="&apos;0" replacement=")"/>
  <pattern input="+1" replacement="!"/>
  <pattern input="+2" replacement="@"/>
  <pattern input="+3" replacement="#"/>
  <pattern input="+4" replacement="$"/>
  <pattern input="+5" replacement="%"/>
  <pattern input="+6" replacement="^"/>
  <pattern input="+7" replacement="&amp;"/>
  <pattern input="+8" replacement="*"/>
  <pattern input="+9" replacement="("/>
  <pattern input="+0" replacement=")"/>
  <pattern input="1" replacement="+"/>
  <pattern input="2" replacement="ľ"/>
  <pattern input="3" replacement="š"/>
  <pattern input="4" replacement="č"/>
  <pattern input="5" replacement="ť"/>
  <pattern input="6" replacement="ž"/>
  <pattern input="7" replacement="ý"/>
  <pattern input="8" replacement="á"/>
  <pattern input="9" replacement="í"/>
  <pattern input="0" replacement="é"/>
  <pattern input="\!" replacement="1"/>
  <pattern input="\@" replacement="2"/>
  <pattern input="\#" replacement="3"/>
  <pattern input="\$" replacement="4"/>
  <pattern input="\%" replacement="5"/>
  <pattern input="\^" replacement="6"/>
  <pattern input="\&amp;" replacement="7"/>
  <pattern input="\*" replacement="8"/>
  <pattern input="\(" replacement="9"/>
  <pattern input="\)" replacement="0"/>
  <pattern input="\-" replacement="="/>
  <pattern input="\_" replacement="%"/>
  <pattern input="\=" replacement="&apos;"/>
  <pattern input="\[" replacement="ú"/>
  <pattern input="\{" replacement="/"/>
  <pattern input="\]" replacement="ä"/>
  <pattern input="\}" replacement="("/>
  <pattern input="\\" replacement="ň"/>
  <pattern input="\|" replacement=")"/>
  <pattern input=";" replacement="ô"/>
  <pattern input=":" replacement="&quot;"/>
  <pattern input="&apos;" replacement="§"/>
  <pattern input="&quot;" replacement="!"/>
  <pattern input="\&lt;" replacement="?"/>
  <pattern input="\&gt;" replacement=":"/>
  <pattern input="/" replacement="-"/>
  <pattern input="\?" replacement="_"/>
  <pattern input="`" replacement=";"/>
  <pattern input="\~" replacement="^"/>
  <pattern input="y" replacement="z"/>
  <pattern input="z" replacement="y"/>
  <pattern input="Y" replacement="Z"/>
  <pattern input="Z" replacement="Y"/>
</inputmethod>
