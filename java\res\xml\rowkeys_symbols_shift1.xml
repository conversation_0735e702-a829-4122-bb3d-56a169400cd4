<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <Key
        latin:keySpec="~" />
    <Key
        latin:keySpec="`" />
    <Key
        latin:keySpec="|" />
    <!-- U+2022: "•" BULLET -->
    <Key
        latin:keySpec="&#x2022;"
        latin:moreKeys="!text/morekeys_bullet" />
    <!-- U+221A: "√" SQUARE ROOT -->
    <Key
        latin:keySpec="&#x221A;" />
    <!-- U+03C0: "π" GREEK SMALL LETTER PI
         U+03A0: "Π" GREEK CAPITAL LETTER PI -->
    <Key
        latin:keySpec="&#x03C0;"
        latin:moreKeys="&#x03A0;" />
    <!-- U+00F7: "÷" DIVISION SIGN -->
    <Key
        latin:keySpec="&#x00F7;" />
    <!-- U+00D7: "×" MULTIPLICATION SIGN -->
    <Key
        latin:keySpec="&#x00D7;" />
    <!-- U+00B6: "¶" PILCROW SIGN
         U+00A7: "§" SECTION SIGN -->
    <Key
        latin:keySpec="&#x00B6;"
        latin:moreKeys="&#x00A7;" />
    <!-- U+2206: "∆" INCREMENT -->
    <Key
        latin:keySpec="&#x2206;" />
</merge>
