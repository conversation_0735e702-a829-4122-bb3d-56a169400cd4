<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case
            latin:keyboardLayoutSet="dvorak"
        >
            <Key
                latin:keySpec="q"
                latin:moreKeys="!text/morekeys_q,%"
                latin:backgroundType="normal"
                latin:keyStyle="settingsMoreKeysStyle" />
        </case>
        <case
            latin:languageCode="ml|hi|as|kn|te"
            latin:keyboardLayoutSet="qwerty">
            <Key
                latin:keySpec="~"
                latin:moreKeys="!text/keyspec_comma" />
        </case>
        <case
            latin:mode="url"
        >
            <Key
                latin:keySpec="/"
                latin:keyStyle="settingsMoreKeysStyle" />
        </case>
        <case
            latin:mode="email"
        >
            <Key
                latin:keySpec="\@"
                latin:keyStyle="settingsMoreKeysStyle" />
        </case>
        <default>
            <Key
                latin:keySpec="!text/keyspec_comma"
                latin:keyLabelFlags="hasPopupHint"
                latin:keyStyle="settingsMoreKeysStyle" />
        </default>
    </switch>
</merge>
