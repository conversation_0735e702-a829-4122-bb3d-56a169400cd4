<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright © 2013-2014 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC), International Centre for Free and Open Source Software (ICFOSS)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted"
        >
            <Key
                latin:keySpec="&#x0D13;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D0F;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D05;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D07;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D09;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D2B;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D31;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D16;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D25;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D1B;"
                latin:moreKeys=":"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D20;"
                latin:moreKeys="&quot;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
        </case>

        <default>
            <Key
                latin:keySpec="&#x0D4B;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio|autoXScale" />
            <Key
                latin:keySpec="&#x0D47;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D4D;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D3F;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D41;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D2A;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D30;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D15;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D24;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D1A;"
                latin:moreKeys=";"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D1F;"
                latin:moreKeys="'"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
         </default>
    </switch>
</merge>
