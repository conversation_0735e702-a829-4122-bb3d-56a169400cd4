<?xml version="1.0"?>
<inputmethod id="ks-inscript" name="InScript" description="InScript keyboard for Kashmiri language using Devanagari script" author="Parag Nemade" version="1.0" contextLength="1" maxKeyLength="3">
  <pattern input="4" replacement="₹" altGr="true"/>
  <pattern input="Q" replacement="ॵ" altGr="true"/>
  <pattern input="q" replacement="ॏ" altGr="true"/>
  <pattern input="T" replacement="ॷ" altGr="true"/>
  <pattern input="t" replacement="ॗ" altGr="true"/>
  <pattern input="A" replacement="ॴ" altGr="true"/>
  <pattern input="a" replacement="ऻ" altGr="true"/>
  <pattern input="G" replacement="ॶ" altGr="true"/>
  <pattern input="g" replacement="ॖ" altGr="true"/>
  <pattern input="Z" replacement="ॳ" altGr="true"/>
  <pattern input="z" replacement="ऺ" altGr="true"/>
  <pattern input="्d" replacement="्‌"/>
  <pattern input="ग_" replacement="ॻ"/>
  <pattern input="ज_" replacement="ॼ"/>
  <pattern input="ड_" replacement="ॾ"/>
  <pattern input="ब_" replacement="ॿ"/>
  <pattern input="\!" replacement="ऍ"/>
  <pattern input="1" replacement="१"/>
  <pattern input="\@" replacement="ॅ"/>
  <pattern input="2" replacement="२"/>
  <pattern input="\#" replacement="#"/>
  <pattern input="3" replacement="३"/>
  <pattern input="\$" replacement="$"/>
  <pattern input="4" replacement="४"/>
  <pattern input="5" replacement="५"/>
  <pattern input="6" replacement="६"/>
  <pattern input="7" replacement="७"/>
  <pattern input="8" replacement="८"/>
  <pattern input="\(" replacement="("/>
  <pattern input="9" replacement="९"/>
  <pattern input="\)" replacement=")"/>
  <pattern input="0" replacement="०"/>
  <pattern input="\_" replacement="ः"/>
  <pattern input="\-" replacement="-"/>
  <pattern input="\+" replacement="ऋ"/>
  <pattern input="\=" replacement="ृ"/>
  <pattern input="Q" replacement="औ"/>
  <pattern input="q" replacement="ौ"/>
  <pattern input="W" replacement="ऐ"/>
  <pattern input="w" replacement="ै"/>
  <pattern input="E" replacement="आ"/>
  <pattern input="e" replacement="ा"/>
  <pattern input="R" replacement="ई"/>
  <pattern input="r" replacement="ी"/>
  <pattern input="T" replacement="ऊ"/>
  <pattern input="t" replacement="ू"/>
  <pattern input="Y" replacement="भ"/>
  <pattern input="y" replacement="ब"/>
  <pattern input="U" replacement="ङ"/>
  <pattern input="u" replacement="ह"/>
  <pattern input="I" replacement="घ"/>
  <pattern input="i" replacement="ग"/>
  <pattern input="O" replacement="ध"/>
  <pattern input="o" replacement="द"/>
  <pattern input="P" replacement="झ"/>
  <pattern input="p" replacement="ज"/>
  <pattern input="\{" replacement="ढ"/>
  <pattern input="\[" replacement="ड"/>
  <pattern input="\}" replacement="ञ"/>
  <pattern input="\]" replacement="़"/>
  <pattern input="A" replacement="ओ"/>
  <pattern input="a" replacement="ो"/>
  <pattern input="S" replacement="ए"/>
  <pattern input="s" replacement="े"/>
  <pattern input="D" replacement="अ"/>
  <pattern input="d" replacement="्"/>
  <pattern input="F" replacement="इ"/>
  <pattern input="f" replacement="ि"/>
  <pattern input="H" replacement="फ"/>
  <pattern input="h" replacement="प"/>
  <pattern input="J" replacement="ऱ"/>
  <pattern input="j" replacement="र"/>
  <pattern input="K" replacement="ख"/>
  <pattern input="k" replacement="क"/>
  <pattern input="L" replacement="थ"/>
  <pattern input="l" replacement="त"/>
  <pattern input=":" replacement="छ"/>
  <pattern input=";" replacement="च"/>
  <pattern input="&quot;" replacement="ठ"/>
  <pattern input="&apos;" replacement="ट"/>
  <pattern input="\|" replacement="ऑ"/>
  <pattern input="\\" replacement="ॉ"/>
  <pattern input="\~" replacement="ऒ"/>
  <pattern input="\`" replacement="ॊ"/>
  <pattern input="Z" replacement="ऎ"/>
  <pattern input="z" replacement="ॆ"/>
  <pattern input="X" replacement="ँ"/>
  <pattern input="x" replacement="ं"/>
  <pattern input="C" replacement="ण"/>
  <pattern input="c" replacement="म"/>
  <pattern input="V" replacement="ऩ"/>
  <pattern input="v" replacement="न"/>
  <pattern input="B" replacement="ऴ"/>
  <pattern input="b" replacement="व"/>
  <pattern input="N" replacement="ळ"/>
  <pattern input="n" replacement="ल"/>
  <pattern input="M" replacement="श"/>
  <pattern input="m" replacement="स"/>
  <pattern input="\&lt;" replacement="ष"/>
  <pattern input="," replacement=","/>
  <pattern input="\&gt;" replacement="।"/>
  <pattern input="\." replacement="."/>
  <pattern input="\?" replacement="य़"/>
  <pattern input="/" replacement="य"/>
  <pattern input="\%" replacement="ज्ञ"/>
  <pattern input="\^" replacement="त्र"/>
  <pattern input="\&amp;" replacement="क्ष"/>
  <pattern input="\*" replacement="श्र"/>
</inputmethod>
