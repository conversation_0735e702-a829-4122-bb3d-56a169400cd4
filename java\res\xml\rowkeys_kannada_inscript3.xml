<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright © 2013-2014 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC), International Centre for Free and Open Source Software (ICFOSS)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted"
        >
            <Key
                latin:keySpec="&#x0C93;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0C8F;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0C85;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0C87;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0C89;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0CAB;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0CB1;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0C96;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0CA5;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0C9B;"
                latin:moreKeys=":"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0CA0;"
                latin:moreKeys="&quot;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
        </case>

        <default>
            <Key
                latin:keySpec="&#x0CCB;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio|autoXScale" />
            <Key
                latin:keySpec="&#x0CC7;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0CCD;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0CBF;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0CC1;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0CAA;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0CB0;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0C95;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0CA4;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0C9A;"
                latin:moreKeys=";"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0C9F;"
                latin:moreKeys="'"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
         </default>
    </switch>
</merge>
