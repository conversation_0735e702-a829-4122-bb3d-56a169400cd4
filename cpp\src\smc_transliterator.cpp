#include "smc_transliterator.h"
#include <algorithm>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <random>
#include <cmath>

namespace smc {

// TransliterationPattern implementation
TransliterationPattern::TransliterationPattern(const std::string& inp, const std::string& repl, const std::string& ctx)
    : input(inp), replacement(repl), context(ctx), hasContext(!ctx.empty()) {
    try {
        inputRegex = std::regex(inp + "$");
        if (hasContext) {
            contextRegex = std::regex(ctx + "$");
        }
    } catch (const std::regex_error& e) {
        std::cerr << "Regex error for pattern '" << inp << "': " << e.what() << std::endl;
        // Fallback to simple string matching
        hasContext = false;
    }
}

bool TransliterationPattern::matches(const std::string& text, const std::string& context) const {
    try {
        if (!std::regex_search(text, inputRegex)) {
            return false;
        }
        if (hasContext && !std::regex_search(context, contextRegex)) {
            return false;
        }
        return true;
    } catch (const std::regex_error&) {
        // Fallback to simple string matching
        return text.find(input) != std::string::npos;
    }
}

std::string TransliterationPattern::apply(const std::string& text) const {
    try {
        return std::regex_replace(text, inputRegex, replacement);
    } catch (const std::regex_error&) {
        // Fallback to simple replacement
        std::string result = text;
        size_t pos = result.find(input);
        if (pos != std::string::npos) {
            result.replace(pos, input.length(), replacement);
        }
        return result;
    }
}

// Dictionary implementation
bool Dictionary::loadFromFile(const std::string& filename) {
    std::ifstream file(filename, std::ios::in | std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Cannot open dictionary file: " << filename << std::endl;
        return false;
    }
    
    std::string line;
    int lineCount = 0;
    
    while (std::getline(file, line)) {
        lineCount++;
        if (line.empty() || line[0] == '#') continue;
        
        // Parse format: word=தமிழ்,f=254
        size_t wordPos = line.find("word=");
        if (wordPos == std::string::npos) continue;
        
        size_t wordStart = wordPos + 5;
        size_t wordEnd = line.find(',', wordStart);
        if (wordEnd == std::string::npos) wordEnd = line.length();
        
        std::string word = line.substr(wordStart, wordEnd - wordStart);
        if (word.empty()) continue;
        
        // Extract frequency
        int frequency = 1;
        size_t freqPos = line.find("f=", wordEnd);
        if (freqPos != std::string::npos) {
            frequency = std::stoi(line.substr(freqPos + 2));
        }
        
        words.insert(word);
        frequencies[word] = frequency;
        wordList.push_back(word);
        
        if (lineCount % 10000 == 0) {
            std::cout << "Loaded " << lineCount << " words..." << std::endl;
        }
    }
    
    std::cout << "Dictionary loaded: " << words.size() << " words" << std::endl;
    return true;
}

bool Dictionary::contains(const std::string& word) const {
    return words.find(word) != words.end();
}

int Dictionary::getFrequency(const std::string& word) const {
    auto it = frequencies.find(word);
    return (it != frequencies.end()) ? it->second : 0;
}

std::vector<std::pair<std::string, int>> Dictionary::findMatches(const std::string& prefix, int maxResults) const {
    std::vector<std::pair<std::string, int>> matches;
    
    for (const auto& word : wordList) {
        if (word.find(prefix) == 0) { // Starts with prefix
            matches.emplace_back(word, getFrequency(word));
            if (matches.size() >= maxResults) break;
        }
    }
    
    // Sort by frequency (descending)
    std::sort(matches.begin(), matches.end(), 
              [](const auto& a, const auto& b) { return a.second > b.second; });
    
    return matches;
}

std::vector<std::string> Dictionary::searchContaining(const std::string& substring, int maxResults) const {
    std::vector<std::string> results;
    
    for (const auto& word : wordList) {
        if (word.find(substring) != std::string::npos) {
            results.push_back(word);
            if (results.size() >= maxResults) break;
        }
    }
    
    return results;
}

Dictionary::Stats Dictionary::getStats() const {
    Stats stats;
    stats.totalWords = words.size();
    
    if (frequencies.empty()) {
        stats.maxFrequency = stats.minFrequency = 0;
        stats.avgFrequency = 0.0;
        return stats;
    }
    
    int sum = 0;
    stats.maxFrequency = 0;
    stats.minFrequency = INT_MAX;
    
    for (const auto& pair : frequencies) {
        int freq = pair.second;
        sum += freq;
        stats.maxFrequency = std::max(stats.maxFrequency, freq);
        stats.minFrequency = std::min(stats.minFrequency, freq);
    }
    
    stats.avgFrequency = static_cast<double>(sum) / frequencies.size();
    return stats;
}

// SuggestionEngine implementation
SuggestionEngine::SuggestionEngine(const Dictionary* dict) : dictionary(dict) {
    initializeProximityMap();
}

void SuggestionEngine::initializeProximityMap() {
    // QWERTY keyboard proximity map
    std::vector<std::vector<std::string>> proximityGroups = {
        {"q", "w", "a", "s"},
        {"w", "q", "e", "a", "s", "d"},
        {"e", "w", "r", "s", "d", "f"},
        {"r", "e", "t", "d", "f", "g"},
        {"t", "r", "y", "f", "g", "h"},
        {"y", "t", "u", "g", "h", "j"},
        {"u", "y", "i", "h", "j", "k"},
        {"i", "u", "o", "j", "k", "l"},
        {"o", "i", "p", "k", "l"},
        {"p", "o", "l"},
        {"a", "q", "w", "s", "z", "x"},
        {"s", "q", "w", "e", "a", "d", "z", "x", "c"},
        {"d", "w", "e", "r", "s", "f", "x", "c", "v"},
        {"f", "e", "r", "t", "d", "g", "c", "v", "b"},
        {"g", "r", "t", "y", "f", "h", "v", "b", "n"},
        {"h", "t", "y", "u", "g", "j", "b", "n", "m"},
        {"j", "y", "u", "i", "h", "k", "n", "m"},
        {"k", "u", "i", "o", "j", "l", "m"},
        {"l", "i", "o", "p", "k"},
        {"z", "a", "s", "x"},
        {"x", "a", "s", "d", "z", "c"},
        {"c", "s", "d", "f", "x", "v"},
        {"v", "d", "f", "g", "c", "b"},
        {"b", "f", "g", "h", "v", "n"},
        {"n", "g", "h", "j", "b", "m"},
        {"m", "h", "j", "k", "n"}
    };
    
    for (const auto& group : proximityGroups) {
        if (group.empty()) continue;
        std::string mainKey = group[0];
        for (size_t i = 1; i < group.size(); ++i) {
            characterProximity[mainKey + group[i]] = 0.8;
            characterProximity[group[i] + mainKey] = 0.8;
        }
    }
}

double SuggestionEngine::calculateSimilarity(const std::string& word1, const std::string& word2) const {
    if (word1 == word2) return 1.0;
    
    // Simple edit distance-based similarity
    size_t len1 = word1.length();
    size_t len2 = word2.length();
    
    if (len1 == 0) return 0.0;
    if (len2 == 0) return 0.0;
    
    std::vector<std::vector<int>> dp(len1 + 1, std::vector<int>(len2 + 1));
    
    for (size_t i = 0; i <= len1; ++i) dp[i][0] = i;
    for (size_t j = 0; j <= len2; ++j) dp[0][j] = j;
    
    for (size_t i = 1; i <= len1; ++i) {
        for (size_t j = 1; j <= len2; ++j) {
            if (word1[i-1] == word2[j-1]) {
                dp[i][j] = dp[i-1][j-1];
            } else {
                dp[i][j] = 1 + std::min({dp[i-1][j], dp[i][j-1], dp[i-1][j-1]});
            }
        }
    }
    
    int editDistance = dp[len1][len2];
    int maxLen = std::max(len1, len2);
    return 1.0 - (static_cast<double>(editDistance) / maxLen);
}

std::vector<std::pair<std::string, double>> SuggestionEngine::getSuggestions(const std::string& input, int maxSuggestions) const {
    std::vector<std::pair<std::string, double>> suggestions;
    
    if (!dictionary) return suggestions;
    
    // Find prefix matches first
    auto prefixMatches = dictionary->findMatches(input, maxSuggestions * 2);
    for (const auto& match : prefixMatches) {
        double score = 1.0 + (match.second / 254.0); // Boost by frequency
        suggestions.emplace_back(match.first, score);
    }
    
    // Find similarity matches
    if (suggestions.size() < maxSuggestions) {
        auto containingMatches = dictionary->searchContaining(input, maxSuggestions * 3);
        for (const auto& word : containingMatches) {
            double similarity = calculateSimilarity(input, word);
            if (similarity > 0.3) { // Threshold for relevance
                double frequency = dictionary->getFrequency(word) / 254.0;
                double score = similarity + frequency * 0.5;
                suggestions.emplace_back(word, score);
            }
        }
    }
    
    // Sort by score and remove duplicates
    std::sort(suggestions.begin(), suggestions.end(),
              [](const auto& a, const auto& b) { return a.second > b.second; });
    
    // Remove duplicates
    suggestions.erase(std::unique(suggestions.begin(), suggestions.end(),
                                 [](const auto& a, const auto& b) { return a.first == b.first; }),
                     suggestions.end());
    
    if (suggestions.size() > maxSuggestions) {
        suggestions.resize(maxSuggestions);
    }
    
    return suggestions;
}

std::vector<std::string> SuggestionEngine::getContextualSuggestions(const std::string& input, const std::string& context) const {
    // For now, return regular suggestions
    // In a full implementation, this would use bigram/trigram models
    auto suggestions = getSuggestions(input, 5);
    std::vector<std::string> result;
    for (const auto& suggestion : suggestions) {
        result.push_back(suggestion.first);
    }
    return result;
}

// SMCTransliterator implementation
SMCTransliterator::SMCTransliterator() : maxKeyLength(4), contextLength(1) {
    dictionary = std::make_unique<Dictionary>();
}

bool SMCTransliterator::initialize(const std::string& xmlFile, const std::string& dictFile) {
    std::cout << "🚀 Initializing SMC Transliterator..." << std::endl;

    // Load transliteration patterns
    bool patternsLoaded = false;
    if (!xmlFile.empty() && utils::fileExists(xmlFile)) {
        patternsLoaded = loadPatternsFromXML(xmlFile);
        std::cout << "📋 Loaded patterns from XML: " << xmlFile << std::endl;
    }

    if (!patternsLoaded) {
        loadBuiltinPatterns();
        std::cout << "📋 Using built-in patterns" << std::endl;
    }

    // Load dictionary
    bool dictLoaded = false;
    if (!dictFile.empty() && utils::fileExists(dictFile)) {
        dictLoaded = dictionary->loadFromFile(dictFile);
        std::cout << "📚 Loaded dictionary from: " << dictFile << std::endl;
    }

    if (!dictLoaded) {
        std::cout << "⚠️ Dictionary not loaded - using pattern-only mode" << std::endl;
    }

    // Initialize suggestion engine
    if (dictLoaded) {
        suggestionEngine = std::make_unique<SuggestionEngine>(dictionary.get());
        std::cout << "🧠 Suggestion engine initialized" << std::endl;
    }

    std::cout << "✅ SMC Transliterator ready!" << std::endl;
    std::cout << "   Patterns: " << patterns.size() << std::endl;
    std::cout << "   Dictionary: " << (dictionary ? dictionary->size() : 0) << " words" << std::endl;

    return true;
}

bool SMCTransliterator::loadPatternsFromXML(const std::string& xmlFile) {
    // XML parsing would go here - for now return false to use builtin patterns
    return false;
}

void SMCTransliterator::loadBuiltinPatterns() {
    // Real SMC patterns extracted from ta_transliteration.xml
    std::vector<std::tuple<std::string, std::string, std::string>> patternData = {
        // Complex patterns first (order matters!)
        {"([க-ஹ])்a", "$1", ""},
        {"([க-ஹ])(்A|a)", "$1ா", ""},
        {"([க-ஹ])்i", "$1ி", ""},
        {"([க-ஹ])(்I|ிi)", "$1ீ", ""},
        {"([க-ஹ])்u", "$1ு", ""},
        {"([க-ஹ])(்U|ுu)", "$1ூ", ""},
        {"([க-ஹ])்e", "$1ெ", ""},
        {"([க-ஹ])(்E|ெe)", "$1ே", ""},
        {"([க-ஹ])i", "$1ை", ""},
        {"([க-ஹ])்o", "$1ொ", ""},
        {"([க-ஹ])(்O|ொo)", "$1ோ", ""},
        {"([க-ஹ])u", "$1ௌ", ""},

        // Vowel modifications
        {"அa", "ஆ", ""},
        {"இi", "ஈ", ""},
        {"உu", "ஊ", ""},
        {"எe", "ஏ", ""},
        {"அi", "ஐ", ""},
        {"ஒo", "ஓ", ""},
        {"அu", "ஔ", ""},

        // Basic characters
        {"a", "அ", ""}, {"b", "ப்", ""}, {"c", "ச்", ""}, {"d", "ட்", ""}, {"e", "எ", ""},
        {"f", "ஃப்", ""}, {"g", "க்", ""}, {"h", "ஹ்", ""}, {"i", "இ", ""}, {"j", "ஜ்", ""},
        {"k", "க்", ""}, {"l", "ல்", ""}, {"m", "ம்", ""}, {"n", "ன்", ""}, {"o", "ஒ", ""},
        {"p", "ப்", ""}, {"q", "ஃ", ""}, {"r", "ர்", ""}, {"s", "ச்", ""}, {"t", "ட்", ""},
        {"u", "உ", ""}, {"v", "வ்", ""}, {"w", "ந்", ""}, {"y", "ய்", ""}, {"z", "ழ்", ""},

        // Capital letters
        {"A", "ஆ", ""}, {"B", "ப்", ""}, {"C", "க்க்", ""}, {"E", "ஏ", ""}, {"F", "ஃப்", ""},
        {"G", "க்", ""}, {"H", "ஃ", ""}, {"I", "ஈ", ""}, {"J", "ஜ்ஜ்", ""}, {"K", "க்", ""},
        {"L", "ள்", ""}, {"M", "ம்ம்", ""}, {"N", "ண்", ""}, {"O", "ஓ", ""}, {"P", "ப்ப்", ""},
        {"Q", "ஃ", ""}, {"R", "ற்", ""}, {"S", "ஸ்", ""}, {"T", "ட்", ""}, {"U", "ஊ", ""},
        {"V", "வ்வ்", ""}, {"W", "வ்வ்", ""}, {"Y", "ய்ய்", ""}, {"Z", "ஶ்", ""}
    };

    for (const auto& [input, replacement, context] : patternData) {
        patterns.push_back(std::make_unique<TransliterationPattern>(input, replacement, context));
    }
}

std::string SMCTransliterator::applyPatterns(const std::string& input, const std::string& context) const {
    for (const auto& pattern : patterns) {
        if (pattern->matches(input, context)) {
            return pattern->apply(input);
        }
    }
    return input;
}

std::string SMCTransliterator::transliterate(const std::string& input, const std::string& context) const {
    auto start = std::chrono::high_resolution_clock::now();

    // Check cache first
    std::string cacheKey = input + "|" + context;
    auto cacheIt = transliterationCache.find(cacheKey);
    if (cacheIt != transliterationCache.end()) {
        perfStats.cacheHits++;
        return cacheIt->second;
    }

    perfStats.cacheMisses++;
    std::string result = applyPatterns(input, context);

    // Cache the result
    transliterationCache[cacheKey] = result;

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    perfStats.avgProcessingTime = (perfStats.avgProcessingTime * perfStats.totalTransliterations + duration.count()) / (perfStats.totalTransliterations + 1);
    perfStats.totalTransliterations++;

    return result;
}

std::string SMCTransliterator::transliterateAll(const std::string& input) const {
    std::string result;
    std::string context;

    for (size_t i = 0; i < input.length(); ++i) {
        char currentChar = input[i];
        std::string charStr(1, currentChar);

        // Build the substring to transliterate
        std::string toTransliterate = result;
        if (toTransliterate.length() > maxKeyLength) {
            toTransliterate = toTransliterate.substr(toTransliterate.length() - maxKeyLength);
        }
        toTransliterate += charStr;

        // Apply transliteration
        std::string transliterated = transliterate(toTransliterate, context);

        // Find the divergence point and update result
        size_t divergence = 0;
        size_t minLen = std::min(toTransliterate.length(), transliterated.length());
        for (size_t j = 0; j < minLen; ++j) {
            if (toTransliterate[j] != transliterated[j]) {
                divergence = j;
                break;
            }
        }

        // Update result
        if (result.length() >= maxKeyLength) {
            result = result.substr(0, result.length() - maxKeyLength + divergence) + transliterated.substr(divergence);
        } else {
            result = transliterated;
        }

        // Update context
        context += charStr;
        if (context.length() > contextLength) {
            context = context.substr(context.length() - contextLength);
        }
    }

    return result;
}

std::string SMCTransliterator::transliterateSmart(const std::string& input) const {
    if (!dictionary) {
        return transliterateAll(input);
    }

    // Try to find exact matches in dictionary first
    auto suggestions = getSuggestions(input, 1);
    if (!suggestions.empty()) {
        return suggestions[0];
    }

    // Fall back to character-by-character
    return transliterateAll(input);
}

std::vector<std::string> SMCTransliterator::getSuggestions(const std::string& input, int maxSuggestions) const {
    std::vector<std::string> result;

    if (suggestionEngine) {
        auto suggestions = suggestionEngine->getSuggestions(input, maxSuggestions);
        for (const auto& suggestion : suggestions) {
            result.push_back(suggestion.first);
        }
    }

    // If no suggestions from dictionary, provide transliterated version
    if (result.empty()) {
        result.push_back(transliterateAll(input));
    }

    return result;
}

std::vector<std::string> SMCTransliterator::getContextualSuggestions(const std::string& input, const std::string& context) const {
    return suggestionEngine ? suggestionEngine->getContextualSuggestions(input, context) : getSuggestions(input, 5);
}

std::vector<std::string> SMCTransliterator::searchDictionary(const std::string& query, int maxResults) const {
    return dictionary ? dictionary->searchContaining(query, maxResults) : std::vector<std::string>();
}

std::string SMCTransliterator::processCharacterByCharacter(const std::string& input) const {
    return transliterateAll(input);
}

std::string SMCTransliterator::simulateRealTimeTyping(const std::string& input) const {
    std::string result;
    for (size_t i = 1; i <= input.length(); ++i) {
        std::string partial = input.substr(0, i);
        result = transliterateAll(partial);
        std::cout << "Typed: " << std::setw(15) << std::left << partial << " -> Tamil: " << result << std::endl;
    }
    return result;
}

Dictionary::Stats SMCTransliterator::getDictionaryStats() const {
    return dictionary ? dictionary->getStats() : Dictionary::Stats{0, 0, 0, 0.0};
}

// Utility functions implementation
namespace utils {

std::string trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\n\r");
    if (start == std::string::npos) return "";
    size_t end = str.find_last_not_of(" \t\n\r");
    return str.substr(start, end - start + 1);
}

std::vector<std::string> split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;
    while (std::getline(ss, token, delimiter)) {
        tokens.push_back(token);
    }
    return tokens;
}

std::string toLowerCase(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

bool fileExists(const std::string& filename) {
    std::ifstream file(filename);
    return file.good();
}

std::string getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

bool isTamilCharacter(const std::string& utf8Char) {
    // Simple check for Tamil Unicode range (U+0B80–U+0BFF)
    if (utf8Char.empty()) return false;
    unsigned char first = utf8Char[0];
    return (first >= 0xE0 && utf8Char.length() >= 3);
}

std::vector<std::string> splitUTF8(const std::string& utf8String) {
    std::vector<std::string> result;
    for (size_t i = 0; i < utf8String.length(); ) {
        size_t charLen = 1;
        unsigned char c = utf8String[i];

        if (c >= 0xF0) charLen = 4;
        else if (c >= 0xE0) charLen = 3;
        else if (c >= 0xC0) charLen = 2;

        if (i + charLen <= utf8String.length()) {
            result.push_back(utf8String.substr(i, charLen));
        }
        i += charLen;
    }
    return result;
}

size_t utf8Length(const std::string& utf8String) {
    return splitUTF8(utf8String).size();
}

} // namespace utils

// SMCInterface implementation
SMCInterface::SMCInterface() : running(false) {
    transliterator = std::make_unique<SMCTransliterator>();
}

bool SMCInterface::initialize(const std::string& xmlFile, const std::string& dictFile) {
    return transliterator->initialize(xmlFile, dictFile);
}

void SMCInterface::showWelcome() const {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "🎉 Complete SMC Tamil Transliteration Engine (C++)" << std::endl;
    std::cout << "   Professional-grade transliteration with full integration" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    std::cout << "Dictionary: " << transliterator->getDictionarySize() << " Tamil words" << std::endl;
    std::cout << "Patterns: " << transliterator->getMaxKeyLength() << " max key length" << std::endl;
    std::cout << std::string(60, '-') << std::endl;
}

void SMCInterface::showHelp() const {
    std::cout << "\nAvailable Commands:" << std::endl;
    std::cout << "  <word>              - Transliterate word" << std::endl;
    std::cout << "  suggest <word>      - Get suggestions" << std::endl;
    std::cout << "  search <tamil>      - Search dictionary" << std::endl;
    std::cout << "  realtime <word>     - Simulate real-time typing" << std::endl;
    std::cout << "  stats               - Show statistics" << std::endl;
    std::cout << "  test                - Run batch tests" << std::endl;
    std::cout << "  clear               - Clear cache" << std::endl;
    std::cout << "  help                - Show this help" << std::endl;
    std::cout << "  quit/exit           - Exit program" << std::endl;
}

void SMCInterface::showStats() const {
    auto dictStats = transliterator->getDictionaryStats();
    auto perfStats = transliterator->getPerformanceStats();

    std::cout << "\n📊 Statistics:" << std::endl;
    std::cout << "Dictionary:" << std::endl;
    std::cout << "  Total words: " << dictStats.totalWords << std::endl;
    std::cout << "  Frequency range: " << dictStats.minFrequency << " - " << dictStats.maxFrequency << std::endl;
    std::cout << "  Average frequency: " << std::fixed << std::setprecision(2) << dictStats.avgFrequency << std::endl;

    std::cout << "Performance:" << std::endl;
    std::cout << "  Total transliterations: " << perfStats.totalTransliterations << std::endl;
    std::cout << "  Cache hits: " << perfStats.cacheHits << std::endl;
    std::cout << "  Cache misses: " << perfStats.cacheMisses << std::endl;
    if (perfStats.totalTransliterations > 0) {
        double hitRate = (double)perfStats.cacheHits / (perfStats.cacheHits + perfStats.cacheMisses) * 100;
        std::cout << "  Cache hit rate: " << std::fixed << std::setprecision(1) << hitRate << "%" << std::endl;
    }
    std::cout << "  Avg processing time: " << std::fixed << std::setprecision(2) << perfStats.avgProcessingTime << " μs" << std::endl;
}

void SMCInterface::runBatchTest() const {
    std::vector<std::string> testWords = {
        "vanakkam", "nandri", "amma", "appa", "tamil",
        "computer", "school", "college", "hospital",
        "chennai", "madurai", "coimbatore", "salem",
        "thiruvananthapuram", "krishna", "rama", "sita"
    };

    std::cout << "\n🧪 Batch Test Results:" << std::endl;
    std::cout << std::string(50, '-') << std::endl;

    for (const auto& word : testWords) {
        std::string result = transliterator->transliterateSmart(word);
        auto suggestions = transliterator->getSuggestions(word, 3);

        std::cout << std::setw(20) << std::left << word << " -> " << result << std::endl;
        if (suggestions.size() > 1) {
            std::cout << std::setw(20) << "" << "    Alternatives: ";
            for (size_t i = 1; i < suggestions.size(); ++i) {
                std::cout << suggestions[i];
                if (i < suggestions.size() - 1) std::cout << ", ";
            }
            std::cout << std::endl;
        }
    }
}

void SMCInterface::simulateRealTime(const std::string& word) const {
    std::cout << "\n⏱️ Real-time simulation for: " << word << std::endl;
    std::cout << std::string(50, '-') << std::endl;
    transliterator->simulateRealTimeTyping(word);
}

void SMCInterface::processCommand(const std::string& command) {
    std::string trimmedCommand = utils::trim(command);
    if (trimmedCommand.empty()) return;

    std::vector<std::string> parts = utils::split(trimmedCommand, ' ');
    std::string cmd = utils::toLowerCase(parts[0]);

    if (cmd == "quit" || cmd == "exit") {
        running = false;
        std::cout << "Thank you for using SMC Transliterator!" << std::endl;
    }
    else if (cmd == "help") {
        showHelp();
    }
    else if (cmd == "stats") {
        showStats();
    }
    else if (cmd == "test") {
        runBatchTest();
    }
    else if (cmd == "clear") {
        transliterator->clearCache();
        std::cout << "Cache cleared." << std::endl;
    }
    else if (cmd == "suggest" && parts.size() > 1) {
        std::string word = parts[1];
        auto suggestions = transliterator->getSuggestions(word, 10);
        std::cout << "Suggestions for '" << word << "':" << std::endl;
        for (size_t i = 0; i < suggestions.size(); ++i) {
            std::cout << "  " << (i + 1) << ". " << suggestions[i] << std::endl;
        }
    }
    else if (cmd == "search" && parts.size() > 1) {
        std::string query = parts[1];
        auto results = transliterator->searchDictionary(query, 15);
        std::cout << "Dictionary search for '" << query << "':" << std::endl;
        for (size_t i = 0; i < results.size(); ++i) {
            std::cout << "  " << (i + 1) << ". " << results[i] << std::endl;
        }
    }
    else if (cmd == "realtime" && parts.size() > 1) {
        std::string word = parts[1];
        simulateRealTime(word);
    }
    else {
        // Default: transliterate the input
        std::string result = transliterator->transliterateSmart(trimmedCommand);
        std::cout << "Tamil: " << result << std::endl;

        // Show suggestions for longer words
        if (trimmedCommand.length() > 3) {
            auto suggestions = transliterator->getSuggestions(trimmedCommand, 3);
            if (suggestions.size() > 1) {
                std::cout << "Other options: ";
                for (size_t i = 1; i < suggestions.size(); ++i) {
                    std::cout << suggestions[i];
                    if (i < suggestions.size() - 1) std::cout << ", ";
                }
                std::cout << std::endl;
            }
        }
    }
}

void SMCInterface::run() {
    showWelcome();
    showHelp();

    running = true;
    std::string input;

    while (running) {
        std::cout << "\nTamil> ";
        std::getline(std::cin, input);

        if (std::cin.eof()) {
            running = false;
            break;
        }

        processCommand(input);
    }
}

void SMCInterface::runTests() const {
    std::cout << "🧪 Running comprehensive tests..." << std::endl;
    runBatchTest();
    showStats();
}

} // namespace smc
