<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<PreferenceScreen
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:latin="http://schemas.android.com/apk/res-auto"
    android:title="@string/settings_screen_advanced"
    android:key="screen_advanced">
    <!-- TODO: consolidate key preview dismiss delay with the key preview animation parameters. -->
    <ListPreference
        android:key="pref_key_preview_popup_dismiss_delay"
        android:title="@string/key_preview_popup_dismiss_delay" />
    <in.androidtweak.inputmethod.indic.settings.SeekBarDialogPreference
        android:key="pref_vibration_duration_settings"
        android:title="@string/prefs_keypress_vibration_duration_settings"
        latin:maxValue="@integer/config_max_vibration_duration" />
    <in.androidtweak.inputmethod.indic.settings.SeekBarDialogPreference
        android:key="pref_keypress_sound_volume"
        android:title="@string/prefs_keypress_sound_volume_settings"
        latin:maxValue="100" /> <!-- percent -->
    <!-- The settings for showing setup wizard application icon shouldn't be persistent and
         the default value is added programmatically. -->
    <CheckBoxPreference
        android:key="pref_show_setup_wizard_icon"
        android:title="@string/show_setup_wizard_icon"
        android:summary="@string/show_setup_wizard_icon_summary" />
    <!-- title will be set programmatically to embed application name -->
    <CheckBoxPreference
        android:key="pref_enable_metrics_logging"
        android:summary="@string/enable_metrics_logging_summary"
        android:defaultValue="true"
        android:persistent="true" />
    <PreferenceScreen
        android:fragment="in.androidtweak.inputmethod.indic.settings.DebugSettingsFragment"
        android:key="screen_debug"
        android:title="Debug settings"
        android:defaultValue="false"
        android:persistent="true" />
</PreferenceScreen>
