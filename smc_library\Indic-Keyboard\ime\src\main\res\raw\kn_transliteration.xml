<?xml version="1.0"?>
<inputmethod id="kn-transliteration" name="ಲಿಪ್ಯಂತರಣ" description="Kannada transliteration" author="<PERSON> <PERSON>, HP Nadig " version="1.0" contextLength="5" maxKeyLength="3">
  <pattern input="ಕ್h" context="c" replacement="ಚ್"/>
  <pattern input="\\([A-Za-z\&gt;_~\.0-9])" context="\\" replacement="$1"/>
  <pattern input="ಜ್್j" replacement="ಜ್ಞ್"/>
  <pattern input="([ಕ-ಹೞ]಼?)್a" replacement="$1"/>
  <pattern input="([ಕ-ಹೞ]಼?)್A" replacement="$1ಾ"/>
  <pattern input="([ಕ-ಹೞ]಼?)a" replacement="$1ಾ"/>
  <pattern input="([ಕ-ಹೞ]಼?)್i" replacement="$1ಿ"/>
  <pattern input="([ಕ-ಹೞ]಼?)(್I|ಿi|ೆe)" replacement="$1ೀ"/>
  <pattern input="([ಕ-ಹೞ]಼?)್u" replacement="$1ು"/>
  <pattern input="([ಕ-ಹೞ]಼?)(ುu|್U|ೊo)" replacement="$1ೂ"/>
  <pattern input="([ಕ-ಹೞ]಼?)್R" replacement="$1ೃ"/>
  <pattern input="([ಕ-ಹೞ]಼?)ೃR" replacement="$1ೄ"/>
  <pattern input="([ಕ-ಹೞ]಼?)್e" replacement="$1ೆ"/>
  <pattern input="([ಕ-ಹೞ]಼?)್E" replacement="$1ೇ"/>
  <pattern input="([ಕ-ಹೞ]಼?)i" replacement="$1ೈ"/>
  <pattern input="([ಕ-ಹೞ]಼?)್o" replacement="$1ೊ"/>
  <pattern input="([ಕ-ಹೞ]಼?)್O" replacement="$1ೋ"/>
  <pattern input="([ಕ-ಹೞ]಼?)u" replacement="$1ೌ"/>
  <pattern input="([ಕ-ಹೞ])್\`" replacement="$1़್"/>
  <pattern input="([ಕ-ಹೞ]಼?)್W" replacement="$1ೌ"/>
  <pattern input="([ಕ-ಹೞ]಼?)್Y" replacement="$1ೈ"/>
  <pattern input="ಅa" replacement="ಆ"/>
  <pattern input="(ಒo|ಉu)" replacement="ಊ"/>
  <pattern input="ಅi" replacement="ಐ"/>
  <pattern input="(ಇi|ಎe)" replacement="ಈ"/>
  <pattern input="ಅu" replacement="ಔ"/>
  <pattern input="ಋR" replacement="ೠ"/>
  <pattern input="ಓ~M" replacement="ॐ"/>
  <pattern input="~l" replacement="ೞ್"/>
  <pattern input="~h" replacement="ಹ್"/>
  <pattern input="~r" replacement="ಱ್"/>
  <pattern input="q" replacement="ಱ್"/>
  <pattern input="Q" replacement="ೞ್"/>
  <pattern input="ಕ್h" replacement="ಖ್"/>
  <pattern input="ಗ್h" replacement="ಘ್"/>
  <pattern input="~g" replacement="ಙ್"/>
  <pattern input="~N" replacement="ಙ್"/>
  <pattern input="ಚ್h" replacement="ಛ್"/>
  <pattern input="ಜ್h" replacement="ಝ್"/>
  <pattern input="~j" replacement="ಞ್"/>
  <pattern input="~n" replacement="ಞ್"/>
  <pattern input="ಟ್h" replacement="ಠ್"/>
  <pattern input="ಡ್h" replacement="ಢ್"/>
  <pattern input="ತ್h" replacement="ಥ್"/>
  <pattern input="ದ್h" replacement="ಧ್"/>
  <pattern input="ಪ್h" replacement="ಫ್"/>
  <pattern input="ಬ್h" replacement="ಭ್"/>
  <pattern input="ಸ್h" replacement="ಶ್"/>
  <pattern input="ಶ್h" replacement="ಷ್"/>
  <pattern input="ಋa" replacement="ರ"/>
  <pattern input="ಋA" replacement="ರಾ"/>
  <pattern input="ಋi" replacement="ರಿ"/>
  <pattern input="ಋI" replacement="ರೀ"/>
  <pattern input="ಋu" replacement="ರು"/>
  <pattern input="ಋU" replacement="ರೂ"/>
  <pattern input="ಋe" replacement="ರೆ"/>
  <pattern input="ಋE" replacement="ರೇ"/>
  <pattern input="X" replacement="‌"/>
  <pattern input="x" replacement="‍"/>
  <pattern input="([ಕ-ಹೞ]಼?)ೃa" replacement="$1್ರ"/>
  <pattern input="([ಕ-ಹೞ]಼?)ೃA" replacement="$1್ರಾ"/>
  <pattern input="([ಕ-ಹೞ]಼?)ೃi" replacement="$1್ರಿ"/>
  <pattern input="([ಕ-ಹೞ]಼?)ೃI" replacement="$1್ರೀ"/>
  <pattern input="([ಕ-ಹೞ]಼?)ೃu" replacement="$1್ರು"/>
  <pattern input="([ಕ-ಹೞ]಼?)ೃU" replacement="$1್ರೂ"/>
  <pattern input="([ಕ-ಹೞ]಼?)ೃe" replacement="$1್ರೆ"/>
  <pattern input="([ಕ-ಹೞ]಼?)ೃE" replacement="$1್ರೇ"/>
  <pattern input="([ಕ-ಹೞ]಼?)ೃo" replacement="$1್ರೊ"/>
  <pattern input="([ಕ-ಹೞ]಼?)ೃO" replacement="$1್ರೋ"/>
  <pattern input="([ಕ-ಹೞ]಼?)ೃ\~" replacement="$1್ರ್"/>
  <pattern input="।\." replacement="॥"/>
  <pattern input="a" replacement="ಅ"/>
  <pattern input="b" replacement="ಬ್"/>
  <pattern input="c" replacement="ಚ್"/>
  <pattern input="d" replacement="ದ್"/>
  <pattern input="e" replacement="ಎ"/>
  <pattern input="g" replacement="ಗ್"/>
  <pattern input="h" replacement="ಹ್"/>
  <pattern input="i" replacement="ಇ"/>
  <pattern input="j" replacement="ಜ್"/>
  <pattern input="k" replacement="ಕ್"/>
  <pattern input="l" replacement="ಲ್"/>
  <pattern input="m" replacement="ಮ್"/>
  <pattern input="n" replacement="ನ್"/>
  <pattern input="o" replacement="ಒ"/>
  <pattern input="p" replacement="ಪ್"/>
  <pattern input="r" replacement="ರ್"/>
  <pattern input="s" replacement="ಸ್"/>
  <pattern input="t" replacement="ತ್"/>
  <pattern input="u" replacement="ಉ"/>
  <pattern input="(v|w)" replacement="ವ್"/>
  <pattern input="y" replacement="ಯ್"/>
  <pattern input="A" replacement="ಆ"/>
  <pattern input="B" replacement="ಭ್"/>
  <pattern input="C" replacement="ಛ್"/>
  <pattern input="D" replacement="ಡ್"/>
  <pattern input="E" replacement="ಏ"/>
  <pattern input="G" replacement="ಘ್"/>
  <pattern input="H" replacement="ಃ"/>
  <pattern input="I" replacement="ಈ"/>
  <pattern input="J" replacement="ಝ್"/>
  <pattern input="K" replacement="ಖ್"/>
  <pattern input="L" replacement="ಳ್"/>
  <pattern input="M" replacement="ಂ"/>
  <pattern input="N" replacement="ಣ್"/>
  <pattern input="O" replacement="ಓ"/>
  <pattern input="P" replacement="ಫ್"/>
  <pattern input="R" replacement="ಋ"/>
  <pattern input="S" replacement="ಶ್"/>
  <pattern input="T" replacement="ಟ್"/>
  <pattern input="U" replacement="ಊ"/>
  <pattern input="V" replacement="ವ್"/>
  <pattern input="W" context="" replacement="ಔ"/>
  <pattern input="Y" replacement="ಐ"/>
  <pattern input="(z|Z)" replacement="ಜ಼್"/>
  <pattern input="(f|F)" replacement="ಫ಼್"/>
  <pattern input="0" replacement="೦"/>
  <pattern input="1" replacement="೧"/>
  <pattern input="2" replacement="೨"/>
  <pattern input="3" replacement="೩"/>
  <pattern input="4" replacement="೪"/>
  <pattern input="5" replacement="೫"/>
  <pattern input="6" replacement="೬"/>
  <pattern input="7" replacement="೭"/>
  <pattern input="8" replacement="೮"/>
  <pattern input="9" replacement="೯"/>
  <pattern input="//" replacement="ಽ"/>
</inputmethod>
