<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources xmlns:android="http://schemas.android.com/apk/res/android">
    <style name="KeyboardTheme.LXX_Dark" parent="KeyboardIcons.LXX_Dark">
        <item name="keyboardStyle">@style/Keyboard.LXX_Dark</item>
        <item name="keyboardViewStyle">@style/KeyboardView.LXX_Dark</item>
        <item name="mainKeyboardViewStyle">@style/MainKeyboardView.LXX_Dark</item>
        <item name="emojiPalettesViewStyle">@style/EmojiPalettesView.LXX_Dark</item>
        <item name="moreKeysKeyboardStyle">@style/MoreKeysKeyboard.LXX_Dark</item>
        <item name="moreKeysKeyboardViewStyle">@style/MoreKeysKeyboardView.LXX_Dark</item>
        <item name="moreKeysKeyboardViewForActionStyle">@style/MoreKeysKeyboardView.LXX_Dark.Action</item>
        <item name="suggestionStripViewStyle">@style/SuggestionStripView.LXX_Dark</item>
        <item name="suggestionWordStyle">@style/SuggestionWord.LXX_Dark</item>
    </style>
    <style
        name="Keyboard.LXX_Dark"
        parent="Keyboard"
    >
        <!-- This should be aligned with KeyboardTheme.THEME_ID_* -->
        <item name="themeId">LXXDark</item>
    </style>
    <style
        name="KeyboardView.LXX_Dark"
        parent="KeyboardView.LXX"
    >
        <item name="android:background">@drawable/keyboard_background_lxx_dark</item>
        <item name="keyBackground">@drawable/btn_keyboard_key_lxx_dark</item>
        <item name="functionalKeyBackground">@drawable/btn_keyboard_key_functional_lxx_dark</item>
        <item name="spacebarBackground">@drawable/btn_keyboard_spacebar_lxx_dark</item>
        <item name="keyTextColor">@color/key_text_color_lxx_dark</item>
        <item name="keyTextInactivatedColor">@color/key_functional_text_color_lxx_dark</item>
        <item name="functionalTextColor">@color/key_text_color_lxx_dark</item>
        <item name="keyHintLetterColor">@color/key_hint_letter_color_lxx_dark</item>
        <item name="keyHintLabelColor">@color/key_text_inactive_color_lxx_dark</item>
        <item name="keyShiftedLetterHintInactivatedColor">@color/key_text_inactive_color_lxx_dark</item>
        <item name="keyShiftedLetterHintActivatedColor">@color/key_text_color_lxx_dark</item>
        <item name="keyPreviewTextColor">@color/key_text_color_lxx_dark</item>
    </style>
    <style
        name="MainKeyboardView.LXX_Dark"
        parent="KeyboardView.LXX_Dark"
    >
        <item name="moreKeysKeyboardForActionLayout">@layout/more_keys_keyboard_for_action_lxx</item>
        <item name="keyPreviewBackground">@drawable/keyboard_key_feedback_lxx_dark</item>
        <item name="keyPreviewHeight">@dimen/config_key_preview_height_lxx</item>
        <item name="keyPreviewOffset">@dimen/config_key_preview_offset_lxx</item>
        <item name="keyPreviewShowUpAnimator">@anim/key_preview_show_up_lxx</item>
        <item name="keyPreviewDismissAnimator">@anim/key_preview_dismiss_lxx</item>
        <item name="gestureFloatingPreviewTextColor">@color/auto_correct_color_lxx_dark</item>
        <item name="gestureFloatingPreviewColor">@color/gesture_floating_preview_color_lxx_dark</item>
        <item name="gestureTrailColor">@color/gesture_trail_color_lxx_dark</item>
        <item name="slidingKeyInputPreviewColor">@color/sliding_key_input_preview_color_lxx_dark</item>
        <item name="languageOnSpacebarTextColor">@color/key_text_inactive_color_lxx_dark</item>
        <!-- A negative value to disable text shadow layer. -->
        <item name="languageOnSpacebarTextShadowRadius">-1.0</item>
    </style>
    <!-- Though {@link EmojiPalettesView} doesn't extend {@link KeyboardView}, some views inside it,
         for instance delete button, need themed {@link KeyboardView} attributes. -->
    <style
        name="EmojiPalettesView.LXX_Dark"
        parent="MainKeyboardView.LXX_Dark"
    >
        <item name="categoryIndicatorEnabled">false</item>
        <item name="categoryPageIndicatorColor">@color/highlight_color_lxx_dark</item>
        <item name="categoryPageIndicatorBackground">@color/emoji_tab_page_indicator_background_lxx_dark</item>
        <item name="iconEmojiRecentsTab">@drawable/ic_emoji_recents_lxx_dark</item>
        <item name="iconEmojiCategory1Tab">@drawable/ic_emoji_people_lxx_dark</item>
        <item name="iconEmojiCategory2Tab">@drawable/ic_emoji_objects_lxx_dark</item>
        <item name="iconEmojiCategory3Tab">@drawable/ic_emoji_nature_lxx_dark</item>
        <item name="iconEmojiCategory4Tab">@drawable/ic_emoji_places_lxx_dark</item>
        <item name="iconEmojiCategory5Tab">@drawable/ic_emoji_symbols_lxx_dark</item>
        <item name="iconEmojiCategory6Tab">@drawable/ic_emoji_emoticons_lxx_dark</item>
    </style>
    <style
        name="MoreKeysKeyboard.LXX_Dark"
        parent="Keyboard.LXX_Dark"
    >
        <item name="keyboardTopPadding">0%p</item>
        <item name="keyboardBottomPadding">0%p</item>
        <item name="horizontalGap">0%p</item>
        <item name="touchPositionCorrectionData">@null</item>
    </style>
    <style
        name="MoreKeysKeyboardView.LXX_Dark"
        parent="KeyboardView.LXX_Dark"
    >
        <item name="android:background">@drawable/keyboard_popup_panel_background_lxx_dark</item>
        <item name="keyBackground">@drawable/btn_keyboard_key_popup_lxx_dark</item>
        <item name="divider">@drawable/more_keys_divider</item>
        <item name="keyTypeface">normal</item>
        <item name="verticalCorrection">@dimen/config_more_keys_keyboard_vertical_correction_holo</item>
    </style>
    <style
        name="MoreKeysKeyboardView.LXX_Dark.Action"
        parent="MoreKeysKeyboardView.LXX_Dark"
    >
        <item name="android:background">@android:color/transparent</item>
        <item name="keyBackground">@drawable/btn_keyboard_key_popup_action_lxx_dark</item>
        <item name="divider">@null</item>
        <item name="keyLabelFlags">keepBackgroundAspectRatio</item>
    </style>
    <style
        name="SuggestionStripView.LXX_Dark"
        parent="KeyboardView.LXX_Dark"
    >
        <item name="suggestionsCountInStrip">@integer/config_suggestions_count_in_strip</item>
        <item name="centerSuggestionPercentile">@fraction/config_center_suggestion_percentile</item>
        <item name="maxMoreSuggestionsRow">@integer/config_max_more_suggestions_row</item>
        <item name="minMoreSuggestionsWidth">@fraction/config_min_more_suggestions_width</item>
        <item name="android:background">@drawable/keyboard_suggest_strip_lxx_dark</item>
        <item name="android:src">@drawable/suggestions_strip_divider_lxx_dark</item>
        <item name="suggestionStripOptions">autoCorrectBold|validTypedWordBold</item>
        <item name="colorValidTypedWord">@color/typed_word_color_lxx_dark</item>
        <item name="colorTypedWord">@color/typed_word_color_lxx_dark</item>
        <item name="colorAutoCorrect">@color/auto_correct_color_lxx_dark</item>
        <item name="colorSuggested">@color/suggested_word_color_lxx_dark</item>
        <item name="alphaObsoleted">70%</item>
    </style>
    <style
        name="SuggestionWord.LXX_Dark"
        parent="SuggestionWord"
    >
        <item name="android:background">@drawable/btn_suggestion_lxx_dark</item>
        <item name="android:textColor">@color/highlight_color_lxx_dark</item>
    </style>
</resources>
