import java.util.Scanner;

/**
 * Simple test class to demonstrate Tamil transliteration
 * Simulates the real-time typing experience from the Indic Keyboard
 */
public class TamilTransliteratorTest {
    
    private TamilTransliterator transliterator;
    
    public TamilTransliteratorTest() {
        this.transliterator = new TamilTransliterator();
    }
    
    /**
     * Simulates the real-time transliteration as you type
     * Similar to how the Indic Keyboard works
     */
    public void simulateRealTimeTyping(String input) {
        System.out.println("\nSimulating real-time typing for: " + input);
        System.out.println("================================");
        
        StringBuilder currentWord = new StringBuilder();
        
        for (int i = 0; i < input.length(); i++) {
            currentWord.append(input.charAt(i));
            String currentInput = currentWord.toString();
            String tamilOutput = transliterator.transliterate(currentInput, "", false);
            
            System.out.printf("Typed: %-15s -> Tamil: %s%n", currentInput, tamilOutput);
        }
    }
    
    /**
     * Test the firstDivergence logic similar to the original code
     */
    public static int firstDivergence(String str1, String str2) {
        int length = Math.min(str1.length(), str2.length());
        for(int i = 0; i < length; i++) {
            if(str1.charAt(i) != str2.charAt(i)) {
                return i;
            }
        }
        return length - 1; // Default
    }
    
    /**
     * Simulates the applyTransliteration method from WordComposer
     */
    public void applyTransliterationSimulation(String typedWord, char newChar) {
        String currentTypedWord = typedWord + newChar;
        
        // Get the transliteration for current word
        String replacement = transliterator.transliterate(currentTypedWord, "", false);
        
        // Find divergence point (similar to original logic)
        int divIndex = firstDivergence(currentTypedWord, replacement);
        
        System.out.printf("Input: %s, Output: %s, Divergence at: %d%n", 
                         currentTypedWord, replacement, divIndex);
    }
    
    /**
     * Batch test common Tamil words
     */
    public void runBatchTest() {
        System.out.println("Batch Test - Common Tamil Words");
        System.out.println("===============================");
        
        String[] commonWords = {
            // Greetings
            "vanakkam",     // Hello
            "nandri",       // Thank you
            "mannikkavum",  // Sorry
            
            // Family
            "amma",         // Mother
            "appa",         // Father
            "anna",         // Elder brother
            "akka",         // Elder sister
            "thambi",       // Younger brother
            "thangai",      // Younger sister
            
            // Common phrases
            "naan",         // I
            "neenga",       // You (respectful)
            "enna",         // What
            "eppo",         // When
            "enga",         // Where
            "eppadi",       // How
            "yen",          // Why
            
            // Responses
            "sari",         // Okay
            "illa",         // No
            "aam",          // Yes
            "theriyala",    // Don't know
            
            // Actions
            "varen",        // I will come
            "poren",        // I will go
            "irukku",       // Is there/exists
            "sapadu",       // Food
            "thanni",       // Water
            
            // Language
            "tamil",        // Tamil
            "mozhi",        // Language
            
            // Simple syllables
            "ka", "ki", "ku", "ke", "ko",
            "ma", "mi", "mu", "me", "mo",
            "na", "ni", "nu", "ne", "no",
            "ta", "ti", "tu", "te", "to",
            "pa", "pi", "pu", "pe", "po",
            "ra", "ri", "ru", "re", "ro",
            "la", "li", "lu", "le", "lo",
            "va", "vi", "vu", "ve", "vo"
        };
        
        for (String word : commonWords) {
            String result = transliterator.transliterate(word, "", false);
            System.out.printf("%-15s -> %s%n", word, result);
        }
    }
    
    /**
     * Interactive testing mode
     */
    public void interactiveMode() {
        Scanner scanner = new Scanner(System.in);
        System.out.println("\n=== Interactive Tamil Transliteration ===");
        System.out.println("Commands:");
        System.out.println("  Type any English word to transliterate");
        System.out.println("  'realtime <word>' - See character-by-character conversion");
        System.out.println("  'batch' - Run batch test");
        System.out.println("  'help' - Show this help");
        System.out.println("  'exit' - Quit");
        System.out.println();
        
        while (true) {
            System.out.print("Tamil> ");
            String input = scanner.nextLine().trim();
            
            if ("exit".equalsIgnoreCase(input)) {
                break;
            } else if ("help".equalsIgnoreCase(input)) {
                System.out.println("Available commands: realtime <word>, batch, help, exit");
                System.out.println("Or just type any English word to transliterate it.");
            } else if ("batch".equalsIgnoreCase(input)) {
                runBatchTest();
            } else if (input.toLowerCase().startsWith("realtime ")) {
                String word = input.substring(9).trim();
                if (!word.isEmpty()) {
                    simulateRealTimeTyping(word);
                }
            } else if (!input.isEmpty()) {
                String result = transliterator.transliterate(input, "", false);
                System.out.println("Tamil: " + result);
                
                // Also show real-time simulation for longer words
                if (input.length() > 3) {
                    simulateRealTimeTyping(input);
                }
            }
        }
        
        scanner.close();
    }
    
    public static void main(String[] args) {
        TamilTransliteratorTest test = new TamilTransliteratorTest();
        
        System.out.println("Tamil Transliteration Engine Test");
        System.out.println("Based on Indic Keyboard Logic");
        System.out.println("================================\n");
        
        // Run a quick demo
        System.out.println("Quick Demo:");
        test.simulateRealTimeTyping("vanakkam");
        
        // Start interactive mode
        test.interactiveMode();
        
        System.out.println("Thank you for testing Tamil transliteration!");
    }
}
