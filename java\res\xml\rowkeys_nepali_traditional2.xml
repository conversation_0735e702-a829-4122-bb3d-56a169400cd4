<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted">
            <!-- U+0906: "आ" DEVANAGARI LETTER AA -->
            <Key latin:keySpec="&#x0906;" />
            <!-- U+0919/U+094D: "ङ्" DEVANAGARI LETTER NGA/DEVANAGARI SIGN VIRAMA -->
            <Key
                latin:keySpec="&#x0919;&#x094D;"
                latin:keyLabelFlags="followKeyLetterRatio" />
            <!-- U+0921/U+094D/U+0921: "ड्ड" DEVANAGARI LETTER DDA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER DDA -->
            <Key
                latin:keySpec="&#x0921;&#x094D;&#x0921;"
                latin:keyLabelFlags="followKeyLetterRatio" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_candrabindu" />
            <Key latin:keyStyle="baseKeyDevanagariSignCandrabindu" />
            <!-- U+0926/U+094D/U+0926: "द्द" DEVANAGARI LETTER DA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER DA -->
            <Key
                latin:keySpec="&#x0926;&#x094D;&#x0926;"
                latin:keyLabelFlags="followKeyLetterRatio" />
            <!-- U+091D: "झ" DEVANAGARI LETTER JHA -->
            <Key latin:keySpec="&#x091D;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_o" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignO" />
            <!-- U+092B: "फ" DEVANAGARI LETTER PHA -->
            <Key latin:keySpec="&#x092B;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_ii" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignIi" />
            <!-- U+091F/U+094D/U+0920: "ट्ठ" DEVANAGARI LETTER TTA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER TTHA -->
            <Key
                latin:keySpec="&#x091F;&#x094D;&#x0920;"
                latin:keyLabelFlags="followKeyLetterRatio" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_uu" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignUu" />
        </case>
        <default>
            <!-- U+092C: "ब" DEVANAGARI LETTER BA -->
            <Key latin:keySpec="&#x092C;" />
            <!-- U+0915: "क" DEVANAGARI LETTER KA -->
            <Key latin:keySpec="&#x0915;" />
            <!-- U+092E: "म" DEVANAGARI LETTER MA -->
            <Key latin:keySpec="&#x092E;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_aa" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignAa" />
            <!-- U+0928: "न" DEVANAGARI LETTER NA -->
            <Key latin:keySpec="&#x0928;" />
            <!-- U+091C: "ज" DEVANAGARI LETTER JA -->
            <Key latin:keySpec="&#x091C;" />
            <!-- U+0935: "व" DEVANAGARI LETTER VA -->
            <Key latin:keySpec="&#x0935;" />
            <!-- U+092A: "प" DEVANAGARI LETTER PA -->
            <Key latin:keySpec="&#x092A;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_i" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignI" />
            <!-- U+0938: "स" DEVANAGARI LETTER SA -->
            <Key latin:keySpec="&#x0938;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_u" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignU" />
        </default>
    </switch>
</merge>
