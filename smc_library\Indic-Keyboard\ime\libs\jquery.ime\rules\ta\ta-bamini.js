( function ( $ ) {
	'use strict';

	var taBamini = {
		id: 'ta-bamini',
		name: 'பாமினி',
		description: 'Tamil Bamini input method',
		date: '2012-10-03',
		URL: 'http://github.com/wikimedia/jquery.ime',
		author: 'Junaid P V',
		license: 'GPLv3',
		version: '1.0',
		contextLength: 0,
		maxKeyLength: 2,
		patterns: [
			[ '`', 'ஹ' ],
			[ 'q', 'ங' ],
			[ 'w', 'ற' ],
			[ 'e', 'ந' ],
			[ 'r', 'ச' ],
			[ 't', 'வ' ],
			[ 'y', 'ல' ],
			[ 'u', 'ர' ],
			[ 'i', 'ை' ],
			[ 'o', 'ழ' ],
			[ 'p', 'ி' ],
			[ '\\[', 'ஜ' ],
			[ '\\]', 'ஸ' ],
			[ '\\\\', 'ஷ' ],
			[ 'a', 'ய' ],
			[ 's', 'ள' ],
			[ 'd', 'ன' ],
			[ 'f', 'க' ],
			[ 'g', 'ப' ],
			[ 'h', 'ா' ],
			[ 'j', 'த' ],
			[ 'k', 'ம' ],
			[ 'l', 'ட' ],
			[ ';', '்' ],
			[ 'z', 'ண' ],
			[ 'x', 'ஒ' ],
			[ 'c', 'உ' ],
			[ 'v', 'எ' ],
			[ 'b', 'டி' ],
			[ 'n', 'ெ' ],
			[ 'N', 'ே' ],
			[ 'm', 'அ' ],
			[ ',', 'இ' ],
			[ '/', 'ஃ' ],
			[ '@', ';' ],
			[ '#', 'சூ' ],
			[ '\\$', 'கூ' ],
			[ '%', 'மூ' ],
			[ '\\^', 'டூ' ],
			[ '&', 'ரூ' ],
			[ '\\*', 'ழூ' ],
			[ '=', 'ஸ்ரீ' ],
			[ 'Q', 'ஞ' ],
			[ 'W', 'று' ],
			[ 'E', 'நு' ],
			[ 'R', 'சு' ],
			[ 'T', 'வு' ],
			[ 'Y', 'லு' ],
			[ 'U', 'ரு' ],
			[ 'I', 'ஐ' ],
			[ 'O', 'ழு' ],
			[ 'P', 'ீ' ],
			[ '\\|', '\'' ],
			[ 'A', 'யு' ],
			[ 'S', 'ளு' ],
			[ 'D', 'னு' ],
			[ 'F', 'கு' ],
			[ 'G', 'பு' ],
			[ 'H', 'ர்' ],
			[ 'J', 'து' ],
			[ 'K', 'மு' ],
			[ 'L', 'டு' ],
			[ 'Z', 'ணு' ],
			[ 'X', 'ஓ' ],
			[ 'C', 'ஊ' ],
			[ 'V', 'ஏ' ],
			[ 'B', 'டீ' ],
			[ 'M', 'ஆ' ],
			[ '\\<', 'ஈ' ],
			[ '\\>', ',' ],
			[ '([ஜஷஸஹ])\\{', '$1ு' ],
			[ '([ஜஷஸஹ])\\_', '$1ூ' ] ]
	};
	$.ime.register( taBamini );

}( jQuery ) );
