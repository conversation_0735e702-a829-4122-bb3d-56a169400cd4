<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- U+00E0: "à" LATIN SMALL LETTER A WITH GRAVE
         U+00E1: "á" LATIN SMALL LETTER A WITH ACUTE
         U+00E2: "â" LATIN SMALL LETTER A WITH CIRCUMFLEX
         U+00E3: "ã" LATIN SMALL LETTER A WITH TILDE
         U+00E4: "ä" LATIN SMALL LETTER A WITH DIAERESIS
         U+00E5: "å" LATIN SMALL LETTER A WITH RING ABOVE
         U+00E6: "æ" LATIN SMALL LETTER AE
         U+0101: "ā" LATIN SMALL LETTER A WITH MACRON
         U+0103: "ă" LATIN SMALL LETTER A WITH BREVE
         U+0105: "ą" LATIN SMALL LETTER A WITH OGONEK
         U+00AA: "ª" FEMININE ORDINAL INDICATOR -->
    <string name="morekeys_a">&#x00E0;,&#x00E1;,&#x00E2;,&#x00E3;,&#x00E4;,&#x00E5;,&#x00E6;,&#x0101;,&#x0103;,&#x0105;,&#x00AA;</string>
    <!-- U+00E8: "è" LATIN SMALL LETTER E WITH GRAVE
         U+00E9: "é" LATIN SMALL LETTER E WITH ACUTE
         U+00EA: "ê" LATIN SMALL LETTER E WITH CIRCUMFLEX
         U+00EB: "ë" LATIN SMALL LETTER E WITH DIAERESIS
         U+0113: "ē" LATIN SMALL LETTER E WITH MACRON
         U+0115: "ĕ" LATIN SMALL LETTER E WITH BREVE
         U+0117: "ė" LATIN SMALL LETTER E WITH DOT ABOVE
         U+0119: "ę" LATIN SMALL LETTER E WITH OGONEK
         U+011B: "ě" LATIN SMALL LETTER E WITH CARON -->
    <string name="morekeys_e">&#x00E8;,&#x00E9;,&#x00EA;,&#x00EB;,&#x0113;,&#x0115;,&#x0117;,&#x0119;,&#x011B;</string>
    <!-- U+00EC: "ì" LATIN SMALL LETTER I WITH GRAVE
         U+00ED: "í" LATIN SMALL LETTER I WITH ACUTE
         U+00EE: "î" LATIN SMALL LETTER I WITH CIRCUMFLEX
         U+00EF: "ï" LATIN SMALL LETTER I WITH DIAERESIS
         U+0129: "ĩ" LATIN SMALL LETTER I WITH TILDE
         U+012B: "ī" LATIN SMALL LETTER I WITH MACRON
         U+012D: "ĭ" LATIN SMALL LETTER I WITH BREVE
         U+012F: "į" LATIN SMALL LETTER I WITH OGONEK
         U+0131: "ı" LATIN SMALL LETTER DOTLESS I
         U+0133: "ĳ" LATIN SMALL LIGATURE IJ -->
    <string name="morekeys_i">&#x00EC;,&#x00ED;,&#x00EE;,&#x00EF;,&#x0129;,&#x012B;,&#x012D;,&#x012F;,&#x0131;,&#x0133;</string>
    <!-- U+00F2: "ò" LATIN SMALL LETTER O WITH GRAVE
         U+00F3: "ó" LATIN SMALL LETTER O WITH ACUTE
         U+00F4: "ô" LATIN SMALL LETTER O WITH CIRCUMFLEX
         U+00F5: "õ" LATIN SMALL LETTER O WITH TILDE
         U+00F6: "ö" LATIN SMALL LETTER O WITH DIAERESIS
         U+00F8: "ø" LATIN SMALL LETTER O WITH STROKE
         U+014D: "ō" LATIN SMALL LETTER O WITH MACRON
         U+014F: "ŏ" LATIN SMALL LETTER O WITH BREVE
         U+0151: "ő" LATIN SMALL LETTER O WITH DOUBLE ACUTE
         U+0153: "œ" LATIN SMALL LIGATURE OE
         U+00BA: "º" MASCULINE ORDINAL INDICATOR -->
    <string name="morekeys_o">&#x00F2;,&#x00F3;,&#x00F4;,&#x00F5;,&#x00F6;,&#x00F8;,&#x014D;,&#x014F;,&#x0151;,&#x0153;,&#x00BA;</string>
    <!-- U+00F9: "ù" LATIN SMALL LETTER U WITH GRAVE
         U+00FA: "ú" LATIN SMALL LETTER U WITH ACUTE
         U+00FB: "û" LATIN SMALL LETTER U WITH CIRCUMFLEX
         U+00FC: "ü" LATIN SMALL LETTER U WITH DIAERESIS
         U+0169: "ũ" LATIN SMALL LETTER U WITH TILDE
         U+016B: "ū" LATIN SMALL LETTER U WITH MACRON
         U+016D: "ŭ" LATIN SMALL LETTER U WITH BREVE
         U+016F: "ů" LATIN SMALL LETTER U WITH RING ABOVE
         U+0171: "ű" LATIN SMALL LETTER U WITH DOUBLE ACUTE
         U+0173: "ų" LATIN SMALL LETTER U WITH OGONEK -->
    <string name="morekeys_u">&#x00F9;,&#x00FA;,&#x00FB;,&#x00FC;,&#x0169;,&#x016B;,&#x016D;,&#x016F;,&#x0171;,&#x0173;</string>
    <!-- U+00DF: "ß" LATIN SMALL LETTER SHARP S
         U+015B: "ś" LATIN SMALL LETTER S WITH ACUTE
         U+015D: "ŝ" LATIN SMALL LETTER S WITH CIRCUMFLEX
         U+015F: "ş" LATIN SMALL LETTER S WITH CEDILLA
         U+0161: "š" LATIN SMALL LETTER S WITH CARON
         U+017F: "ſ" LATIN SMALL LETTER LONG S -->
    <string name="morekeys_s">&#x00DF;,&#x015B;,&#x015D;,&#x015F;,&#x0161;,&#x017F;</string>
    <!-- U+00F1: "ñ" LATIN SMALL LETTER N WITH TILDE
         U+0144: "ń" LATIN SMALL LETTER N WITH ACUTE
         U+0146: "ņ" LATIN SMALL LETTER N WITH CEDILLA
         U+0148: "ň" LATIN SMALL LETTER N WITH CARON
         U+0149: "ŉ" LATIN SMALL LETTER N PRECEDED BY APOSTROPHE
         U+014B: "ŋ" LATIN SMALL LETTER ENG -->
    <string name="morekeys_n">&#x00F1;,&#x0144;,&#x0146;,&#x0148;,&#x0149;,&#x014B;</string>
    <!-- U+00E7: "ç" LATIN SMALL LETTER C WITH CEDILLA
         U+0107: "ć" LATIN SMALL LETTER C WITH ACUTE
         U+0109: "ĉ" LATIN SMALL LETTER C WITH CIRCUMFLEX
         U+010B: "ċ" LATIN SMALL LETTER C WITH DOT ABOVE
         U+010D: "č" LATIN SMALL LETTER C WITH CARON -->
    <string name="morekeys_c">&#x00E7;,&#x0107;,&#x0109;,&#x010B;,&#x010D;</string>
    <!-- U+00FD: "ý" LATIN SMALL LETTER Y WITH ACUTE
         U+0177: "ŷ" LATIN SMALL LETTER Y WITH CIRCUMFLEX
         U+00FF: "ÿ" LATIN SMALL LETTER Y WITH DIAERESIS
         U+0133: "ĳ" LATIN SMALL LIGATURE IJ -->
    <string name="morekeys_y">&#x00FD;,&#x0177;,&#x00FF;,&#x0133;</string>
    <!-- U+010F: "ď" LATIN SMALL LETTER D WITH CARON
         U+0111: "đ" LATIN SMALL LETTER D WITH STROKE
         U+00F0: "ð" LATIN SMALL LETTER ETH -->
    <string name="morekeys_d">&#x010F;,&#x0111;,&#x00F0;</string>
    <!-- U+0155: "ŕ" LATIN SMALL LETTER R WITH ACUTE
         U+0157: "ŗ" LATIN SMALL LETTER R WITH CEDILLA
         U+0159: "ř" LATIN SMALL LETTER R WITH CARON -->
    <string name="morekeys_r">&#x0155;,&#x0157;,&#x0159;</string>
    <!-- U+00FE: "þ" LATIN SMALL LETTER THORN
         U+0163: "ţ" LATIN SMALL LETTER T WITH CEDILLA
         U+0165: "ť" LATIN SMALL LETTER T WITH CARON
         U+0167: "ŧ" LATIN SMALL LETTER T WITH STROKE -->
    <string name="morekeys_t">&#x00FE;,&#x0163;,&#x0165;,&#x0167;</string>
    <!-- U+017A: "ź" LATIN SMALL LETTER Z WITH ACUTE
         U+017C: "ż" LATIN SMALL LETTER Z WITH DOT ABOVE
         U+017E: "ž" LATIN SMALL LETTER Z WITH CARON -->
    <string name="morekeys_z">&#x017A;,&#x017C;,&#x017E;</string>
    <!-- U+0137: "ķ" LATIN SMALL LETTER K WITH CEDILLA
         U+0138: "ĸ" LATIN SMALL LETTER KRA -->
    <string name="morekeys_k">&#x0137;,&#x0138;</string>
    <!-- U+013A: "ĺ" LATIN SMALL LETTER L WITH ACUTE
         U+013C: "ļ" LATIN SMALL LETTER L WITH CEDILLA
         U+013E: "ľ" LATIN SMALL LETTER L WITH CARON
         U+0140: "ŀ" LATIN SMALL LETTER L WITH MIDDLE DOT
         U+0142: "ł" LATIN SMALL LETTER L WITH STROKE -->
    <string name="morekeys_l">&#x013A;,&#x013C;,&#x013E;,&#x0140;,&#x0142;</string>
    <!-- U+011D: "ĝ" LATIN SMALL LETTER G WITH CIRCUMFLEX
         U+011F: "ğ" LATIN SMALL LETTER G WITH BREVE
         U+0121: "ġ" LATIN SMALL LETTER G WITH DOT ABOVE
         U+0123: "ģ" LATIN SMALL LETTER G WITH CEDILLA -->
    <string name="morekeys_g">&#x011D;,&#x011F;,&#x0121;,&#x0123;</string>
    <!-- U+0125: "ĥ" LATIN SMALL LETTER H WITH CIRCUMFLEX -->
    <string name="morekeys_h">&#x0125;</string>
    <!-- U+0135: "ĵ" LATIN SMALL LETTER J WITH CIRCUMFLEX -->
    <string name="morekeys_j">&#x0135;</string>
    <!-- U+0175: "ŵ" LATIN SMALL LETTER W WITH CIRCUMFLEX -->
    <string name="morekeys_w">&#x0175;</string>
</resources>
