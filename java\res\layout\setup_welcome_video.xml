<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="@dimen/setup_welcome_video_top_padding"
        android:paddingBottom="@dimen/setup_welcome_video_bottom_padding">
        <LinearLayout
            android:layout_weight="@integer/setup_welcome_video_weight_in_screen"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="1dp"
            android:background="@color/setup_welcome_video_margin_color" >
            <ImageView
                android:id="@+id/setup_welcome_image"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:visibility="gone"
                android:contentDescription="@string/setup_welcome_additional_description"/>
        </LinearLayout>
        <View
            android:layout_weight="@integer/setup_welcome_video_end_padding_weight_in_screen"
            android:layout_width="0dp"
            android:layout_height="0dp" />
    </LinearLayout>
    <in.androidtweak.inputmethod.indic.setup.SetupStartIndicatorView
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</merge>
