<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <!-- U+0636: "ض" ARABIC LETTER DAD
         U+0661: "١" ARABIC-INDIC DIGIT ONE -->
    <Key
        latin:keySpec="&#x0636;"
        latin:keyHintLabel="1"
        latin:additionalMoreKeys="1,&#x0661;" />
    <!-- U+0635: "ص" ARABIC LETTER SAD
         U+0662: "٢" ARABIC-INDIC DIGIT TWO -->
    <Key
        latin:keySpec="&#x0635;"
        latin:keyHintLabel="2"
        latin:additionalMoreKeys="2,&#x0662;" />
    <!-- U+062B: "ث" ARABIC LETTER THEH
         U+0663: "٣" ARABIC-INDIC DIGIT THREE -->
    <Key
        latin:keySpec="&#x062B;"
        latin:keyHintLabel="3"
        latin:additionalMoreKeys="3,&#x0663;" />
    <!-- U+0642: "ق" ARABIC LETTER QAF
         U+0664: "٤" ARABIC-INDIC DIGIT FOUR
         U+06A8: "ڨ" ARABIC LETTER QAF WITH THREE DOTS ABOVE -->
    <!-- TODO: DroidSansArabic lacks the glyph of U+06A8 ARABIC LETTER QAF WITH THREE DOTS ABOVE -->
    <Key
        latin:keySpec="&#x0642;"
        latin:keyHintLabel="4"
        latin:additionalMoreKeys="4,&#x0664;"
        latin:moreKeys="&#x06A8;" />
    <!-- U+0641: "ف" ARABIC LETTER FEH
         U+0665: "٥" ARABIC-INDIC DIGIT FIVE
         U+06A4: "ڤ" ARABIC LETTER VEH
         U+06A2: "ڢ" ARABIC LETTER FEH WITH DOT MOVED BELOW
         U+06A5: "ڥ" ARABIC LETTER FEH WITH THREE DOTS BELOW -->
    <!-- TODO: DroidSansArabic lacks the glyph of U+06A2 ARABIC LETTER FEH WITH DOT MOVED BELOW -->
    <!-- TODO: DroidSansArabic lacks the glyph of U+06A5 ARABIC LETTER FEH WITH THREE DOTS BELOW -->
    <Key
        latin:keySpec="&#x0641;"
        latin:keyHintLabel="5"
        latin:additionalMoreKeys="5,&#x0665;"
        latin:moreKeys="&#x06A4;,&#x06A2;,&#x06A5;" />
    <!-- U+063A: "غ" ARABIC LETTER GHAIN
         U+0666: "٦" ARABIC-INDIC DIGIT SIX -->
    <Key
        latin:keySpec="&#x063A;"
        latin:keyHintLabel="6"
        latin:additionalMoreKeys="6,&#x0666;" />
    <!-- U+0639: "ع" ARABIC LETTER AIN
         U+0667: "٧" ARABIC-INDIC DIGIT SEVEN -->
    <Key
        latin:keySpec="&#x0639;"
        latin:keyHintLabel="7"
        latin:additionalMoreKeys="7,&#x0667;" />
    <!-- U+0647: "ه" ARABIC LETTER HEH
         U+0668: "٨" ARABIC-INDIC DIGIT EIGHT
         U+FEEB: "ﻫ" ARABIC LETTER HEH INITIAL FORM
         U+0647 U+200D: ARABIC LETTER HEH + ZERO WIDTH JOINER -->
    <Key
        latin:keySpec="&#x0647;"
        latin:keyHintLabel="8"
        latin:additionalMoreKeys="8,&#x0668;"
        latin:moreKeys="&#xFEEB;|&#x0647;&#x200D;" />
    <!-- U+062E: "خ" ARABIC LETTER KHAH
         U+0669: "٩" ARABIC-INDIC DIGIT NINE -->
    <Key
        latin:keySpec="&#x062E;"
        latin:keyHintLabel="9"
        latin:additionalMoreKeys="9,&#x0669;" />
    <!-- U+062D: "ح" ARABIC LETTER HAH
         U+0660: "٠" ARABIC-INDIC DIGIT ZERO -->
    <Key
        latin:keySpec="&#x062D;"
        latin:keyHintLabel="0"
        latin:additionalMoreKeys="0,&#x0660;" />
    <!-- U+062C: "ج" ARABIC LETTER JEEM
         U+0686: "چ" ARABIC LETTER TCHEH -->
    <Key
        latin:keySpec="&#x062C;"
        latin:moreKeys="&#x0686;" />
</merge>
