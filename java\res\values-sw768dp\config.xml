<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2010, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- Configuration values for Large Tablet Portrait. -->
<resources>
    <!-- Preferable keyboard height in absolute scale: 48.0mm -->
    <!-- This config_default_keyboard_height value should match with keyboard-heights.xml -->
    <dimen name="config_default_keyboard_height">302.4dp</dimen>
    <fraction name="config_max_keyboard_height">46%p</fraction>
    <fraction name="config_min_keyboard_height">-35.0%p</fraction>

    <fraction name="config_keyboard_top_padding_holo">2.335%p</fraction>
    <fraction name="config_keyboard_bottom_padding_holo">0.0%p</fraction>
    <fraction name="config_key_vertical_gap_holo">3.312%p</fraction>
    <fraction name="config_key_horizontal_gap_holo">1.066%p</fraction>
    <!-- config_more_keys_keyboard_key_height x -0.5 -->
    <dimen name="config_more_keys_keyboard_vertical_correction_holo">-31.5dp</dimen>

    <dimen name="config_more_keys_keyboard_key_height">63.0dp</dimen>
    <dimen name="config_more_keys_keyboard_key_horizontal_padding">12dp</dimen>
    <!-- Amount of allowance for selecting keys in a mini popup keyboard by sliding finger. -->
    <!-- config_more_keys_keyboard_key_height x 1.2 -->
    <dimen name="config_more_keys_keyboard_slide_allowance">98.3dp</dimen>

    <fraction name="config_key_preview_text_ratio">50%</fraction>
    <fraction name="config_key_letter_ratio_holo">40%</fraction>
    <fraction name="config_key_letter_ratio_lxx">50%</fraction>
    <fraction name="config_key_large_letter_ratio_holo">42%</fraction>
    <fraction name="config_key_large_letter_ratio_lxx">60%</fraction>
    <fraction name="config_key_label_ratio_holo">28%</fraction>
    <fraction name="config_key_label_ratio_lxx">32%</fraction>
    <fraction name="config_key_hint_letter_ratio_holo">23%</fraction>
    <fraction name="config_key_hint_letter_ratio_lxx">23%</fraction>
    <fraction name="config_key_hint_label_ratio_holo">28%</fraction>
    <fraction name="config_key_hint_label_ratio_lxx">20%</fraction>
    <fraction name="config_key_shifted_letter_hint_ratio_holo">26%</fraction>
    <fraction name="config_key_shifted_letter_hint_ratio_lxx">26%</fraction>
    <fraction name="config_language_on_spacebar_text_ratio">29.03%</fraction>
    <dimen name="config_key_hint_letter_padding">3dp</dimen>
    <dimen name="config_key_shifted_letter_hint_padding">3dp</dimen>

    <!-- For 5-row keyboard -->
    <fraction name="config_key_vertical_gap_5row">2.95%p</fraction>
    <fraction name="config_key_letter_ratio_5row">51%</fraction>
    <fraction name="config_key_shifted_letter_hint_ratio_5row">33%</fraction>

    <dimen name="config_suggestions_strip_height">44dp</dimen>
    <dimen name="config_suggestions_strip_horizontal_margin">100dp</dimen>
    <dimen name="config_suggestions_strip_edge_key_width">54dp</dimen>
    <dimen name="config_more_suggestions_row_height">44dp</dimen>
    <integer name="config_max_more_suggestions_row">6</integer>
    <fraction name="config_min_more_suggestions_width">90%</fraction>
    <dimen name="config_suggestion_min_width">46dp</dimen>
    <dimen name="config_suggestion_text_horizontal_padding">10dp</dimen>
    <dimen name="config_suggestion_text_size">22dp</dimen>
    <dimen name="config_more_suggestions_hint_text_size">33dp</dimen>

    <!-- Gesture floating preview text parameters -->
    <dimen name="config_gesture_floating_preview_text_size">26dp</dimen>
    <dimen name="config_gesture_floating_preview_text_offset">86dp</dimen>
    <dimen name="config_gesture_floating_preview_horizontal_padding">26dp</dimen>
    <dimen name="config_gesture_floating_preview_vertical_padding">17dp</dimen>
    <dimen name="config_gesture_floating_preview_round_radius">3dp</dimen>

    <!-- Emoji keyboard -->
    <fraction name="config_emoji_keyboard_key_width">10%p</fraction>
    <fraction name="config_emoji_keyboard_row_height">33%p</fraction>
    <fraction name="config_emoji_keyboard_key_letter_size">68%p</fraction>
    <integer name="config_emoji_keyboard_max_page_key_count">30</integer>
</resources>
