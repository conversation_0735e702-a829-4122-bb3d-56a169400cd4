<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <Key
        latin:keyStyle="moreCurrency1KeyStyle" />
    <Key
        latin:keyStyle="moreCurrency2KeyStyle" />
    <Key
        latin:keyStyle="moreCurrency3KeyStyle" />
    <Key
        latin:keyStyle="moreCurrency4KeyStyle" />
    <!-- U+2191: "↑" UPWARDS ARROW
         U+2193: "↓" DOWNWARDS ARROW
         U+2190: "←" LEFTWARDS ARROW
         U+2192: "→" RIGHTWARDS ARROW -->
    <Key
        latin:keySpec="^"
        latin:moreKeys="&#x2191;,&#x2193;,&#x2190;,&#x2192;" />
    <!-- U+00B0: "°" DEGREE SIGN
         U+2032: "′" PRIME
         U+2033: "″" DOUBLE PRIME -->
    <Key
        latin:keySpec="&#x00B0;"
        latin:moreKeys="&#x2032;,&#x2033;" />
    <!-- U+2260: "≠" NOT EQUAL TO
         U+2248: "≈" ALMOST EQUAL TO
         U+221E: "∞" INFINITY -->
    <Key
        latin:keySpec="="
        latin:moreKeys="&#x2260;,&#x2248;,&#x221E;" />
    <include
        latin:keyboardLayout="@xml/keys_curly_brackets" />
</merge>
