@echo off
REM Complete SMC Tamil Transliteration Engine - Windows Build Script
REM Professional-grade C++ implementation with full integration

echo 🚀 SMC Tamil Transliteration Engine - Windows Build
echo ================================================

REM Check for C++ compiler
echo 🔍 Checking for C++ compiler...

where cl >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Found Microsoft Visual C++ compiler
    set COMPILER=cl
    set COMPILE_CMD=cl /EHsc /std:c++17 /O2
    goto :compile
)

where g++ >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Found GCC compiler
    set COMPILER=g++
    set COMPILE_CMD=g++ -std=c++17 -Wall -Wextra -O2
    goto :compile
)

where clang++ >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Found Clang compiler
    set COMPILER=clang++
    set COMPILE_CMD=clang++ -std=c++17 -Wall -Wextra -O2
    goto :compile
)

echo ❌ No C++ compiler found!
echo.
echo 📦 Please install one of the following:
echo.
echo 1. Microsoft Visual Studio Community (Free)
echo    Download: https://visualstudio.microsoft.com/vs/community/
echo    - Install "Desktop development with C++" workload
echo.
echo 2. MinGW-w64 (GCC for Windows)
echo    Download: https://www.mingw-w64.org/downloads/
echo    - Or install via MSYS2: https://www.msys2.org/
echo.
echo 3. LLVM/Clang
echo    Download: https://releases.llvm.org/download.html
echo.
echo 4. Quick option - Install via Chocolatey:
echo    choco install mingw
echo    choco install llvm
echo.
pause
exit /b 1

:compile
echo.
echo 🔨 Compiling SMC Transliteration Engine...
echo Using: %COMPILER%
echo.

REM Check if source files exist
if not exist "smc_transliterator.h" (
    echo ❌ Source files not found in current directory
    echo Please run this script from the directory containing the C++ source files
    pause
    exit /b 1
)

REM Compile the program
echo 📝 Compiling main.cpp and smc_transliterator.cpp...
%COMPILE_CMD% main.cpp smc_transliterator.cpp -o smc_transliterator.exe

if %errorlevel% neq 0 (
    echo ❌ Compilation failed!
    pause
    exit /b 1
)

echo ✅ Compilation successful!
echo.

REM Check for SMC files
echo 🔍 Checking for SMC files...
set SMC_XML=smc_library\Indic-Keyboard\ime\src\main\res\raw\ta_transliteration.xml
set SMC_DICT=smc_library\Indic-Keyboard\dictionaries-indic\ta_wordlist.combined

if exist "%SMC_XML%" (
    echo ✅ Found XML patterns: %SMC_XML%
    set HAS_XML=1
) else (
    echo ⚠️ XML patterns not found: %SMC_XML%
    set HAS_XML=0
)

if exist "%SMC_DICT%" (
    echo ✅ Found dictionary: %SMC_DICT%
    set HAS_DICT=1
) else (
    echo ⚠️ Dictionary not found: %SMC_DICT%
    set HAS_DICT=0
)

echo.
echo 🎯 Build complete! Choose an option:
echo.
echo 1. Run quick test
echo 2. Run with SMC files (if available)
echo 3. Run interactive mode
echo 4. Exit
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto :test
if "%choice%"=="2" goto :run_smc
if "%choice%"=="3" goto :run_interactive
if "%choice%"=="4" goto :end

:test
echo.
echo 🧪 Running quick test...
smc_transliterator.exe --batch
goto :end

:run_smc
echo.
if %HAS_XML%==1 if %HAS_DICT%==1 (
    echo 🚀 Running with SMC files...
    smc_transliterator.exe -x "%SMC_XML%" -d "%SMC_DICT%"
) else (
    echo ⚠️ SMC files not available, running in basic mode...
    smc_transliterator.exe
)
goto :end

:run_interactive
echo.
echo 🚀 Starting interactive mode...
smc_transliterator.exe
goto :end

:end
echo.
echo 📖 Usage examples:
echo   smc_transliterator.exe --help
echo   smc_transliterator.exe --batch
echo   smc_transliterator.exe --test
if %HAS_XML%==1 if %HAS_DICT%==1 (
    echo   smc_transliterator.exe -x "%SMC_XML%" -d "%SMC_DICT%"
)
echo.
echo Thank you for using SMC Tamil Transliteration Engine!
pause
