<?xml version="1.0"?>
<inputmethod id="gu-transliteration" name="લિપ્યાંતરણ" description="Gujarati transliteration" author="Jaldeep R Vasavada ([[User:JaldeepVasavada]]) / <PERSON> ([[User:Amire80]])" version="1.0" contextLength="2" maxKeyLength="4">
  <pattern input="ક્h" context="c" replacement="ચ"/>
  <pattern input="\\([A-Za-z\&gt;_~\.0-9])" context="\\" replacement="$1"/>
  <pattern input="([ક-હ]઼?)્a" replacement="$1"/>
  <pattern input="([ક-હ]઼?)્A" replacement="$1ા"/>
  <pattern input="([ક-હ]઼?)a" replacement="$1ા"/>
  <pattern input="([ક-હ]઼?)્i" replacement="$1િ"/>
  <pattern input="([ક-હ]઼?)(્I|િi|ેe)" replacement="$1ી"/>
  <pattern input="([ક-હ]઼?)્u" replacement="$1ુ"/>
  <pattern input="([ક-હ]઼?)(ુu|્U|ોo)" replacement="$1ૂ"/>
  <pattern input="([ક-હ]઼?)્R" replacement="$1ૃ"/>
  <pattern input="([ક-હ]઼?)ૃR" replacement="$1ૄ"/>
  <pattern input="([ક-હ]઼?)ે\^" replacement="$1ૅ"/>
  <pattern input="([ક-હ]઼?)્e" replacement="$1ે"/>
  <pattern input="([ક-હ]઼?)્E" replacement="$1ૅ"/>
  <pattern input="([ક-હ]઼?)ો\^" replacement="$1ૉ"/>
  <pattern input="([ક-હ]઼?)i" replacement="$1ૈ"/>
  <pattern input="([ક-હ]઼?)્o" replacement="$1ો"/>
  <pattern input="([ક-હ]઼?)્O" replacement="$1ૉ"/>
  <pattern input="([ક-હ]઼?)u" replacement="$1ૌ"/>
  <pattern input="([ક-હ]઼?)ૃa" replacement="$1્ર"/>
  <pattern input="([ક-હ]઼?)ૃA" replacement="$1્રા"/>
  <pattern input="([ક-હ]઼?)ૃi" replacement="$1્રિ"/>
  <pattern input="([ક-હ]઼?)ૃI" replacement="$1્રી"/>
  <pattern input="([ક-હ]઼?)ૃu" replacement="$1્રુ"/>
  <pattern input="([ક-હ]઼?)ૃU" replacement="$1્રૂ"/>
  <pattern input="([ક-હ]઼?)ૃ\^" replacement="$1્રૅ"/>
  <pattern input="([ક-હ]઼?)ૃe" replacement="$1્રે"/>
  <pattern input="([ક-હ]઼?)ૃE" replacement="$1્રૅ"/>
  <pattern input="([ક-હ]઼?)ૃo" replacement="$1્રો"/>
  <pattern input="([ક-હ]઼?)ૃO" replacement="$1્રો"/>
  <pattern input="([ક-હ]઼?)ૃ\~" replacement="$1્ર્"/>
  <pattern input="([ક-હ])્J" replacement="$1઼્"/>
  <pattern input="અa" replacement="આ"/>
  <pattern input="(ઑo|ઉu)" replacement="ઊ"/>
  <pattern input="ઍ\^" replacement="એ"/>
  <pattern input="અi" replacement="ઐ"/>
  <pattern input="અ\^" replacement="ઍ"/>
  <pattern input="(ઇi|ઍe)" replacement="ઈ"/>
  <pattern input="ઑ\^" replacement="ઓ"/>
  <pattern input="અu" replacement="ઔ"/>
  <pattern input="ઋR" replacement="ૠ"/>
  <pattern input="ળ્l" replacement="ઌ"/>
  <pattern input="ઌl" replacement="ૡ"/>
  <pattern input="ં\^" replacement="ઁ"/>
  <pattern input="ઑM" replacement="ૐ"/>
  <pattern input="રY" replacement="ર્ય"/>
  <pattern input="ક્h" replacement="ખ્"/>
  <pattern input="ગ્h" replacement="ઘ્"/>
  <pattern input="ન્g" replacement="ઙ્"/>
  <pattern input="ચ્h" replacement="છ્"/>
  <pattern input="જ્h" replacement="ઝ્"/>
  <pattern input="જ્n" replacement="જ્ઞ્"/>
  <pattern input="ગ્n" replacement="જ્ઞ્"/>
  <pattern input="ન્j" replacement="ઞ્"/>
  <pattern input="ટ્h" replacement="ઠ્"/>
  <pattern input="ડ્h" replacement="ઢ્"/>
  <pattern input="ત્h" replacement="થ્"/>
  <pattern input="દ્h" replacement="ધ્"/>
  <pattern input="પ્h" replacement="ફ્"/>
  <pattern input="બ્h" replacement="ભ્"/>
  <pattern input="ઋa" replacement="ર"/>
  <pattern input="ઋA" replacement="રા"/>
  <pattern input="ઋi" replacement="રિ"/>
  <pattern input="ઋI" replacement="રી"/>
  <pattern input="ઋu" replacement="રુ"/>
  <pattern input="ઋU" replacement="રૂ"/>
  <pattern input="ઋ\^" replacement="રૅ"/>
  <pattern input="ઋe" replacement="રે"/>
  <pattern input="ઋE" replacement="રૅ"/>
  <pattern input="ઋo" replacement="રો"/>
  <pattern input="ઋ\~" replacement="ર્"/>
  <pattern input="સ્h" replacement="શ્"/>
  <pattern input="ક઼્h" replacement="ખ"/>
  <pattern input="a" replacement="અ"/>
  <pattern input="b" replacement="બ્"/>
  <pattern input="c" replacement="ચ્"/>
  <pattern input="d" replacement="દ્"/>
  <pattern input="e" replacement="એ"/>
  <pattern input="f" replacement="ફ્"/>
  <pattern input="g" replacement="ગ્"/>
  <pattern input="h" replacement="હ્"/>
  <pattern input="i" replacement="ઇ"/>
  <pattern input="j" replacement="જ્"/>
  <pattern input="k" replacement="ક્"/>
  <pattern input="l" replacement="લ્"/>
  <pattern input="m" replacement="મ્"/>
  <pattern input="n" replacement="ન્"/>
  <pattern input="o" replacement="ઓ"/>
  <pattern input="p" replacement="પ્"/>
  <pattern input="r" replacement="ર્"/>
  <pattern input="s" replacement="સ્"/>
  <pattern input="t" replacement="ત્"/>
  <pattern input="u" replacement="ઉ"/>
  <pattern input="(v|w)" replacement="વ્"/>
  <pattern input="x" replacement="ક્ષ્"/>
  <pattern input="y" replacement="ય્"/>
  <pattern input="(z|Z)" replacement="ઝ્"/>
  <pattern input="A" replacement="આ"/>
  <pattern input="D" replacement="ડ્"/>
  <pattern input="E" replacement="ઍ"/>
  <pattern input="F" replacement="ફ્"/>
  <pattern input="H" replacement="ઃ"/>
  <pattern input="I" replacement="ઈ"/>
  <pattern input="L" replacement="ળ્"/>
  <pattern input="M" replacement="ં"/>
  <pattern input="N" replacement="ણ્"/>
  <pattern input="O" replacement="ઑ"/>
  <pattern input="R" replacement="ઋ"/>
  <pattern input="S" replacement="ષ્"/>
  <pattern input="T" replacement="ટ્"/>
  <pattern input="U" replacement="ઊ"/>
  <pattern input="0" replacement="૦"/>
  <pattern input="1" replacement="૧"/>
  <pattern input="2" replacement="૨"/>
  <pattern input="3" replacement="૩"/>
  <pattern input="4" replacement="૪"/>
  <pattern input="5" replacement="૫"/>
  <pattern input="6" replacement="૬"/>
  <pattern input="7" replacement="૭"/>
  <pattern input="8" replacement="૮"/>
  <pattern input="9" replacement="૯"/>
  <pattern input="।K" replacement="॥"/>
  <pattern input="K" replacement="।"/>
  <pattern input="//" replacement="ઽ"/>
  <pattern input="J" replacement="઼"/>
  <pattern input="(‌)*`" replacement="‌"/>
</inputmethod>
