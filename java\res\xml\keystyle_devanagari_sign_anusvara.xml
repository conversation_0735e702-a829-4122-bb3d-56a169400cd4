<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- The code point U+25CC for key label is needed because the font rendering system prior to
     API version 16 can't automatically render dotted circle for incomplete combining letter
     of some scripts. The files named res/xml/key_*.xml have this U+25CC hack, although the
     counterpart files named res/xml-v16/key_*.xml don't have this hack. -->
<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSet="hindi_compact">
            <!-- U+25CC: "◌" DOTTED CIRCLE
                 U+0903: "ः‍" DEVANAGARI SIGN VISARGA
                 U+0901: "ँ" DEVANAGARI SIGN CANDRABINDU
                 U+093C: "़" DEVANAGARI SIGN NUKTA -->
            <key-style
                latin:styleName="moreKeysDevanagariSignAnusvara"
                latin:moreKeys="&#x25CC;&#x0903;|&#x0903;,&#x25CC;&#x0901;|&#x0901;,&#x25CC;&#x093C;|&#x093C;" />
        </case>
        <case latin:keyboardLayoutSet="marathi">
            <!-- U+25CC: "◌" DOTTED CIRCLE
                 U+0903: "ः‍" DEVANAGARI SIGN VISARGA
                 U+0901: "ँ" DEVANAGARI SIGN CANDRABINDU -->
            <key-style
                latin:styleName="moreKeysDevanagariSignAnusvara"
                latin:moreKeys="&#x25CC;&#x0903;|&#x0903;,&#x25CC;&#x0901;|&#x0901;" />
        </case>
        <default>
            <key-style latin:styleName="moreKeysDevanagariSignAnusvara" />
        </default>
    </switch>
    <!-- U+25CC: "◌" DOTTED CIRCLE
         U+0902: "ं" DEVANAGARI SIGN ANUSVARA -->
    <key-style
        latin:styleName="baseKeyDevanagariSignAnusvara"
        latin:parentStyle="moreKeysDevanagariSignAnusvara"
        latin:keySpec="&#x25CC;&#x0902;|&#x0902;"
        latin:keyLabelFlags="followKeyLetterRatio" />
</merge>
