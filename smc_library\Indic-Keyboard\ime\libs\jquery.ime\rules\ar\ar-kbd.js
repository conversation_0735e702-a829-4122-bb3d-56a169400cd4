( function ( $ ) {
	'use strict';

	var arKbd = {
		id: 'ar-kbd',
		name: 'kbd',
		description: 'Arabic keyboard layout',
		date: '2013-02-09',
		URL: 'http://github.com/wikimedia/jquery.ime',
		author: '<PERSON><PERSON>',
		license: 'GPLv3',
		version: '1.0',
		patterns: [
  			['1', '١'],
			['2', '٢'],
			['3', '٣'],
			['4', '٤'],
			['5', '٥'],
			['6', '٦'],
			['7', '٧'],
			['8', '٨'],
			['9', '٩'],
			['0', '٠'],
			['`', 'ذ'],

			['q', 'ض'],
			['w', 'ص'],
			['e', 'ث'],
			['r', 'ق'],
			['t', 'ف'],
			['y', 'غ'],
			['u', 'ع'],
			['i', 'ه'],
			['o', 'خ'],
			['p', 'ح'],
			['\\[', 'ج'],
			['\\]', 'د'],

			['a', 'ش'],
			['s', 'س'],
			['d', 'ي'],
			['f', 'ب'],
			['g', 'ل'],
			['h', 'ا'],
			['j', 'ت'],
			['k', 'ن'],
			['l', 'م'],
			[';', 'ك'],
			['\'', 'ط'],

			['z', 'ئ'],
			['x', 'ء'],
			['c', 'ؤ'],
			['v', 'ر'],
			['b', 'لا'],
			['n', 'ى'],
			['m', 'ة'],
			[',', 'و'],
			['\\.', 'ز'],
			['/', 'ظ'],

			['\\(', ')'],
			['\\)', '('],
			['\\~', 'ّ'],

			['Q', 'َ'],
			['W', 'ً'],
			['E', 'ُ'],
			['R', 'ٌ'],
			['T', 'لإ'],
			['Y', 'إ'],
			['U', '`'],
			['I', '÷'],
			['O', '×'],
			['P', '؛'],
			['\\{', '>'],
			['\\}', '<'],

			['A', 'ِ'],
			['S', 'ٍ'],
			['D', ']'],
			['F', '['],
			['G', 'لأ'],
			['H', 'أ'],
			['J', 'ـ'],
			['K', '،'],
			['L', '/'],

  			['Z', '~'],
			['X', 'ْ'],
			['C', '}'],
			['V', '{'],
			['B', 'لآ'],
			['N', 'آ'],
			['M', '\''],
			['\\<', ','],
			['\\>', '.'],
			['\\?', '؟']
 		],
		patterns_x: [
			[',', '>'],
			['\\.', '<'],
			['\\[', ']'],
			['\\]', '['],
			['J', '‍'],
			['L', '‎'],
			['N', '‌'],
			['R', '‏'],
			['\\{', '}'],
			['\\}', '{']
		]
	};

	$.ime.register( arKbd );

}( jQuery ) );
