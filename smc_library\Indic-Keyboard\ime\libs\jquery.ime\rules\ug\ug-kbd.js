( function ( $ ) {
	'use strict';

	var ugKbd = {
		id: 'ug-kbd',
		name: 'Uyghur kbd',
		description: 'Uyghur kbd keyboard layout',
		date: '2013-02-12',
		URL: 'http://github.com/wikimedia/jquery.ime',
		author: 'Parag <PERSON>',
		license: 'GPLv3',
		version: '1.0',
		patterns: [
			['q', '\u0686'],
			['w', '\u06CB'],
			['e', '\u06D0'],
			['r', '\u0631'],
			['t', '\u062A'],
			['y', '\u064A'],
			['u', '\u06C7'],
			['i', '\u06AD'],
			['o', '\u0648'],
			['p', '\u067E'],
			['a', '\u06BE'],
			['s', '\u0633'],
			['d', '\u062F'],
			['f', '\u0627'],
			['g', '\u06D5'],
			['h', '\u0649'],
			['j', '\u0642'],
			['k', '\u0643'],
			['l', '\u0644'],
			[';', '\u061B'],
			['z', '\u0632'],
			['x', '\u0634'],
			['c', '\u063A'],
			['v', '\u06C8'],
			['b', '\u0628'],
			['n', '\u0646'],
			['m', '\u0645'],
			[',', '\u060C'],
			['/', '\u0626'],
			['Q', '\u0686'],
			['W', '\u06CB'],
			['E', '\u06D0'],
			['R', '\u0631'],
			['T', '\u062A'],
			['Y', '\u064A'],
			['U', '\u06C7'],
			['I', '\u06AD'],
			['O', '\u0648'],
			['P', '\u067E'],
			['\\{', '\u00AB'],
			['\\}', '\u00BB'],
			['A', '\u06BE'],
			['S', '\u0633'],
			['D', '\u0698'],
			['F', '\u0641'],
			['G', '\u06AF'],
			['H', '\u062E'],
			['J', '\u062C'],
			['K', '\u06C6'],
			['L', '\u0644'],
			['Z', '\u0632'],
			['X', '\u0634'],
			['C', '\u063A'],
			['V', '\u06C8'],
			['B', '\u0628'],
			['N', '\u0646'],
			['M', '\u0645'],
			['\\?', '\u061F'],
			['\\<', '\u2039'],
			['\\>', '\u203A'],
			['\\_', '\u2014']
		]
	};
	$.ime.register( ugKbd );

}( jQuery ) );

