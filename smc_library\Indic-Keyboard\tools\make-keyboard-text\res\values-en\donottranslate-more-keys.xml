<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2010, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- U+00E0: "à" LATIN SMALL LETTER A WITH GRAVE
         U+00E1: "á" LATIN SMALL LETTER A WITH ACUTE
         U+00E2: "â" LATIN SMALL LETTER A WITH CIRCUMFLEX
         U+00E4: "ä" LATIN SMALL LETTER A WITH DIAERESIS
         U+00E6: "æ" LATIN SMALL LETTER AE
         U+00E3: "ã" LATIN SMALL LETTER A WITH TILDE
         U+00E5: "å" LATIN SMALL LETTER A WITH RING ABOVE
         U+0101: "ā" LATIN SMALL LETTER A WITH MACRON -->
    <string name="morekeys_a">&#x00E0;,&#x00E1;,&#x00E2;,&#x00E4;,&#x00E6;,&#x00E3;,&#x00E5;,&#x0101;</string>
    <!-- U+00E9: "é" LATIN SMALL LETTER E WITH ACUTE
         U+00E8: "è" LATIN SMALL LETTER E WITH GRAVE
         U+00EA: "ê" LATIN SMALL LETTER E WITH CIRCUMFLEX
         U+00EB: "ë" LATIN SMALL LETTER E WITH DIAERESIS
         U+0113: "ē" LATIN SMALL LETTER E WITH MACRON -->
    <string name="morekeys_e">&#x00E9;,&#x00E8;,&#x00EA;,&#x00EB;,&#x0113;</string>
    <!-- U+00ED: "í" LATIN SMALL LETTER I WITH ACUTE
         U+00EE: "î" LATIN SMALL LETTER I WITH CIRCUMFLEX
         U+00EF: "ï" LATIN SMALL LETTER I WITH DIAERESIS
         U+012B: "ī" LATIN SMALL LETTER I WITH MACRON
         U+00EC: "ì" LATIN SMALL LETTER I WITH GRAVE -->
    <string name="morekeys_i">&#x00ED;,&#x00EE;,&#x00EF;,&#x012B;,&#x00EC;</string>
    <!-- U+00F3: "ó" LATIN SMALL LETTER O WITH ACUTE
         U+00F4: "ô" LATIN SMALL LETTER O WITH CIRCUMFLEX
         U+00F6: "ö" LATIN SMALL LETTER O WITH DIAERESIS
         U+00F2: "ò" LATIN SMALL LETTER O WITH GRAVE
         U+0153: "œ" LATIN SMALL LIGATURE OE
         U+00F8: "ø" LATIN SMALL LETTER O WITH STROKE
         U+014D: "ō" LATIN SMALL LETTER O WITH MACRON
         U+00F5: "õ" LATIN SMALL LETTER O WITH TILDE -->
    <string name="morekeys_o">&#x00F3;,&#x00F4;,&#x00F6;,&#x00F2;,&#x0153;,&#x00F8;,&#x014D;,&#x00F5;</string>
    <!-- U+00FA: "ú" LATIN SMALL LETTER U WITH ACUTE
         U+00FB: "û" LATIN SMALL LETTER U WITH CIRCUMFLEX
         U+00FC: "ü" LATIN SMALL LETTER U WITH DIAERESIS
         U+00F9: "ù" LATIN SMALL LETTER U WITH GRAVE
         U+016B: "ū" LATIN SMALL LETTER U WITH MACRON -->
    <string name="morekeys_u">&#x00FA;,&#x00FB;,&#x00FC;,&#x00F9;,&#x016B;</string>
    <!-- U+00DF: "ß" LATIN SMALL LETTER SHARP S -->
    <string name="morekeys_s">&#x00DF;</string>
    <!-- U+00F1: "ñ" LATIN SMALL LETTER N WITH TILDE -->
    <string name="morekeys_n">&#x00F1;</string>
    <!-- U+00E7: "ç" LATIN SMALL LETTER C WITH CEDILLA -->
    <string name="morekeys_c">&#x00E7;</string>
</resources>
