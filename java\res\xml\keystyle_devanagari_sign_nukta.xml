<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- The code point U+25CC for key label is needed because the font rendering system prior to
     API version 16 can't automatically render dotted circle for incomplete combining letter
     of some scripts. The files named res/xml/key_*.xml have this U+25CC hack, although the
     counterpart files named res/xml-v16/key_*.xml don't have this hack. -->
<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSet="hindi">
            <!-- U+25CC: "◌" DOTTED CIRCLE
                 U+097D: "ॽ" DEVANAGARI LETTER GLOTTAL STOP
                 U+0970: "॰" DEVANAGARI ABBREVIATION SIGN
                 U+093D: "ऽ" DEVANAGARI SIGN AVAGRAHA -->
            <key-style
                latin:styleName="moreKeysDevanagariSignNukta"
                latin:moreKeys="&#x25CC;&#x097D;|&#x097D;,&#x25CC;&#x0970;|&#x0970;,&#x25CC;&#x093D;|&#x093D;" />
        </case>
        <case latin:keyboardLayoutSet="nepali_romanized">
            <!-- U+25CC: "◌" DOTTED CIRCLE
                 U+093C: "़" DEVANAGARI SIGN NUKTA -->
            <key-style
                latin:styleName="moreKeysDevanagariSignNukta"
                latin:moreKeys="&#x25CC;&#x093C;|&#x093C;" />
        </case>
        <default>
            <key-style latin:styleName="moreKeysDevanagariSignNukta" />
        </default>
    </switch>
    <!-- U+25CC: "◌" DOTTED CIRCLE
         U+093C: "़" DEVANAGARI SIGN NUKTA -->
    <key-style
        latin:styleName="baseKeyDevanagariSignNukta"
        latin:parentStyle="moreKeysDevanagariSignNukta"
        latin:keySpec="&#x25CC;&#x093C;|&#x093C;"
        latin:keyLabelFlags="followKeyLetterRatio" />
</merge>
