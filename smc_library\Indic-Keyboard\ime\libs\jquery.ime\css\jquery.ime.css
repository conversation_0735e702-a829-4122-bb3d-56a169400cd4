.imeselector {
	position: absolute;
	/* Fix rgba fallback bug - http://css-tricks.com/ie-background-rgb-bug */
	/* @embed */
	background: url('../images/ime-active.png') no-repeat 3px center #fff;
	/* @embed */
	background: url('../images/ime-active.png') no-repeat 3px center rgba(255,255,255,0.75);
	cursor: pointer;
	height: 15px;
	font-size: small;
	padding: 2px 2px 1px 20px;
	box-shadow: 0 1px 3px 0 #777;
	margin-top: 0;
	text-align: left;
	font-family: sans-serif;
	white-space: nowrap;
	z-index: 9999;
}

div.imeselector:hover {
	box-shadow: 0 1px 3px 0 #565656;
	border-top: none;
	background-color: rgba(255,255,255,0.85);
}

.imeselector a {
	text-decoration: none;
	outline: none;
	color: #222222;
	line-height: 1em;
	padding-top: 4px;
	padding-bottom: 4px;
}

.ime-setting-caret {
	margin-left: 2px;
	margin-top: 8px;
	border-left: 4px solid transparent;
	border-right: 4px solid transparent;
	border-top: 4px solid #565656;
	content: "";
	display: inline-block;
	height: 0;
	vertical-align: top;
	width: 0;

}

span.ime-disable-link {
	color: #222222;
	line-height: 1em;
	padding-top: 4px;
	padding-left: 20px;
	padding-bottom: 4px;
	white-space: nowrap;
}

span.ime-disable-shortcut {
	text-align: right;
	margin-left: 10px;
	color: #888;
	font-size: smaller;
	padding-right: 4px;
}

.ime-disable-link:hover span {
	color: #eee;
}

.ime-list-title,
.ime-lang-title {
	color: #39d;
	border-bottom: solid 1px #39d;
	text-align: left;
	font-size: larger;
	font-weight: normal;
	padding-bottom: 5px;
	padding-left: 20px;
	padding-top: 9px;
	margin: 0 0 1px;
}

.ime-language-list-wrapper {
	position: relative;
	padding: 0;
	display: block;
	overflow-y: auto;
	max-height: 150px;
}

.imeselector-menu {
	position: absolute;
	top: 14px;
	right: 0;
	z-index: 1000;
	display: none;
	float: left;
	margin-top: 13px;
	min-width: 160px;
	padding: 0;
	border: 1px solid #888;
	background-color: #FFFFFF;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	-webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	-webkit-background-clip: padding-box;
	-moz-background-clip: padding;
	background-clip: padding-box;
	text-align: left;
}

.imeselector-menu ul {
	width: 100%;
	padding: 0;
	margin: 0;
	list-style: none;
}

/* The triangle shaped callout */
.imeselector-menu:before {
	border-bottom: 7px solid #888;
	border-left: 7px solid transparent;
	border-right: 7px solid transparent;
	content: "";
	display: inline-block;
	right: 9px;
	position: absolute;
	top: -7px;
}

.imeselector-menu:after {
	border-bottom: 6px solid #FFFFFF;
	border-left: 6px solid transparent;
	border-right: 6px solid transparent;
	content: "";
	display: inline-block;
	right: 10px;
	position: absolute;
	top: -6px;
}

.imeselector-menu.position-top:before {
	border-bottom: 0 none;
	border-top: 7px solid #888;
	top: auto;
	bottom: -7px;
}

.imeselector-menu.position-top:after {
	border-bottom: 0 none;
	border-top: 6px solid #FFFFFF;
	top: auto;
	bottom: -6px;
}

.imeselector-menu .checked {
	/* @embed */
	background: url(../images/tick.png) 4px center no-repeat;
}

.imeselector-menu .ime-help-link {
	background-color: #f0f0f0;
	border-radius: 0 0 5px 5px;
	border-top: 1px solid #ddd;
	margin-top: 6px;
	padding: 2px 0;
	color: #444;
}

.imeselector-menu .ime-help-link > a:hover {
	background-color: #f0f0f0;
	color: #000;
}

.imeselector-menu a {
	display: block;
	padding-left: 20px;
	padding-right: 20px;
	clear: both;
	font-weight: normal;
	color: #333333;
	outline: none;
	white-space: nowrap;
}

.imeselector-menu li > a:hover {
	text-decoration: none;
	color: #ffffff;
	background-color: #5089e8;
}

.open {
	*z-index: 1000;
	display: block;
}

.imeselector-menu li {
	position: relative;
}
