<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case
            latin:languageCode="fa"
            latin:languageSwitchKeyEnabled="true"
        >
            <Key
                latin:keyStyle="languageSwitchKeyStyle" />
            <Key
                latin:keyStyle="spaceKeyStyle"
                latin:keyWidth="30%p" />
            <Key
                latin:keyStyle="zwnjKeyStyle" />
        </case>
        <case
            latin:languageCode="fa"
            latin:languageSwitchKeyEnabled="false"
        >
            <Key
                latin:keyStyle="spaceKeyStyle"
                latin:keyWidth="40%p" />
            <Key
                latin:keyStyle="zwnjKeyStyle" />
        </case>
        <case
            latin:languageSwitchKeyEnabled="true"
        >
            <Key
                latin:keyStyle="languageSwitchKeyStyle" />
            <Key
                latin:keyStyle="spaceKeyStyle"
                latin:keyWidth="40%p" />
        </case>
        <!-- languageSwitchKeyEnabled="false" -->
        <default>
            <Key
                latin:keyStyle="spaceKeyStyle"
                latin:keyWidth="50%p" />
        </default>
    </switch>
</merge>
