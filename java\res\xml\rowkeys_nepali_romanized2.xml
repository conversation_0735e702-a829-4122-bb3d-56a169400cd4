<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted">
            <!-- U+0906: "आ" DEVANAGARI LETTER AA -->
            <Key latin:keySpec="&#x0906;" />
            <!-- U+0936: "श" DEVANAGARI LETTER SHA -->
            <Key latin:keySpec="&#x0936;" />
            <!-- U+0927: "ध" DEVANAGARI LETTER DHA -->
            <Key latin:keySpec="&#x0927;" />
            <!-- U+090A: "ऊ" DEVANAGARI LETTER UU -->
            <Key latin:keySpec="&#x090A;" />
            <!-- U+0918: "घ" DEVANAGARI LETTER GHA -->
            <Key latin:keySpec="&#x0918;" />
            <!-- U+0905: "अ" DEVANAGARI LETTER A -->
            <Key latin:keySpec="&#x0905;" />
            <!-- U+091D: "झ" DEVANAGARI LETTER JHA -->
            <Key latin:keySpec="&#x091D;" />
            <!-- U+0916: "ख" DEVANAGARI LETTER KHA -->
            <Key latin:keySpec="&#x0916;" />
            <!-- U+0965: "॥" DEVANAGARI DOUBLE DANDA -->
            <Key latin:keySpec="&#x0965;" />
            <!-- U+0910: "ऐ" DEVANAGARI LETTER AI -->
            <Key latin:keySpec="&#x0910;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_visarga" />
            <Key latin:keyStyle="baseKeyDevanagariSignVisarga" />
        </case>
        <default>
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_aa" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignAa" />
            <!-- U+0938: "स" DEVANAGARI LETTER SA -->
            <Key latin:keySpec="&#x0938;" />
            <!-- U+0926: "द" DEVANAGARI LETTER DA -->
            <Key latin:keySpec="&#x0926;" />
            <!-- U+0909: "उ" DEVANAGARI LETTER U -->
            <Key latin:keySpec="&#x0909;" />
            <!-- U+0917: "ग" DEVANAGARI LETTER GA -->
            <Key latin:keySpec="&#x0917;" />
            <!-- U+0939: "ह" DEVANAGARI LETTER HA -->
            <Key latin:keySpec="&#x0939;" />
            <!-- U+091C: "ज" DEVANAGARI LETTER JA -->
            <Key latin:keySpec="&#x091C;" />
            <!-- U+0915: "क" DEVANAGARI LETTER KA -->
            <Key latin:keySpec="&#x0915;" />
            <!-- U+0932: "ल" DEVANAGARI LETTER LA -->
            <Key latin:keySpec="&#x0932;" />
            <!-- U+090F: "ए" DEVANAGARI LETTER E -->
            <Key latin:keySpec="&#x090F;" />
            <!-- U+0950: "ॐ" DEVANAGARI OM -->
            <Key latin:keySpec="&#x0950;" />
        </default>
    </switch>
</merge>
