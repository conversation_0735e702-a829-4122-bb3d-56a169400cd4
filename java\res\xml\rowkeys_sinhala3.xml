<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted">
            <!-- U+0D9E: "ඞ" SINHALA LETTER KANTAJA NAASIKYAYA -->
            <Key latin:keySpec="&#x0D9E;" />
            <!-- U+0DA3: "ඣ" SINHALA LETTER MAHAAPRAANA JAYANNA -->
            <Key latin:keySpec="&#x0DA3;" />
            <!-- U+0DAA: "ඪ" SINHALA LETTER MAHAAPRAANA DDAYANNA -->
            <Key latin:keySpec="&#x0DAA;" />
            <!-- U+0D8A: "ඊ" SINHALA LETTER IIYANNA -->
            <Key latin:keySpec="&#x0D8A;" />
            <!-- U+0DB7: "භ" SINHALA LETTER MAHAAPRAANA BAYANNA -->
            <Key latin:keySpec="&#x0DB7;" />
            <!-- U+0DB5: "ඵ" SINHALA LETTER MAHAAPRAANA PAYANNA -->
            <Key latin:keySpec="&#x0DB5;" />
            <!-- U+0DC5: "ළ" SINHALA LETTER MUURDHAJA LAYANNA -->
            <Key latin:keySpec="&#x0DC5;" />
            <!-- U+0D9D: "ඝ" SINHALA LETTER MAHAAPRAANA GAYANNA -->
            <Key latin:keySpec="&#x0D9D;" />
            <!-- U+0DBB/U+0DCA/U+200D: "ර්‍" SINHALA LETTER RAYANNA/SINHALA SIGN AL-LAKUNA/ZERO WIDTH JOINER -->
            <Key latin:keySpec="&#x0DBB;&#x0DCA;&#x200D;" />
        </case>
        <default>
            <!-- U+0D82: "ං" SINHALA SIGN ANUSVARAYA
                 U+0D83: "ඃ" SINHALA SIGN VISARGAYA -->
            <Key
                latin:keySpec="&#x0D82;"
                latin:moreKeys="&#x0D83;" />
            <!-- U+0DA2: "ජ" SINHALA LETTER ALPAPRAANA JAYANNA
                 U+0DA6: "ඦ" SINHALA LETTER SANYAKA JAYANNA -->
            <Key
                latin:keySpec="&#x0DA2;"
                latin:moreKeys="&#x0DA6;" />
            <!-- U+0DA9: "ඩ" SINHALA LETTER ALPAPRAANA DDAYANNA
                 U+0DAC: "ඬ" SINHALA LETTER SANYAKA DDAYANNA -->
            <Key
                latin:keySpec="&#x0DA9;"
                latin:moreKeys="&#x0DAC;" />
            <!-- U+0D89: "ඉ" SINHALA LETTER IYANNA -->
            <Key latin:keySpec="&#x0D89;" />
            <!-- U+0DB6: "බ" SINHALA LETTER ALPAPRAANA BAYANNA -->
            <Key latin:keySpec="&#x0DB6;" />
            <!-- U+0DB4: "ප" SINHALA LETTER ALPAPRAANA PAYANNA -->
            <Key latin:keySpec="&#x0DB4;" />
            <!-- U+0DBD: "ල" SINHALA LETTER DANTAJA LAYANNA -->
            <Key latin:keySpec="&#x0DBD;" />
            <!-- U+0D9C: "ග" SINHALA LETTER ALPAPRAANA GAYANNA
                 U+0D9F: "ඟ" SINHALA LETTER SANYAKA GAYANNA -->
            <Key
                latin:keySpec="&#x0D9C;"
                latin:moreKeys="&#x0D9F;" />
            <!-- U+0DF3: "ෳ" SINHALA VOWEL SIGN DIGA GAYANUKITTA -->
            <Key latin:keySpec="&#x0DF3;" />
        </default>
    </switch>
</merge>
