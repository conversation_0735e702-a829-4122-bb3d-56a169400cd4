<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- For keyboard color scheme option dialog. -->
    <string-array name="keyboard_theme_names" translatable="false">
        <item>@string/keyboard_theme_material_light</item>
        <item>@string/keyboard_theme_material_dark</item>
        <item>@string/keyboard_theme_holo_white</item>
        <item>@string/keyboard_theme_holo_blue</item>
    </string-array>
    <!-- An element must be a keyboard theme id of {@link KeyboardTheme#THEME_ID_*}. -->
    <string-array name="keyboard_theme_ids" translatable="false">
        <item>3</item>
        <item>4</item>
        <item>2</item>
        <item>0</item>
    </string-array>
</resources>
