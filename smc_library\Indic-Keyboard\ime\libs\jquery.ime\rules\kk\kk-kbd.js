( function ( $ ) {
	'use strict';

	var kkKbd = {
		id: 'kk-kbd',
		name: 'Kazak kbd',
		description: 'Kazak kbd in Cyrillic script keyboard layout',
		date: '2013-02-12',
		URL: 'http://github.com/wikimedia/jquery.ime',
		author: '<PERSON><PERSON>',
		license: 'GPLv3',
		version: '1.0',
		patterns: [
			['`', '('],
			['\\~', ')'],
			['1', '\"'],
			['2', 'ә'],
			['\\@', 'Ә'],
			['3', 'і'],
			['\\#', 'І'],
			['4', 'ӊ'],
			['\\$', 'Ӊ'],
			['5', 'ғ'],
			['\\%', 'Ғ'],
			['6', ','],
			['\\^', ';'],
			['7', '.'],
			['\\&', ':'],
			['8', 'ү'],
			['\\*', 'Ү'],
			['9', 'ұ'],
			['\\(', 'Ұ'],
			['0', 'қ'],
			['\\)', 'Қ'],
			['\\-', 'ө'],
			['\\_', 'Ө'],
			['\\=', 'һ'],
			['\\+', 'Һ'],
			['q', 'й'],
			['Q', 'Й'],
			['w', 'ц'],
			['W', 'Ц'],
			['e', 'у'],
			['E', 'У'],
			['r', 'к'],
			['R', 'К'],
			['t', 'е'],
			['T', 'Е'],
			['y', 'н'],
			['Y', 'Н'],
			['u', 'г'],
			['U', 'Г'],
			['i', 'ш'],
			['I', 'Ш'],
			['o', 'щ'],
			['O', 'Щ'],
			['p', 'з'],
			['P', 'З'],
			['\\[', 'х'],
			['\\{', 'Х'],
			['\\]', 'ъ'],
			['\\}', 'Ъ'],
			['a', 'ф'],
			['A', 'Ф'],
			['s', 'ы'],
			['S', 'Ы'],
			['d', 'в'],
			['D', 'В'],
			['f', 'а'],
			['F', 'А'],
			['g', 'п'],
			['G', 'П'],
			['h', 'р'],
			['H', 'Р'],
			['j', 'о'],
			['J', 'О'],
			['k', 'л'],
			['K', 'Л'],
			['l', 'д'],
			['L', 'Д'],
			[';', 'ж'],
			[':', 'Ж'],
			['\'', 'э'],
			['\"', 'Э'],
			['z', 'я'],
			['Z', 'Я'],
			['x', 'ч'],
			['X', 'Ч'],
			['c', 'с'],
			['C', 'С'],
			['v', 'м'],
			['V', 'М'],
			['b', 'и'],
			['B', 'И'],
			['n', 'т'],
			['N', 'Т'],
			['m', 'ь'],
			['M', 'Ь'],
			[',', 'б'],
			['\\<', 'Б'],
			['\\.', 'ю'],
			['\\>', 'Ю'],
			['/', '№']
		]
	};
	$.ime.register( kkKbd );

}( jQuery ) );

