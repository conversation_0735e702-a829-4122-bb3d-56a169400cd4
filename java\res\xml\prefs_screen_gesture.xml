<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<PreferenceScreen
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:title="@string/settings_screen_gesture"
    android:key="screen_gesture">
    <CheckBoxPreference
        android:key="gesture_input"
        android:title="@string/gesture_input"
        android:summary="@string/gesture_input_summary"
        android:defaultValue="true"
        android:persistent="true" />
    <CheckBoxPreference
        android:key="pref_gesture_floating_preview_text"
        android:dependency="gesture_input"
        android:title="@string/gesture_floating_preview_text"
        android:summary="@string/gesture_floating_preview_text_summary"
        android:defaultValue="true"
        android:persistent="true" />
    <CheckBoxPreference
        android:key="pref_gesture_preview_trail"
        android:dependency="gesture_input"
        android:title="@string/gesture_preview_trail"
        android:defaultValue="true"
        android:persistent="true" />
    <CheckBoxPreference
        android:key="pref_gesture_space_aware"
        android:dependency="gesture_input"
        android:title="@string/gesture_space_aware"
        android:summary="@string/gesture_space_aware_summary"
        android:defaultValue="true"
        android:persistent="true" />
</PreferenceScreen>
