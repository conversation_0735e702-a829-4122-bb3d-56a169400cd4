<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- TODO: these settings depend on the language. They should be put either in the dictionary
         header, or in the subtype maybe? -->
    <!-- Symbols that are suggested between words -->
    <string name="suggested_punctuations">!,?,\\,,:,;,\",(,),\',-,/,@,_</string>
    <!-- Symbols that are normally preceded by a space (used to add an auto-space before these) -->
    <string name="symbols_preceded_by_space">([{&amp;</string>
    <!-- Symbols that are normally followed by a space (used to add an auto-space after these) -->
    <string name="symbols_followed_by_space">.,;:!?)]}&amp;</string>
    <!-- Symbols that behave like a single punctuation when typed next to each other -->
    <string name="symbols_clustering_together"></string>
    <!-- Symbols that separate words -->
    <!-- Don't remove the enclosing double quotes, they protect whitespace (not just U+0020) -->
    <string name="symbols_word_separators">"&#x0009;&#x0020;&#x000A;&#x00A0;"()[]{}*&amp;&lt;&gt;+=|.,;:!?/_\"</string>
    <!-- Word connectors -->
    <string name="symbols_word_connectors">\'-</string>
    <!-- The sentence separator code point, for capitalization -->
    <!-- U+002E: "." FULL STOP   ; 2Eh = 46d -->
    <integer name="sentence_separator">46</integer>
    <!-- Whether this language uses spaces between words -->
    <bool name="current_language_has_spaces">true</bool>
</resources>
