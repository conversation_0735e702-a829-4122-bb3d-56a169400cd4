<?xml version="1.0"?>
<inputmethod id="kk-kbd" name="Kazak kbd" description="Kazak kbd in Cyrillic script keyboard layout" author="Parag Nemade" version="1.0" contextLength="0" maxKeyLength="1">
  <pattern input="`" replacement="("/>
  <pattern input="\~" replacement=")"/>
  <pattern input="1" replacement="&quot;"/>
  <pattern input="2" replacement="ә"/>
  <pattern input="\@" replacement="Ә"/>
  <pattern input="3" replacement="і"/>
  <pattern input="\#" replacement="І"/>
  <pattern input="4" replacement="ӊ"/>
  <pattern input="\$" replacement="Ӊ"/>
  <pattern input="5" replacement="ғ"/>
  <pattern input="\%" replacement="Ғ"/>
  <pattern input="6" replacement=","/>
  <pattern input="\^" replacement=";"/>
  <pattern input="7" replacement="."/>
  <pattern input="\&amp;" replacement=":"/>
  <pattern input="8" replacement="ү"/>
  <pattern input="\*" replacement="Ү"/>
  <pattern input="9" replacement="ұ"/>
  <pattern input="\(" replacement="Ұ"/>
  <pattern input="0" replacement="қ"/>
  <pattern input="\)" replacement="Қ"/>
  <pattern input="\-" replacement="ө"/>
  <pattern input="\_" replacement="Ө"/>
  <pattern input="\=" replacement="һ"/>
  <pattern input="\+" replacement="Һ"/>
  <pattern input="q" replacement="й"/>
  <pattern input="Q" replacement="Й"/>
  <pattern input="w" replacement="ц"/>
  <pattern input="W" replacement="Ц"/>
  <pattern input="e" replacement="у"/>
  <pattern input="E" replacement="У"/>
  <pattern input="r" replacement="к"/>
  <pattern input="R" replacement="К"/>
  <pattern input="t" replacement="е"/>
  <pattern input="T" replacement="Е"/>
  <pattern input="y" replacement="н"/>
  <pattern input="Y" replacement="Н"/>
  <pattern input="u" replacement="г"/>
  <pattern input="U" replacement="Г"/>
  <pattern input="i" replacement="ш"/>
  <pattern input="I" replacement="Ш"/>
  <pattern input="o" replacement="щ"/>
  <pattern input="O" replacement="Щ"/>
  <pattern input="p" replacement="з"/>
  <pattern input="P" replacement="З"/>
  <pattern input="\[" replacement="х"/>
  <pattern input="\{" replacement="Х"/>
  <pattern input="\]" replacement="ъ"/>
  <pattern input="\}" replacement="Ъ"/>
  <pattern input="a" replacement="ф"/>
  <pattern input="A" replacement="Ф"/>
  <pattern input="s" replacement="ы"/>
  <pattern input="S" replacement="Ы"/>
  <pattern input="d" replacement="в"/>
  <pattern input="D" replacement="В"/>
  <pattern input="f" replacement="а"/>
  <pattern input="F" replacement="А"/>
  <pattern input="g" replacement="п"/>
  <pattern input="G" replacement="П"/>
  <pattern input="h" replacement="р"/>
  <pattern input="H" replacement="Р"/>
  <pattern input="j" replacement="о"/>
  <pattern input="J" replacement="О"/>
  <pattern input="k" replacement="л"/>
  <pattern input="K" replacement="Л"/>
  <pattern input="l" replacement="д"/>
  <pattern input="L" replacement="Д"/>
  <pattern input=";" replacement="ж"/>
  <pattern input=":" replacement="Ж"/>
  <pattern input="&apos;" replacement="э"/>
  <pattern input="&quot;" replacement="Э"/>
  <pattern input="z" replacement="я"/>
  <pattern input="Z" replacement="Я"/>
  <pattern input="x" replacement="ч"/>
  <pattern input="X" replacement="Ч"/>
  <pattern input="c" replacement="с"/>
  <pattern input="C" replacement="С"/>
  <pattern input="v" replacement="м"/>
  <pattern input="V" replacement="М"/>
  <pattern input="b" replacement="и"/>
  <pattern input="B" replacement="И"/>
  <pattern input="n" replacement="т"/>
  <pattern input="N" replacement="Т"/>
  <pattern input="m" replacement="ь"/>
  <pattern input="M" replacement="Ь"/>
  <pattern input="," replacement="б"/>
  <pattern input="\&lt;" replacement="Б"/>
  <pattern input="\." replacement="ю"/>
  <pattern input="\&gt;" replacement="Ю"/>
  <pattern input="/" replacement="№"/>
</inputmethod>
