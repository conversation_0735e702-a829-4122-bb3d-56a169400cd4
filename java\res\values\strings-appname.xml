<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources>
    <!-- Application name for opensource Android keyboard. AOSP(Android Open Source Project) should not be translated.
         This resource should be copied from msgid="8250992613616792321" -->
    <string name="english_ime_name">Indic Keyboard Prime</string>

    <!-- Name of Android spell checker service. AOSP(Android Open Source Project) should not be translated.
         This resource should be copied from msgid="511950477199948048" -->
    <string name="spell_checker_service_name">Indic Keyboard Prime Spell Checker</string>

    <!-- Title for Android Keyboard settings screen. AOSP(Android Open Source Project) should not be translated.
         This resource should be copied from msgid="423615877174850267" -->
    <string name="english_ime_settings">Indic Keyboard Prime Settings</string>

    <!-- Title for the spell checking service settings screen. AOSP(Android Open Source Project) should not be translated.
         This resource should be copied from msgid="2970535894327288421" -->
    <string name="android_spell_checker_settings">Indic Keyboard Prime Spell Checker Settings</string>
</resources>
