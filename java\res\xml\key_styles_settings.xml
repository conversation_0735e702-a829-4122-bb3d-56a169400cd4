<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <!-- Base key style for the key which may have settings key as more keys. -->
    <!-- Kept as a separate file for cleaner overriding by an overlay.  -->
    <switch>
        <case
            latin:clobberSettingsKey="true"
        >
            <key-style
                latin:styleName="settingsMoreKeysStyle"
                latin:backgroundType="functional"
                latin:parentStyle="baseSettingsMoreKeysStyle" />
        </case>
        <!-- clobberSettingsKey="false" -->
        <default>
            <key-style
                latin:styleName="settingsMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint"
                latin:additionalMoreKeys="!text/keyspec_settings"
                latin:backgroundType="functional"
                latin:parentStyle="baseSettingsMoreKeysStyle" />
        </default>
    </switch>
</merge>
