<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources>
    <!-- Build condition,keyboard_height_in_dp -->
    <string-array name="keyboard_heights" translatable="false">
    <!-- Preferable keyboard height in absolute scale: 1.285in -->
        <!-- Droid -->
        <item>HARDWARE=sholes,227.0167</item>
        <!-- Nexus One -->
        <item>HARDWARE=mahimahi,217.5932</item>
        <!-- Nexus S -->
        <item>HARDWARE=herring,200.8554</item>
        <!-- Galaxy Nexus -->
        <item>HARDWARE=tuna,202.5869</item>
    <!-- Preferable keyboard height in absolute scale: 48.0mm -->
        <!-- Xoom -->
        <item>HARDWARE=stingray,283.1337</item>
    </string-array>
</resources>
