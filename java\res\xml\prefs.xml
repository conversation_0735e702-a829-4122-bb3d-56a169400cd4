<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2008 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<PreferenceScreen
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:latin="http://schemas.android.com/apk/res-auto"
    android:key="english_ime_settings">
    <PreferenceScreen
        android:fragment="in.androidtweak.inputmethod.indic.settings.PreferencesSettingsFragment"
        android:title="@string/settings_screen_preferences"
        android:key="screen_preferences" />
    <PreferenceScreen
        android:fragment="in.androidtweak.inputmethod.indic.settings.AppearanceSettingsFragment"
        android:title="@string/settings_screen_appearance"
        android:key="screen_appearance" />
    <PreferenceScreen
        android:fragment="in.androidtweak.inputmethod.indic.settings.MultiLingualSettingsFragment"
        android:title="@string/settings_screen_multilingual"
        android:key="screen_multilingual" />
    <PreferenceScreen
        android:fragment="in.androidtweak.inputmethod.indic.settings.GestureSettingsFragment"
        android:title="@string/settings_screen_gesture"
        android:key="screen_gesture" />
    <PreferenceScreen
        android:fragment="in.androidtweak.inputmethod.indic.settings.CorrectionSettingsFragment"
        android:title="@string/settings_screen_correction"
        android:key="screen_correction" />
    <PreferenceScreen
        android:fragment="in.androidtweak.inputmethod.indic.settings.AdvancedSettingsFragment"
        android:title="@string/settings_screen_advanced"
        android:key="screen_advanced" />
</PreferenceScreen>
