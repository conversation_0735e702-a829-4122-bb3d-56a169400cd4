<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Color resources for IceCreamSandwich theme. Base color = 33B5E5 -->
    <!-- android:color/holo_blue_light value is #FF33B5E5 -->
    <color name="highlight_color_ics">#FF33B5E5</color>
    <color name="typed_word_color_ics">#D833B5E5</color>
    <color name="suggested_word_color_ics">#B233B5E5</color>
    <color name="highlight_translucent_color_ics">#9933B5E5</color>
    <color name="key_text_color_holo">@android:color/white</color>
    <color name="key_text_inactivated_color_holo">#66E0E4E5</color>
    <color name="key_hint_letter_color_holo">#80000000</color>
    <color name="key_hint_label_color_holo">#A0FFFFFF</color>
    <color name="key_shifted_letter_hint_inactivated_color_holo">#66E0E4E5</color>
    <color name="key_shifted_letter_hint_activated_color_holo">@android:color/white</color>
    <color name="spacebar_text_color_holo">#FFC0C0C0</color>
    <color name="spacebar_text_shadow_color_holo">#80000000</color>
    <color name="gesture_floating_preview_color_holo">#C0000000</color>
    <color name="emoji_tab_page_indicator_background_holo">#111111</color>
    <!-- Color resources for KLP theme. Base color = F0F0F0 -->
    <color name="highlight_color_klp">#FFF0F0F0</color>
    <color name="typed_word_color_klp">#D8F0F0F0</color>
    <color name="suggested_word_color_klp">#B2F0F0F0</color>
    <color name="highlight_translucent_color_klp">#99E0E0E0</color>
    <!-- Color resources for LXX_Light theme.
         15%:0x26 70%:0xB3 75%:0xC0 80%:0xCC 85%:0xD9 90%:0xE6 -->
    <color name="key_text_color_lxx_light">#37474F</color>
    <color name="key_functional_text_color_lxx_light">#CC37474F</color>
    <color name="key_text_inactive_color_lxx_light">#B337474F</color>
    <color name="key_hint_letter_color_lxx_light">#B337474F</color>
    <color name="language_on_spacebar_text_color_lxx_light">#B337474F</color>
    <color name="highlight_color_lxx_light">#4DB6AC</color>
    <color name="auto_correct_color_lxx_light">#37474F</color>
    <color name="typed_word_color_lxx_light">#D937474F</color>
    <color name="suggested_word_color_lxx_light">#B337474F</color>
    <color name="gesture_trail_color_lxx_light">#4DB6AC</color>
    <color name="sliding_key_input_preview_color_lxx_light">#B34DB6AC</color>
    <color name="key_background_lxx_light">#ECEFF1</color>
    <color name="key_background_pressed_lxx_light">#2637474F</color>
    <color name="suggested_word_background_selected_lxx_light">#2637474F</color>
    <color name="gesture_floating_preview_color_lxx_light">#E6ECEFF1</color>
    <color name="emoji_tab_page_indicator_background_lxx_light">#E4E7E9</color>
    <!-- Color resources for LXX_Dark theme.
         10%:0x19 50%:0x80 70%:0xB3 75%:0xC0 80%:0xCC 85%:0xD9 90%:0xE6  -->
    <color name="key_text_color_lxx_dark">#CCFFFFFF</color>
    <color name="key_functional_text_color_lxx_dark">#CCFFFFFF</color>
    <color name="key_text_inactive_color_lxx_dark">#80FFFFFF</color>
    <color name="key_hint_letter_color_lxx_dark">#80FFFFFF</color>
    <color name="language_on_spacebar_text_color_lxx_dark">#B3FFFFFF</color>
    <color name="highlight_color_lxx_dark">#80CBC4</color>
    <color name="auto_correct_color_lxx_dark">#FFFFFF</color>
    <color name="typed_word_color_lxx_dark">#D9FFFFFF</color>
    <color name="suggested_word_color_lxx_dark">#B3FFFFFF</color>
    <color name="gesture_trail_color_lxx_dark">#80CBC4</color>
    <color name="sliding_key_input_preview_color_lxx_dark">#B380CBC4</color>
    <color name="key_background_lxx_dark">#263238</color>
    <color name="key_background_pressed_lxx_dark">#19FFFFFF</color>
    <color name="suggested_word_background_selected_lxx_dark">#19FFFFFF</color>
    <color name="gesture_floating_preview_color_lxx_dark">#E621272B</color>
    <color name="emoji_tab_page_indicator_background_lxx_dark">#21272B</color>
    <!-- Color resources for setup wizard and tutorial -->
    <color name="setup_background">#FFEBEBEB</color>
    <color name="setup_text_dark">#FF707070</color>
    <color name="setup_text_action">@android:color/holo_blue_light</color>
    <color name="setup_step_background">@android:color/background_light</color>
    <color name="setup_welcome_video_margin_color">#FFCCCCCC</color>
    <!-- Accent color for the notification. We need to match this to the OS build -->
    <color name="notification_accent_color">#FF607D8B</color>
</resources>
