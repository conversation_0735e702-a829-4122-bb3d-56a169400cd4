<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted">
            <!-- U+0DDF: "ෟ" SINHALA VOWEL SIGN GAYANUKITTA -->
            <Key latin:keySpec="&#x0DDF;" />
            <!-- U+0DD3: "ී" SINHALA VOWEL SIGN DIGA IS-PILLA -->
            <Key latin:keySpec="&#x0DD3;" />
            <!-- U+0DD8: "ෘ" SINHALA VOWEL SIGN GAETTA-PILLA -->
            <Key latin:keySpec="&#x0DD8;" />
            <!-- U+0DC6: "ෆ" SINHALA LETTER FAYANNA -->
            <Key latin:keySpec="&#x0DC6;" />
            <!-- U+0DA8: "ඨ" SINHALA LETTER MAHAAPRAANA TTAYANNA -->
            <Key latin:keySpec="&#x0DA8;" />
            <!-- U+0DCA/U+200D/U+0DBA: "්‍ය" SINHALA SIGN AL-LAKUNA/ZERO WIDTH JOINER/SINHALA LETTER YAYANNA -->
            <Key latin:keySpec="&#x0DCA;&#x200D;&#x0DBA;" />
            <!-- U+0DC5/U+0DD4: "ළු" SINHALA LETTER MUURDHAJA LAYANNA/SINHALA VOWEL SIGN KETTI PAA-PILLA -->
            <Key latin:keySpec="&#x0DC5;&#x0DD4;" />
            <!-- U+0DAB: "ණ" SINHALA LETTER MUURDHAJA NAYANNA -->
            <Key latin:keySpec="&#x0DAB;" />
            <!-- U+0D9B: "ඛ" SINHALA LETTER MAHAAPRAANA KAYANNA -->
            <Key latin:keySpec="&#x0D9B;" />
            <!-- U+0DAE: "ථ" SINHALA LETTER MAHAAPRAANA TAYANNA -->
            <Key latin:keySpec="&#x0DAE;" />
            <!-- U+0DCA/U+200D/U+0DBB: "්‍ර" SINHALA SIGN AL-LAKUNA/ZERO WIDTH JOINER/SINHALA LETTER RAYANNA -->
            <Key latin:keySpec="&#x0DCA;&#x200D;&#x0DBB;" />
        </case>
        <default>
            <!-- U+0DCA: "්" SINHALA SIGN AL-LAKUNA -->
            <Key latin:keySpec="&#x0DCA;" />
            <!-- U+0DD2: "ි" SINHALA VOWEL SIGN KETTI IS-PILLA -->
            <Key latin:keySpec="&#x0DD2;" />
            <!-- U+0DCF: "ා" SINHALA VOWEL SIGN AELA-PILLA -->
            <Key latin:keySpec="&#x0DCF;" />
            <!-- U+0DD9: "ෙ" SINHALA VOWEL SIGN KOMBUVA -->
            <Key latin:keySpec="&#x0DD9;" />
            <!-- U+0DA7: "ට" SINHALA LETTER ALPAPRAANA TTAYANNA -->
            <Key latin:keySpec="&#x0DA7;" />
            <!-- U+0DBA: "ය" SINHALA LETTER YAYANNA -->
            <Key latin:keySpec="&#x0DBA;" />
            <!-- U+0DC0: "ව" SINHALA LETTER VAYANNA -->
            <Key latin:keySpec="&#x0DC0;" />
            <!-- U+0DB1: "න" SINHALA LETTER DANTAJA NAYANNA -->
            <Key latin:keySpec="&#x0DB1;" />
            <!-- U+0D9A: "ක" SINHALA LETTER ALPAPRAANA KAYANNA -->
            <Key latin:keySpec="&#x0D9A;" />
            <!-- U+0DAD: "ත" SINHALA LETTER ALPAPRAANA TAYANNA -->
            <Key latin:keySpec="&#x0DAD;" />
            <!-- U+0D8F: "ඏ" SINHALA LETTER ILUYANNA -->
            <Key latin:keySpec="&#x0D8F;" />
        </default>
    </switch>
</merge>
