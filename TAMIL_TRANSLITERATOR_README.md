# Tamil Transliteration Engine

A standalone Java implementation of Tamil transliteration logic based on the Indic Keyboard project. This allows you to convert English phonetic input to Tamil script.

## Files

- `TamilTransliterator.java` - Core transliteration engine
- `TamilTransliteratorTest.java` - Test interface with interactive mode
- `run_tamil_test.bat` - Windows run script
- `run_tamil_test.sh` - Linux/Mac run script

## How to Run

### Windows
```bash
run_tamil_test.bat
```

### Linux/Mac
```bash
chmod +x run_tamil_test.sh
./run_tamil_test.sh
```

### Manual Compilation
```bash
javac TamilTransliterator.java TamilTransliteratorTest.java
java TamilTransliteratorTest
```

## Features

### 1. Real-time Transliteration
Watch how words are converted character by character as you type:
```
Typed: v               -> Tamil: வ
Typed: va              -> Tamil: வ
Typed: van             -> Tamil: வன்
Typed: vana            -> Tamil: வன
Typed: vanak           -> Tamil: வனக்
Typed: vanakk          -> Tamil: வனக்க்
Typed: vanakka         -> Tamil: வனக்க
Typed: vanakkam        -> Tamil: வணக்கம்
```

### 2. Interactive Mode
- Type any English word to see Tamil output
- Use `realtime <word>` to see character-by-character conversion
- Use `batch` to test common Tamil words
- Type `help` for commands

### 3. Supported Conversions

#### Basic Vowels
- a → அ, aa → ஆ, i → இ, ii → ஈ
- u → உ, uu → ஊ, e → எ, ee → ஏ
- ai → ஐ, o → ஒ, oo → ஓ, au → ஔ

#### Consonants
- k → க, ng → ங, ch → ச, nj → ஞ
- t → ட, n → ந, th → த, p → ப
- m → ம, y → ய, r → ர, l → ல
- v → வ, zh → ழ, ll → ள, rr → ற

#### Common Words
- vanakkam → வணக்கம் (Hello)
- nandri → நன்றி (Thank you)
- tamil → தமிழ் (Tamil)
- amma → அம்மா (Mother)
- appa → அப்பா (Father)

## How It Works

The transliteration engine mimics the logic from the Indic Keyboard:

1. **Progressive Matching**: Tries to find the longest possible match from current position
2. **Context Awareness**: Considers previous characters for better accuracy
3. **Vowel Combination**: Automatically combines consonants with vowel signs
4. **Real-time Processing**: Updates output as each character is typed

## Example Usage

```java
TamilTransliterator transliterator = new TamilTransliterator();

// Simple transliteration
String result = transliterator.transliterate("vanakkam", "", false);
System.out.println(result); // Output: வணக்கம்

// Character by character (simulating real-time typing)
String word = "nandri";
for (int i = 1; i <= word.length(); i++) {
    String partial = word.substring(0, i);
    String tamil = transliterator.transliterate(partial, "", false);
    System.out.println(partial + " -> " + tamil);
}
```

## Test Words to Try

### Greetings
- vanakkam (Hello)
- nandri (Thank you)
- mannikkavum (Sorry)

### Family
- amma (Mother)
- appa (Father)
- anna (Elder brother)
- akka (Elder sister)

### Common Phrases
- naan (I)
- neenga (You)
- enna (What)
- eppo (When)
- enga (Where)
- sari (Okay)
- illa (No)

### Simple Syllables
- ka, ki, ku, ke, ko
- ma, mi, mu, me, mo
- na, ni, nu, ne, no

## Limitations

This is a simplified version of the full Indic Keyboard transliteration engine. The original uses the SMC (Swathanthra Malayalam Computing) library which has more comprehensive rules and mappings.

## Based On

This implementation is based on the transliteration logic from:
- **Indic Keyboard** project
- **SMC IME** (Swathanthra Malayalam Computing Input Method Engine)
- Tamil Unicode standard mappings

The core algorithm follows the same pattern as the original `WordComposer.applyTransliteration()` method from the Indic Keyboard project.
