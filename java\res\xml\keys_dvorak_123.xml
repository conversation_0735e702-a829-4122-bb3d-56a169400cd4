<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case
            latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted"
        >
            <Key
                latin:keySpec="&quot;"
                latin:keyHintLabel="1"
                latin:additionalMoreKeys="1" />
        </case>
        <case
            latin:mode="url"
        >
            <Key
                latin:keySpec="/"
                latin:keyHintLabel="1"
                latin:additionalMoreKeys="1" />
        </case>
        <case
            latin:mode="email"
        >
            <Key
                latin:keySpec="\@"
                latin:keyHintLabel="1"
                latin:additionalMoreKeys="1" />
        </case>
        <default>
            <Key
                latin:keySpec="\'"
                latin:keyHintLabel="1"
                latin:additionalMoreKeys="1"
                latin:moreKeys="!,&quot;" />
        </default>
    </switch>
    <switch>
        <case
            latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted"
        >
            <Key
                latin:keySpec="&lt;"
                latin:keyHintLabel="2"
                latin:additionalMoreKeys="2" />
            <Key
                latin:keySpec="&gt;"
                latin:keyHintLabel="3"
                latin:additionalMoreKeys="3" />
        </case>
        <default>
            <Key
                latin:keySpec=","
                latin:keyHintLabel="2"
                latin:additionalMoreKeys="2"
                latin:moreKeys="\?,&lt;" />
            <Key
                latin:keySpec="."
                latin:keyHintLabel="3"
                latin:additionalMoreKeys="3"
                latin:moreKeys="&gt;" />
        </default>
    </switch>
</merge>
