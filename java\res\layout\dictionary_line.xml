<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<!-- TODO: Remove paddingRight, layout_marginLeft, layout_marginRight for API version 17+ -->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:minHeight="?android:attr/listPreferredItemHeight"
    android:gravity="center_vertical"
    android:paddingRight="?android:attr/scrollbarSize"
    android:paddingEnd="?android:attr/scrollbarSize"
    android:background="?android:attr/selectableItemBackground"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="15dip"
    android:layout_marginStart="15dip"
    android:layout_marginRight="6dip"
    android:layout_marginEnd="6dip"
    android:layout_marginTop="6dip"
    android:layout_marginBottom="6dip"
    android:layout_weight="1">

  <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_weight="1"
      android:orientation="vertical">

    <TextView
        android:id="@android:id/title"
        android:layout_marginLeft="5dip"
        android:layout_marginStart="5dip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:ellipsize="marquee"
        android:fadingEdge="horizontal" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dip"
        android:layout_marginLeft="5dip">

      <TextView
          android:id="@android:id/summary"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:textAppearance="?android:attr/textAppearanceSmall"
          android:textColor="?android:attr/textColorSecondary"
          android:maxLines="1" />

      <in.androidtweak.inputmethod.dictionarypack.DictionaryDownloadProgressBar
          android:id="@+id/dictionary_line_progress_bar"
          style="@android:style/Widget.Holo.ProgressBar.Horizontal"
          android:layout_width="match_parent"
          android:layout_height="match_parent"
          android:gravity="center" />

    </FrameLayout>
  </LinearLayout>

  <in.androidtweak.inputmethod.dictionarypack.ButtonSwitcher
      android:id="@+id/wordlist_button_switcher"
      android:layout_weight="0"
      android:layout_marginStart="13dip"
      android:layout_marginLeft="13dip"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content">
    <Button
        android:id="@+id/dict_install_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right|center_vertical"
        android:singleLine="true"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:text="@string/install_dict" />
    <Button
        android:id="@+id/dict_cancel_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right|center_vertical"
        android:singleLine="true"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:text="@string/cancel_download_dict" />
    <Button
        android:id="@+id/dict_delete_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right|center_vertical"
        android:singleLine="true"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:text="@string/delete_dict" />
  </in.androidtweak.inputmethod.dictionarypack.ButtonSwitcher>
</LinearLayout>
