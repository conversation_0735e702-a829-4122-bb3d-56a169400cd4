<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dip">
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="0dp"
            android:layout_weight="30"
            android:layout_height="wrap_content"
            android:layout_gravity="start|center_vertical"
            android:gravity="start|left"
            android:textAlignment="viewStart"
            style="?android:attr/textAppearanceSmall"
            android:text="@string/subtype_locale" />
        <Spinner
            android:id="@+id/subtype_locale_spinner"
            android:spinnerMode="dialog"
            android:layout_width="0dp"
            android:layout_weight="70"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dip"
            android:layout_marginBottom="8dip"
            android:layout_marginTop="8dip"
            android:layout_gravity="fill_horizontal|center_vertical"
            android:gravity="start|left"
            android:prompt="@string/subtype_locale" />
        </LinearLayout>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="0dp"
            android:layout_weight="30"
            android:layout_height="wrap_content"
            android:layout_gravity="start|center_vertical"
            android:textAlignment="viewStart"
            style="?android:attr/textAppearanceSmall"
            android:text="@string/keyboard_layout_set" />
        <Spinner
            android:id="@+id/keyboard_layout_set_spinner"
            android:spinnerMode="dialog"
            android:layout_width="0dp"
            android:layout_weight="70"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dip"
            android:layout_marginBottom="8dip"
            android:layout_marginTop="8dip"
            android:layout_gravity="fill_horizontal|center_vertical"
            android:gravity="start|left"
            android:prompt="@string/keyboard_layout_set" />
    </LinearLayout>
</LinearLayout>
