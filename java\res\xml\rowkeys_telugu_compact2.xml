<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <!-- U+0C4B: "ో" TELUGU VOWEL SIGN OO
         U+0C13: "ఓ" TELUGU LETTER OO -->
    <Key
        latin:keySpec="&#x0C4B;"
        latin:moreKeys="&#x0C13;" />
    <!-- U+0C47: "ే" TELUGU VOWEL SIGN EE
         U+0C0F: "ఏ" TELUGU LETTER EE -->
    <Key
        latin:keySpec="&#x0C47;"
        latin:moreKeys="&#x0C0F;" />
    <!-- U+0C4D: "్" TELUGU SIGN VIRAMA
         U+0C05: "అ" TELUGU LETTER A -->
    <Key
        latin:keySpec="&#x0C4D;"
        latin:moreKeys="&#x0C05;" />
    <!-- U+0C3F: "ి" TELUGU VOWEL SIGN I
         U+0C07: "ఇ" TELUGU LETTER I -->
    <Key
        latin:keySpec="&#x0C3F;"
        latin:moreKeys="&#x0C07;" />
    <!-- U+0C41: "ు" TELUGU VOWEL SIGN U
         U+0C09: "ఉ" TELUGU LETTER U -->
    <Key
        latin:keySpec="&#x0C41;"
        latin:moreKeys="&#x0C09;" />
    <!-- U+0C2A: "ప" TELUGU LETTER PA
         U+0C2B: "ఫ" TELUGU LETTER PHA -->
    <Key
        latin:keySpec="&#x0C2A;"
        latin:moreKeys="&#x0C2B;" />
    <!-- U+0C30: "ర" TELUGU LETTER RA
         U+0C31: "ఱ" TELUGU LETTER RRA
         U+0C43: "ృ" TELUGU VOWEL SIGN VOCALIC R -->
    <Key
        latin:keySpec="&#x0C30;"
        latin:moreKeys="&#x0C31;,&#x0C43;" />
    <!-- U+0C15: "క" TELUGU LETTER KA
         U+0C16: "ఖ" TELUGU LETTER KHA -->
    <Key
        latin:keySpec="&#x0C15;"
        latin:moreKeys="&#x0C16;" />
    <!-- U+0C24: "త" TELUGU LETTER TA
         U+0C25: "థ" TELUGU LETTER THA -->
    <Key
        latin:keySpec="&#x0C24;"
        latin:moreKeys="&#x0C25;" />
    <!-- U+0C1A: "చ" TELUGU LETTER CA
         U+0C1B: "ఛ" TELUGU LETTER CHA -->
    <Key
        latin:keySpec="&#x0C1A;"
        latin:moreKeys="&#x0C1B;" />
    <!-- U+0C1F: "ట" TELUGU LETTER TTA
         U+0C20: "ఠ" TELUGU LETTER TTHA -->
    <Key
        latin:keySpec="&#x0C1F;"
        latin:moreKeys="&#x0C20;" />
</merge>
