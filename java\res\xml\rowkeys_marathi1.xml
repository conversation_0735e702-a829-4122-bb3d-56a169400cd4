<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_au" />
    <!-- U+0967: "१" DEVANAGARI DIGIT ONE -->
    <Key
        latin:keyStyle="baseKeyDevanagariVowelSignAu"
        latin:keyHintLabel="1"
        latin:additionalMoreKeys="&#x0967;,1" />
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_ai" />
    <!-- U+0968: "२" DEVANAGARI DIGIT TWO -->
    <Key
        latin:keyStyle="baseKeyDevanagariVowelSignAi"
        latin:keyHintLabel="2"
        latin:additionalMoreKeys="&#x0968;,2" />
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_aa" />
    <!-- U+0969: "३" DEVANAGARI DIGIT THREE -->
    <Key
        latin:keyStyle="baseKeyDevanagariVowelSignAa"
        latin:keyHintLabel="3"
        latin:additionalMoreKeys="&#x0969;,3" />
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_ii" />
    <!-- U+096A: "४" DEVANAGARI DIGIT FOUR -->
    <Key
        latin:keyStyle="baseKeyDevanagariVowelSignIi"
        latin:keyHintLabel="4"
        latin:additionalMoreKeys="&#x096A;,4" />
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_uu" />
    <!-- U+096B: "५" DEVANAGARI DIGIT FIVE -->
    <Key
        latin:keyStyle="baseKeyDevanagariVowelSignUu"
        latin:keyHintLabel="5"
        latin:additionalMoreKeys="&#x096B;,5" />
    <!-- U+092C: "ब" DEVANAGARI LETTER BA
         U+092D: "भ" DEVANAGARI LETTER BHA
         U+096C: "६" DEVANAGARI DIGIT SIX -->
    <Key
        latin:keySpec="&#x092C;"
        latin:moreKeys="&#x092D;,%"
        latin:keyHintLabel="6"
        latin:additionalMoreKeys="&#x096C;,6" />
    <!-- U+0939: "ह" DEVANAGARI LETTER HA
         U+096D: "७" DEVANAGARI DIGIT SEVEN -->
    <Key
        latin:keySpec="&#x0939;"
        latin:keyHintLabel="7"
        latin:additionalMoreKeys="&#x096D;,7" />
    <!-- U+0917: "ग" DEVANAGARI LETTER GA
         U+0918: "घ" DEVANAGARI LETTER GHA
         U+096E: "८" DEVANAGARI DIGIT EIGHT -->
    <Key
        latin:keySpec="&#x0917;"
        latin:moreKeys="&#x0918;,%"
        latin:keyHintLabel="8"
        latin:additionalMoreKeys="&#x096E;,8" />
    <!-- U+0926: "द" DEVANAGARI LETTER DA
         U+0927: "ध" DEVANAGARI LETTER DHA
         U+096F: "९" DEVANAGARI DIGIT NINE -->
    <Key
        latin:keySpec="&#x0926;"
        latin:moreKeys="&#x0927;,%"
        latin:keyHintLabel="9"
        latin:additionalMoreKeys="&#x096F;,9" />
    <!-- U+091C: "ज" DEVANAGARI LETTER JA
         U+091D: "झ" DEVANAGARI LETTER JHA
         U+091C/U+094D/U+091E: "ज्ञ" DEVANAGARI LETTER JA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER JHA -->
    <Key
        latin:keySpec="&#x091C;"
        latin:moreKeys="&#x091D;,&#x091C;&#x094D;&#x091E;,%"
        latin:keyHintLabel="0"
        latin:additionalMoreKeys="&#x0966;,0" />
    <!-- U+0921: "ड" DEVANAGARI LETTER DDA
         U+0922: "ढ" DEVANAGARI LETTER DDHA -->
    <Key
        latin:keySpec="&#x0921;"
        latin:moreKeys="&#x0922;" />
</merge>
