<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Label for "switch to alphabetic" key.
         U+0915: "क" DEVANAGARI LETTER KA
         U+0916: "ख" DEVANAGARI LETTER KHA
         U+0917: "ग" DEVANAGARI LETTER GA -->
    <string name="keylabel_to_alpha">&#x0915;&#x0916;&#x0917;</string>
    <!-- U+0967: "१" DEVANAGARI DIGIT ONE -->
    <string name="keyspec_symbols_1">&#x0967;</string>
    <!-- U+0968: "२" DEVANAGARI DIGIT TWO -->
    <string name="keyspec_symbols_2">&#x0968;</string>
    <!-- U+0969: "३" DEVANAGARI DIGIT THREE -->
    <string name="keyspec_symbols_3">&#x0969;</string>
    <!-- U+096A: "४" DEVANAGARI DIGIT FOUR -->
    <string name="keyspec_symbols_4">&#x096A;</string>
    <!-- U+096B: "५" DEVANAGARI DIGIT FIVE -->
    <string name="keyspec_symbols_5">&#x096B;</string>
    <!-- U+096C: "६" DEVANAGARI DIGIT SIX -->
    <string name="keyspec_symbols_6">&#x096C;</string>
    <!-- U+096D: "७" DEVANAGARI DIGIT SEVEN -->
    <string name="keyspec_symbols_7">&#x096D;</string>
    <!-- U+096E: "८" DEVANAGARI DIGIT EIGHT -->
    <string name="keyspec_symbols_8">&#x096E;</string>
    <!-- U+096F: "९" DEVANAGARI DIGIT NINE -->
    <string name="keyspec_symbols_9">&#x096F;</string>
    <!-- U+0966: "०" DEVANAGARI DIGIT ZERO -->
    <string name="keyspec_symbols_0">&#x0966;</string>
    <!-- Label for "switch to symbols" key. -->
    <string name="keylabel_to_symbol">?&#x0967;&#x0968;&#x0969;</string>
    <string name="additional_morekeys_symbols_1">1</string>
    <string name="additional_morekeys_symbols_2">2</string>
    <string name="additional_morekeys_symbols_3">3</string>
    <string name="additional_morekeys_symbols_4">4</string>
    <string name="additional_morekeys_symbols_5">5</string>
    <string name="additional_morekeys_symbols_6">6</string>
    <string name="additional_morekeys_symbols_7">7</string>
    <string name="additional_morekeys_symbols_8">8</string>
    <string name="additional_morekeys_symbols_9">9</string>
    <string name="additional_morekeys_symbols_0">0</string>
    <!-- U+20B9: "₹" INDIAN RUPEE SIGN -->
    <string name="keyspec_currency">&#x20B9;</string>
</resources>
