<?xml version="1.0"?>
<inputmethod id="ml-swanalekha" name="സ്വനലേഖ" description="Malayalam Transliteration based input method" author="Junaid P V | Anoop P" version="1.0" contextLength="2" maxKeyLength="4">
  <pattern input="(‌)+a" replacement="അ"/>
  <pattern input="(‌)+A" replacement="ആ"/>
  <pattern input="(‌)+i" replacement="ഇ"/>
  <pattern input="(‌)+I" replacement="ഈ"/>
  <pattern input="(‌)+u" replacement="ഉ"/>
  <pattern input="(‌)+U" replacement="ഊ"/>
  <pattern input="(‌)+e" replacement="എ"/>
  <pattern input="(‌)+E" replacement="ഏ"/>
  <pattern input="(‌)+o" replacement="ഒ"/>
  <pattern input="(‌)+O" replacement="ഓ"/>
  <pattern input="(‌)+H" replacement="ഃ"/>
  <pattern input="ന്ൿh" context="nc" replacement="ഞ്ച്"/>
  <pattern input="ൿh" context="c" replacement="ച്"/>
  <pattern input="ക്ക്h" context="cc" replacement="ച്ച്"/>
  <pattern input="ല്പ്p" context="lp" replacement="ൽപ്പ്"/>
  <pattern input="\\([A-Za-z\&gt;_~])" context="\\" replacement="$1"/>
  <pattern input="([ക-ഹ])്R" replacement="$1ൃ"/>
  <pattern input="ന്ന്j" replacement="ഞ്ഞ്"/>
  <pattern input="ന്ന്g" replacement="ങ്"/>
  <pattern input="ട്h" replacement="ത്"/>
  <pattern input="ട്t" replacement="ട്ട്"/>
  <pattern input="ഠ്T" replacement="റ്റ്"/>
  <pattern input="ന്റ്h" replacement="ന്ത്"/>
  <pattern input="([ക-ഹ])്ല്l" context="L" replacement="$1ൢ"/>
  <pattern input="([ക-ഹ])ൢl" replacement="$1ൣ"/>
  <pattern input="([അ-ഊഌഎ-ഐഒ-ഹ])([ഽ-ൂെ-ൌൗ])+R" replacement="$1$2റ്"/>
  <pattern input="([ക-ഹ])ൃa" replacement="$1്ര"/>
  <pattern input="([ക-ഹ])ൃA" replacement="$1്രാ"/>
  <pattern input="([ക-ഹ])ൃi" replacement="$1്രി"/>
  <pattern input="([ക-ഹ])ൃI" replacement="$1്രീ"/>
  <pattern input="([ക-ഹ])ൃu" replacement="$1്രു"/>
  <pattern input="([ക-ഹ])ൃU" replacement="$1്രൂ"/>
  <pattern input="([ക-ഹ])ൃe" replacement="$1്രെ"/>
  <pattern input="([ക-ഹ])ൃE" replacement="$1്രേ"/>
  <pattern input="([ക-ഹ])ൃo" replacement="$1്രൊ"/>
  <pattern input="([ക-ഹ])ൃO" replacement="$1്രോ"/>
  <pattern input="([ക-ഹ])്l" replacement="$1്ല്"/>
  <pattern input="([ക-ഹ])്L" replacement="$1്ള്"/>
  <pattern input="അa" replacement="ആ"/>
  <pattern input="അi" replacement="ഐ"/>
  <pattern input="അu" replacement="ഔ"/>
  <pattern input="ഇi" replacement="ഈ"/>
  <pattern input="ഉu" replacement="ഊ"/>
  <pattern input="ഊo" replacement="ഊഊ"/>
  <pattern input="റ്R" replacement="ഋ"/>
  <pattern input="റ്a" replacement="റ"/>
  <pattern input="റ്A" replacement="റാ"/>
  <pattern input="റ്i" replacement="റി"/>
  <pattern input="റ്I" replacement="റീ"/>
  <pattern input="റ്u" replacement="റു"/>
  <pattern input="റ്U" replacement="റൂ"/>
  <pattern input="റ്e" replacement="റെ"/>
  <pattern input="റ്E" replacement="റേ"/>
  <pattern input="റ്o" replacement="റൊ"/>
  <pattern input="റ്O" replacement="റോ"/>
  <pattern input="ഌl" replacement="ൡ"/>
  <pattern input="എe" replacement="ഈ"/>
  <pattern input="ഒo" replacement="ഊ"/>
  <pattern input="ാa" replacement="ാാ"/>
  <pattern input="ീi" replacement="ീീ"/>
  <pattern input="ൂu" replacement="ൂൂ"/>
  <pattern input="ൂo" replacement="ൂൂ"/>
  <pattern input="ൄR" replacement="ൄൄ"/>
  <pattern input="േE" replacement="േേ"/>
  <pattern input="ോO" replacement="ോോ"/>
  <pattern input="ൗu" replacement="ൗൗ"/>
  <pattern input="ൢ\^" replacement="ൣ"/>
  <pattern input="ൣ\^" replacement="ൣൣ"/>
  <pattern input="ക്h" replacement="ഖ്"/>
  <pattern input="ഗ്h" replacement="ഘ്"/>
  <pattern input="ന്g" replacement="ങ്ങ്"/>
  <pattern input="ച്h" replacement="ഛ്"/>
  <pattern input="ജ്h" replacement="ഝ്"/>
  <pattern input="ട്h" replacement="ഠ്"/>
  <pattern input="ഡ്h" replacement="ഢ്"/>
  <pattern input="ത്h" replacement="ഥ്"/>
  <pattern input="ഠ്h" replacement="ഥ്"/>
  <pattern input="ഠ്H" replacement="ഥ്"/>
  <pattern input="ദ്h" replacement="ധ്"/>
  <pattern input="പ്h" replacement="ഫ്"/>
  <pattern input="ബ്h" replacement="ഭ്"/>
  <pattern input="മ്a" replacement="മ"/>
  <pattern input="മ്A" replacement="മാ"/>
  <pattern input="മ്i" replacement="മി"/>
  <pattern input="മ്I" replacement="മീ"/>
  <pattern input="മ്u" replacement="മു"/>
  <pattern input="മ്U" replacement="മൂ"/>
  <pattern input="മ്R" replacement="മൃ"/>
  <pattern input="മ്e" replacement="മെ"/>
  <pattern input="മ്E" replacement="മേ"/>
  <pattern input="മ്o" replacement="മൊ"/>
  <pattern input="മ്O" replacement="മോ"/>
  <pattern input="മ്m" replacement="മ്മ്"/>
  <pattern input="([^സ])മ്r" replacement="$1മ്ര്"/>
  <pattern input="മ്p" replacement="മ്പ്"/>
  <pattern input="([^സ])മ്(l|L)" replacement="$1മ്ല്"/>
  <pattern input="മ്~" replacement="ം"/>
  <pattern input="([^സ])മ്n" replacement="$1മ്ന്"/>
  <pattern input="([^സ])മ്y" replacement="$1മ്യ്"/>
  <pattern input="സ്h" replacement="ഷ്"/>
  <pattern input="ശ്h" replacement="ഴ്"/>
  <pattern input="ൺ\\\\" replacement="ണ്‍"/>
  <pattern input="ണ്~" replacement="ൺ"/>
  <pattern input="ണ്R" replacement="ണൃ"/>
  <pattern input="ണ്N" replacement="ണ്ണ്"/>
  <pattern input="ണ്t" replacement="ണ്ട്"/>
  <pattern input="ണ്D" replacement="ണ്ഡ്"/>
` <pattern input="ണ്y" replacement="ണ്യ്"/>
  <pattern input="ണ്v" replacement="ണ്വ്"/>
  <pattern input="ണ്m" replacement="ണ്മ്"/>
  <pattern input="ൻ\\\\" replacement="ന്‍"/>
  <pattern input="ന്~" replacement="ൻ"/>
  <pattern input="ന്r" replacement="ന്ര്"/>
  <pattern input="ന്R" replacement="നൃ"/>
  <pattern input="ന്k" replacement="ങ്ക്"/>
  <pattern input="ന്j" replacement="ഞ്"/>
  <pattern input="ന്n" replacement="ന്ന്"/>
  <pattern input="ന്d" replacement="ന്ദ്"/>
  <pattern input="ന്t" replacement="ന്റ്"/>
  <pattern input="ന്m" replacement="ന്മ്"/>
  <pattern input="ന്L" replacement="ന്ല്"/>
  <pattern input="ന്v" replacement="ന്വ്"/>
  <pattern input="ന്y" replacement="ന്യ്"/>
  <pattern input="ർ\\\\" replacement="ര്‍"/>
  <pattern input="ര്~" replacement="ർ"/>
  <pattern input="ര്r" replacement="ഋ"/>
  <pattern input="ര്R" replacement="രൃ"/>
  <pattern input="ര്y" replacement="ര്യ്"/>
  <pattern input="ൽ\\\\" replacement="ല്‍"/>
  <pattern input="ല്~" replacement="ൽ"/>
  <pattern input="ല്R" replacement="ലൃ"/>
  <pattern input="ല്(l|L)" replacement="ല്ല്"/>
  <pattern input="ല്p" replacement="ല്പ്"/>
  <pattern input="ല്y" replacement="ല്യ്"/>
  <pattern input="ല്v" replacement="ല്വ്"/>
  <pattern input="ൾ\\\\" replacement="ള്‍"/>
  <pattern input="ള്~" replacement="ൾ"/>
  <pattern input="ള്R" replacement="ളൃ"/>
  <pattern input="ള്L" replacement="ള്ള്"/>
  <pattern input="ള്y" replacement="ള്യ്"/>
  <pattern input="ള്l" replacement="ഌ"/>
  <pattern input="ൿ\\\\" replacement="ക്‍"/>
  <pattern input="ൿR" replacement="കൃ"/>
  <pattern input="ൿy" replacement="ക്യ്"/>
  <pattern input="ൿ(l|L)" replacement="ക്ല്"/>
  <pattern input="ൿv" replacement="ക്വ്"/>
  <pattern input="ൿ(k|c)" replacement="ക്ക്"/>
  <pattern input="([ക-ഹ])a" replacement="$1ാ"/>
  <pattern input="([ക-ഹ])്a" replacement="$1"/>
  <pattern input="ണ്a" replacement="ണ"/>
  <pattern input="ന്a" replacement="ന"/>
  <pattern input="ര്a" replacement="ര"/>
  <pattern input="ല്a" replacement="ല"/>
  <pattern input="ള്a" replacement="ള"/>
  <pattern input="ൿa" replacement="ക"/>
  <pattern input="([ക-ഹ])്A" replacement="$1ാ"/>
  <pattern input="ണ്A" replacement="ണാ"/>
  <pattern input="ന്A" replacement="നാ"/>
  <pattern input="ര്A" replacement="രാ"/>
  <pattern input="ല്A" replacement="ലാ"/>
  <pattern input="ള്A" replacement="ളാ"/>
  <pattern input="ൿA" replacement="കാ"/>
  <pattern input="([ക-ഹ])്i" replacement="$1ി"/>
  <pattern input="ണ്i" replacement="ണി"/>
  <pattern input="ന്i" replacement="നി"/>
  <pattern input="ര്i" replacement="രി"/>
  <pattern input="ല്i" replacement="ലി"/>
  <pattern input="ള്i" replacement="ളി"/>
  <pattern input="ൿi" replacement="കി"/>
  <pattern input="([ക-ഹ])്I" replacement="$1ീ"/>
  <pattern input="ണ്I" replacement="ണീ"/>
  <pattern input="ന്I" replacement="നീ"/>
  <pattern input="ര്I" replacement="രീ"/>
  <pattern input="ല്I" replacement="ലീ"/>
  <pattern input="ള്I" replacement="ളീ"/>
  <pattern input="ൿI" replacement="കീ"/>
  <pattern input="([ക-ഹ])ിi" replacement="$1ീ"/>
  <pattern input="([ക-ഹ])െe" replacement="$1ീ"/>
  <pattern input="([ക-ഹ])്u" replacement="$1ു"/>
  <pattern input="ണ്u" replacement="ണു"/>
  <pattern input="ന്u" replacement="നു"/>
  <pattern input="ര്u" replacement="രു"/>
  <pattern input="ല്u" replacement="ലു"/>
  <pattern input="ള്u" replacement="ളു"/>
  <pattern input="ൿu" replacement="കു"/>
  <pattern input="([ക-ഹ])്U" replacement="$1ൂ"/>
  <pattern input="([ക-ഹ])ുu" replacement="$1ൂ"/>
  <pattern input="ണ്U" replacement="ണൂ"/>
  <pattern input="ന്U" replacement="നൂ"/>
  <pattern input="ര്U" replacement="രൂ"/>
  <pattern input="ല്U" replacement="ലൂ"/>
  <pattern input="ള്U" replacement="ളൂ"/>
  <pattern input="ൿU" replacement="കൂ"/>
  <pattern input="([ക-ഹ])ൊo" replacement="$1ൂ"/>
  <pattern input="([ക-ഹ])ൃR" replacement="$1ൄ"/>
  <pattern input="([ക-ഹ])്e" replacement="$1െ"/>
  <pattern input="ണ്e" replacement="ണെ"/>
  <pattern input="ന്e" replacement="നെ"/>
  <pattern input="ര്e" replacement="രെ"/>
  <pattern input="ല്e" replacement="ലെ"/>
  <pattern input="ള്e" replacement="ളെ"/>
  <pattern input="ൿe" replacement="കെ"/>
  <pattern input="([ക-ഹ])്E" replacement="$1േ"/>
  <pattern input="ണ്E" replacement="ണേ"/>
  <pattern input="ന്E" replacement="നേ"/>
  <pattern input="ര്E" replacement="രേ"/>
  <pattern input="ല്E" replacement="ലേ"/>
  <pattern input="ള്E" replacement="ളേ"/>
  <pattern input="ൿE" replacement="കേ"/>
  <pattern input="([ക-ഹ])i" replacement="$1ൈ"/>
  <pattern input="([ക-ഹ])്o" replacement="$1ൊ"/>
  <pattern input="ണ്o" replacement="ണൊ"/>
  <pattern input="ന്o" replacement="നൊ"/>
  <pattern input="ര്o" replacement="രൊ"/>
  <pattern input="ല്o" replacement="ലൊ"/>
  <pattern input="ള്o" replacement="ളൊ"/>
  <pattern input="ൿo" replacement="കൊ"/>
  <pattern input="([ക-ഹ])്O" replacement="$1ോ"/>
  <pattern input="ണ്O" replacement="ണോ"/>
  <pattern input="ന്O" replacement="നോ"/>
  <pattern input="ര്O" replacement="രോ"/>
  <pattern input="ല്O" replacement="ലോ"/>
  <pattern input="ള്O" replacement="ളോ"/>
  <pattern input="ൿO" replacement="കോ"/>
  <pattern input="([ക-ഹ])u" replacement="$1ൗ"/>
  <pattern input="([ക-ഹ])U" replacement="$1ൌ"/>
  <pattern input="([അ-്])m" replacement="$1മ്"/>
  <pattern input="@0" replacement="൦"/>
  <pattern input="@1" replacement="൧"/>
  <pattern input="@2" replacement="൨"/>
  <pattern input="@3" replacement="൩"/>
  <pattern input="@4" replacement="൪"/>
  <pattern input="@5" replacement="൫"/>
  <pattern input="@6" replacement="൬"/>
  <pattern input="@7" replacement="൭"/>
  <pattern input="@8" replacement="൮"/>
  <pattern input="@9" replacement="൯"/>
  <pattern input="മ്\\" replacement="ം"/>
  <pattern input="ആ\\" replacement="ാ"/>
  <pattern input="ഇ\\" replacement="ി"/>
  <pattern input="ഈ\\" replacement="ീ"/>
  <pattern input="ഉ\\" replacement="ു"/>
  <pattern input="ഊ\\" replacement="ൂ"/>
  <pattern input="ഋ\\" replacement="ൃ"/>
  <pattern input="ൠ\\" replacement="ൄ"/>
  <pattern input="എ\\" replacement="െ"/>
  <pattern input="ഏ\\" replacement="േ"/>
  <pattern input="ഐ\\" replacement="ൈ"/>
  <pattern input="ഒ\\" replacement="ൊ"/>
  <pattern input="ഓ\\" replacement="ോ"/>
  <pattern input="ഔ\\" replacement="ൗ"/>
  <pattern input="ൗ\\" replacement="ൌ"/>
  <pattern input="ഌ\\" replacement="ൢ"/>
  <pattern input="ൡ\\" replacement="ൣ"/>

  <pattern input="@u" replacement="ു"/>
  <pattern input="@U" replacement="ൂ"/>

  <pattern input="ക്~" replacement="ൿ"/>
  <pattern input="മ്~" replacement="ൔ"/>
  <pattern input="യ്[~\_]" replacement="ൕ"/>
  <pattern input="ഴ്~" replacement="ൖ"/>

  <pattern input="(([ൺ-ൿം])‌+)?A" replacement="$2ആ"/>
  <pattern input="(([ൺ-ൿം])‌+)?B" replacement="$2ബ്ബ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?C" replacement="$2ച്ച്"/>
  <pattern input="(([ൺ-ൿം])‌+)?D" replacement="$2ഡ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?E" replacement="$2ഏ"/>
  <pattern input="(([ൺ-ൿം])‌+)?F" replacement="$2ഫ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?G" replacement="$2ഘ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?H" replacement="$2ഃ"/>
  <pattern input="(([ൺ-ൿം])‌+)?I" replacement="$2ഈ"/>
  <pattern input="(([ൺ-ൿം])‌+)?J" replacement="$2ജ്ജ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?K" replacement="$2ഖ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?L" replacement="$2ള്"/>
  <pattern input="(([ൺ-ൿം])‌+)?M" replacement="$2മ്മ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?N" replacement="$2ണ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?O" replacement="$2ഓ"/>
  <pattern input="(([ൺ-ൿം])‌+)?P" replacement="$2പ്പ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?Q" replacement="$2ക്യ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?(S|z)" replacement="$2ശ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?T" replacement="$2ഠ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?U" replacement="$2ഊ"/>
  <pattern input="(([ൺ-ൿം])‌+)?(V|W)" replacement="$2വ്വ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?X" replacement="$2ക്ഷ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?Y" replacement="$2യ്യ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?Z" replacement="$2ശ്ശ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?a" replacement="$2അ"/>
  <pattern input="(([ൺ-ൿം])‌+)?b" replacement="$2ബ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?c" replacement="$2ൿ"/>
  <pattern input="(([ൺ-ൿം])‌+)?d" replacement="$2ദ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?e" replacement="$2എ"/>
  <pattern input="(([ൺ-ൿം])‌+)?f" replacement="$2ഫ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?g" replacement="$2ഗ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?h" replacement="$2ഹ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?i" replacement="$2ഇ"/>
  <pattern input="(([ൺ-ൿം])‌+)?j" replacement="$2ജ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?k" replacement="$2ക്"/>
  <pattern input="(([ൺ-ൿം])‌+)?l" replacement="$2ല്"/>
  <pattern input="(([ൺ-ൿം])‌+)?m" replacement="$2മ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?n" replacement="$2ന്"/>
  <pattern input="(([ൺ-ൿം])‌+)?o" replacement="$2ഒ"/>
  <pattern input="(([ൺ-ൿം])‌+)?p" replacement="$2പ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?q" replacement="$2ക്ക്"/>
  <pattern input="(([ൺ-ൿം])‌+)?r" replacement="$2ര്"/>
  <pattern input="(([ൺ-ൿം])‌+)?R" replacement="$2റ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?s" replacement="$2സ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?t" replacement="$2ട്"/>
  <pattern input="(([ൺ-ൿം])‌+)?u" replacement="$2ഉ"/>
  <pattern input="(([ൺ-ൿം])‌+)?(v|w)" replacement="$2വ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?x" replacement="$2ക്ഷ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?y" replacement="$2യ്"/>
  <pattern input="(([ൺ-ൿം])‌+)?~" replacement="$2്"/>
  <pattern input="(‌)*_" replacement="‌"/>
  <pattern input="//" replacement="ऽ"/>
  <pattern input="1/4\\" replacement="൳"/>
  <pattern input="1/2\\" replacement="൴"/>
  <pattern input="3/4\\" replacement="൵"/>
  <pattern input="10\\" replacement="൰"/>
  <pattern input="100\\" replacement="൱"/>
  <pattern input="1000\\" replacement="൲"/>
  <pattern input="\-\-\-\\" replacement="—"/>
  <pattern input="\-\-\\" replacement="–"/>
  <pattern input="\\\-" replacement="−"/>
  <pattern input="\\\*" replacement="×"/>
  <pattern input="\\/" replacement="÷"/>
</inputmethod>
