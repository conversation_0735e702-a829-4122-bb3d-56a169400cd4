<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources>
    <!-- The extra margin in dp around the hit area of the commit/add-to-dictionary indicator -->
    <integer name="text_decorator_hit_area_margin_in_dp">
        4
    </integer>

    <!-- Background color to be used to highlight the target text when the add-to-dictionary
         indicator is visible. -->
    <color name="text_decorator_add_to_dictionary_indicator_text_highlight_color">
        #D1E7B7
    </color>

    <!-- Foreground color of the commit indicator. -->
    <color name="text_decorator_add_to_dictionary_indicator_background_color">
        #4EB848
    </color>

    <!-- Foreground color of the add-to-dictionary indicator. -->
    <color name="text_decorator_add_to_dictionary_indicator_foreground_color">
        #FFFFFF
    </color>

    <!-- Viewport size of "text_decorator_add_to_dictionary_indicator_path". -->
    <integer name="text_decorator_add_to_dictionary_indicator_path_size">
        480
    </integer>

    <!-- Coordinates of the closed path to be used to render the add-to-dictionary indicator.
         The format is: X[0], Y[0], X[1], Y[1], ..., X[N-1], Y[N-1] -->
    <integer-array name="text_decorator_add_to_dictionary_indicator_path">
        <item>380</item>
        <item>260</item>
        <item>260</item>
        <item>260</item>
        <item>260</item>
        <item>380</item>
        <item>220</item>
        <item>380</item>
        <item>220</item>
        <item>260</item>
        <item>100</item>
        <item>260</item>
        <item>100</item>
        <item>220</item>
        <item>220</item>
        <item>220</item>
        <item>220</item>
        <item>100</item>
        <item>260</item>
        <item>100</item>
        <item>260</item>
        <item>220</item>
        <item>380</item>
        <item>220</item>
    </integer-array>
</resources>
