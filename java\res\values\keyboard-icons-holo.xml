<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<resources xmlns:android="http://schemas.android.com/apk/res/android">
    <style name="KeyboardIcons.Holo">
        <!-- Keyboard icons -->
        <item name="iconShiftKey">@drawable/sym_keyboard_shift_holo_dark</item>
        <item name="iconDelete<PERSON><PERSON>">@drawable/sym_keyboard_delete_holo_dark</item>
        <item name="iconSettingsKey">@drawable/sym_keyboard_settings_holo_dark</item>
        <item name="iconSpaceKey">@null</item>
        <item name="iconEnterKey">@drawable/sym_keyboard_return_holo_dark</item>
        <item name="iconSearchKey">@drawable/sym_keyboard_search_holo_dark</item>
        <item name="iconTabKey">@drawable/sym_keyboard_tab_holo_dark</item>
        <item name="iconShortcutKey">@drawable/sym_keyboard_voice_holo_dark</item>
        <item name="iconSpaceKeyForNumberLayout">@drawable/sym_keyboard_space_holo_dark</item>
        <item name="iconShiftKeyShifted">@drawable/sym_keyboard_shift_locked_holo_dark</item>
        <item name="iconShortcutKeyDisabled">@drawable/sym_keyboard_voice_off_holo_dark</item>
        <item name="iconLanguageSwitchKey">@drawable/sym_keyboard_language_switch_dark</item>
        <item name="iconZwnjKey">@drawable/sym_keyboard_zwnj_holo_dark</item>
        <item name="iconZwjKey">@drawable/sym_keyboard_zwj_holo_dark</item>
        <item name="iconEmojiActionKey">@drawable/sym_keyboard_smiley_holo_dark</item>
        <item name="iconEmojiNormalKey">@drawable/sym_keyboard_smiley_holo_dark</item>
    </style>
</resources>
