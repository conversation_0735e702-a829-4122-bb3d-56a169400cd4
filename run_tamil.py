#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main runner script for Tamil transliteration
Choose between different testing modes
"""

import sys
from tamil_transliterator import TamilTransliterator, simulate_real_time_typing, batch_test, interactive_mode

def show_menu():
    """Show the main menu"""
    print("\nTamil Transliteration Engine")
    print("=" * 40)
    print("Choose an option:")
    print("1. Interactive mode (type words)")
    print("2. Batch test (common words)")
    print("3. Real-time simulation demo")
    print("4. Quick syllable test")
    print("5. Test specific word")
    print("6. Exit")
    print("-" * 40)

def quick_syllable_test(transliterator):
    """Quick test of basic syllables"""
    print("\nQuick Syllable Test")
    print("=" * 25)
    
    syllables = ['ka', 'ki', 'ku', 'ma', 'mi', 'mu', 'na', 'ni', 'nu', 
                'ta', 'ti', 'tu', 'pa', 'pi', 'pu', 'ra', 'ri', 'ru',
                'la', 'li', 'lu', 'va', 'vi', 'vu']
    
    for i, syllable in enumerate(syllables):
        result = transliterator.transliterate(syllable)
        print(f"{syllable:<4} -> {result:<4}", end="  ")
        if (i + 1) % 6 == 0:  # New line every 6 items
            print()
    print()

def test_specific_word(transliterator):
    """Test a specific word with detailed output"""
    word = input("\nEnter a word to test: ").strip()
    if not word:
        return
    
    print(f"\nTesting word: '{word}'")
    print("-" * 30)
    
    # Basic transliteration
    result = transliterator.transliterate(word)
    print(f"Result: {result}")
    
    # Real-time simulation
    print("\nReal-time simulation:")
    simulate_real_time_typing(transliterator, word)
    
    # Character analysis
    print(f"\nAnalysis:")
    print(f"Input length: {len(word)} characters")
    print(f"Output length: {len(result)} characters")
    print(f"Unicode points: {[hex(ord(c)) for c in result]}")

def real_time_demo(transliterator):
    """Demo of real-time transliteration"""
    demo_words = ['vanakkam', 'nandri', 'tamil', 'amma', 'appa']
    
    print("\nReal-time Transliteration Demo")
    print("=" * 35)
    
    for word in demo_words:
        simulate_real_time_typing(transliterator, word)
        input("\nPress Enter to continue...")

def main():
    """Main function"""
    transliterator = TamilTransliterator()
    
    print("Welcome to Tamil Transliteration Engine!")
    print("Based on Indic Keyboard logic")
    
    while True:
        show_menu()
        
        try:
            choice = input("Enter your choice (1-6): ").strip()
            
            if choice == '1':
                interactive_mode(transliterator)
            elif choice == '2':
                batch_test(transliterator)
            elif choice == '3':
                real_time_demo(transliterator)
            elif choice == '4':
                quick_syllable_test(transliterator)
            elif choice == '5':
                test_specific_word(transliterator)
            elif choice == '6':
                print("Thank you for using Tamil Transliteration Engine!")
                break
            else:
                print("Invalid choice. Please enter 1-6.")
        
        except KeyboardInterrupt:
            print("\n\nExiting...")
            break
        except EOFError:
            print("\n\nExiting...")
            break

if __name__ == "__main__":
    main()
