<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2011, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Provide audio and haptic feedback by ourselves based on the keyboard settings.
         We just need to ignore the system's audio and haptic feedback settings. -->
    <LinearLayout
        android:id="@+id/suggestions_strip"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/config_suggestions_strip_horizontal_margin"
        android:layout_marginRight="@dimen/config_suggestions_strip_horizontal_margin"
        android:hapticFeedbackEnabled="false"
        android:soundEffectsEnabled="false" />
    <!-- Provide audio and haptic feedback by ourselves based on the keyboard settings.
         We just need to ignore the system's audio and haptic feedback settings. -->
    <LinearLayout
        android:id="@+id/add_to_dictionary_strip"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/config_suggestions_strip_horizontal_margin"
        android:layout_marginRight="@dimen/config_suggestions_strip_horizontal_margin"
        android:hapticFeedbackEnabled="false"
        android:soundEffectsEnabled="false">
        <TextView
            android:id="@+id/word_to_save"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            style="?attr/suggestionWordStyle" />
        <include
            android:id="@+id/word_to_save_divider"
            layout="@layout/suggestion_divider" />
        <TextView
            android:id="@+id/hint_add_to_dictionary"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical|start"
            style="?attr/suggestionWordStyle" />
    </LinearLayout>
    <!-- Provide audio and haptic feedback by ourselves based on the keyboard settings.
         We just need to ignore the system's audio and haptic feedback settings. -->
    <LinearLayout
        android:id="@+id/important_notice_strip"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/config_suggestions_strip_horizontal_margin"
        android:layout_marginRight="@dimen/config_suggestions_strip_horizontal_margin"
        android:hapticFeedbackEnabled="false"
        android:soundEffectsEnabled="false" >
        <TextView
            android:id="@+id/important_notice_title"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="6sp"
            android:textSize="16sp"
            style="?attr/suggestionWordStyle" />
    </LinearLayout>
    <ImageButton
        android:id="@+id/suggestions_strip_voice_key"
        android:layout_width="@dimen/config_suggestions_strip_edge_key_width"
        android:layout_height="fill_parent"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:contentDescription="@string/spoken_description_mic"
        style="?attr/suggestionWordStyle" />
</merge>
