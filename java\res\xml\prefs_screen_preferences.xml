<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<PreferenceScreen
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:latin="http://schemas.android.com/apk/res-auto"
    android:title="@string/settings_screen_preferences">
    <CheckBoxPreference
        android:key="auto_cap"
        android:title="@string/auto_cap"
        android:summary="@string/auto_cap_summary"
        android:defaultValue="true"
        android:persistent="true" />
    <CheckBoxPreference
        android:key="pref_key_use_double_space_period"
        android:title="@string/use_double_space_period"
        android:summary="@string/use_double_space_period_summary"
        android:defaultValue="true"
        android:persistent="true" />
    <CheckBoxPreference
        android:key="vibrate_on"
        android:title="@string/vibrate_on_keypress"
        android:defaultValue="@bool/config_default_vibration_enabled"
        android:persistent="true" />
    <CheckBoxPreference
        android:key="sound_on"
        android:title="@string/sound_on_keypress"
        android:defaultValue="@bool/config_default_sound_enabled"
        android:persistent="true" />
    <CheckBoxPreference
        android:key="popup_on"
        android:title="@string/popup_on_keypress"
        android:defaultValue="@bool/config_default_key_preview_popup"
        android:persistent="true" />
    <CheckBoxPreference
        android:key="pref_voice_input_key"
        android:title="@string/voice_input"
        android:defaultValue="true"
        android:persistent="true" />
</PreferenceScreen>
