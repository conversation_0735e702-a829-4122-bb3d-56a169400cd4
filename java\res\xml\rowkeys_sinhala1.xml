<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted">
            <!-- U+0DD6: "ූ" SINHALA VOWEL SIGN DIGA PAA-PILLA -->
            <Key latin:keySpec="&#x0DD6;" />
            <!-- U+0D8B: "උ" SINHALA LETTER UYANNA -->
            <Key latin:keySpec="&#x0D8B;" />
            <!-- U+0DD1: "ෑ" SINHALA VOWEL SIGN DIGA AEDA-PILLA -->
            <Key latin:keySpec="&#x0DD1;" />
            <!-- U+0D8D: "ඍ" SINHALA LETTER IRUYANNA -->
            <Key latin:keySpec="&#x0D8D;" />
            <!-- U+0D94: "ඔ" SINHALA LETTER OYANNA -->
            <Key latin:keySpec="&#x0D94;" />
            <!-- U+0DC1: "ශ" SINHALA LETTER TAALUJA SAYANNA -->
            <Key latin:keySpec="&#x0DC1;" />
            <!-- U+0DB9: "ඹ" SINHALA LETTER AMBA BAYANNA -->
            <Key latin:keySpec="&#x0DB9;" />
            <!-- U+0DC2: "ෂ" SINHALA LETTER MUURDHAJA SAYANNA -->
            <Key latin:keySpec="&#x0DC2;" />
            <!-- U+0DB0: "ධ" SINHALA LETTER MAHAAPRAANA DAYANNA -->
            <Key latin:keySpec="&#x0DB0;" />
            <!-- U+0DA1: "ඡ" SINHALA LETTER MAHAAPRAANA CAYANNA -->
            <Key latin:keySpec="&#x0DA1;" />
            <!-- U+0DA5: "ඥ" SINHALA LETTER TAALUJA SANYOOGA NAAKSIKYAYA
                 U+0DF4: "෴" SINHALA PUNCTUATION KUNDDALIYA  -->
            <Key
                latin:keySpec="&#x0DA5;"
                latin:moreKeys="&#x0DF4;" />
        </case>
        <default>
            <!-- U+0DD4: "ු" SINHALA VOWEL SIGN KETTI PAA-PILLA -->
            <Key
                latin:keySpec="&#x0DD4;"
                latin:keyHintLabel="1"
                latin:additionalMoreKeys="1" />
            <!-- U+0D85: "අ" SINHALA LETTER AYANNA -->
            <Key
                latin:keySpec="&#x0D85;"
                latin:keyHintLabel="2"
                latin:additionalMoreKeys="2" />
            <!-- U+0DD0: "ැ" SINHALA VOWEL SIGN KETTI AEDA-PILLA -->
            <Key
                latin:keySpec="&#x0DD0;"
                latin:keyHintLabel="3"
                latin:additionalMoreKeys="3" />
            <!-- U+0DBB: "ර" SINHALA LETTER RAYANNA -->
            <Key
                latin:keySpec="&#x0DBB;"
                latin:keyHintLabel="4"
                latin:additionalMoreKeys="4" />
            <!-- U+0D91: "එ" SINHALA LETTER EYANNA -->
            <Key
                latin:keySpec="&#x0D91;"
                latin:keyHintLabel="5"
                latin:additionalMoreKeys="5" />
            <!-- U+0DC4: "හ" SINHALA LETTER HAYANNA -->
            <Key
                latin:keySpec="&#x0DC4;"
                latin:keyHintLabel="6"
                latin:additionalMoreKeys="6" />
            <!-- U+0DB8: "ම" SINHALA LETTER MAYANNA -->
            <Key
                latin:keySpec="&#x0DB8;"
                latin:keyHintLabel="7"
                latin:additionalMoreKeys="7" />
            <!-- U+0DC3: "ස" SINHALA LETTER DANTAJA SAYANNA -->
            <Key
                latin:keySpec="&#x0DC3;"
                latin:keyHintLabel="8"
                latin:additionalMoreKeys="8" />
            <!-- U+0DAF: "ද" SINHALA LETTER ALPAPRAANA DAYANNA
                 U+0DB3: "ඳ" SINHALA LETTER SANYAKA DAYANNA -->
            <Key
                latin:keySpec="&#x0DAF;"
                latin:moreKeys="&#x0DB3;"
                latin:keyHintLabel="9"
                latin:additionalMoreKeys="9" />
            <!-- U+0DA0: "ච" SINHALA LETTER ALPAPRAANA CAYANNA -->
            <Key
                latin:keySpec="&#x0DA0;"
                latin:keyHintLabel="0"
                latin:additionalMoreKeys="0" />
            <!-- U+0DA4: "ඤ" SINHALA LETTER TAALUJA NAASIKYAYA
                 U+0DF4: "෴" SINHALA PUNCTUATION KUNDDALIYA -->
            <Key
                latin:keySpec="&#x0DA4;"
                latin:moreKeys="&#x0DF4;" />
        </default>
    </switch>
</merge>
