<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <Key
        latin:keySpec="*"
        latin:moreKeys="!text/morekeys_star" />
    <switch>
        <case
            latin:languageCode="fa"
        >
            <Key
                latin:keySpec="!text/keyspec_left_double_angle_quote"
                latin:moreKeys="!text/morekeys_double_quote" />
            <Key
                latin:keySpec="!text/keyspec_right_double_angle_quote"
                latin:moreKeys="!text/morekeys_single_quote" />
        </case>
        <default>
            <Key
                latin:keySpec="&quot;"
                latin:moreKeys="!text/morekeys_double_quote" />
            <Key
                latin:keySpec="\'"
                latin:moreKeys="!text/morekeys_single_quote" />
        </default>
    </switch>
    <Key
        latin:keySpec=":" />
    <Key
        latin:keySpec="!text/keyspec_symbols_semicolon"
        latin:moreKeys="!text/morekeys_symbols_semicolon" />
    <Key
        latin:keySpec="!"
        latin:moreKeys="!text/morekeys_exclamation" />
    <Key
        latin:keySpec="!text/keyspec_symbols_question"
        latin:moreKeys="!text/morekeys_question" />
</merge>
