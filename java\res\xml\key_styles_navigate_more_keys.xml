<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case latin:keyboardTheme="ICS|KLP">
            <key-style
                latin:styleName="navigateNextMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!text/keyspec_action_next" />
            <key-style
                latin:styleName="navigatePreviousMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!text/keyspec_action_previous" />
            <key-style
                latin:styleName="navigatePreviousNextMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!fixedColumnOrder!2,!needsDividers!,!text/keyspec_action_previous,!text/keyspec_action_next" />
            <key-style
                latin:styleName="navigateEmojiMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!text/keyspec_emoji_action_key" />
            <key-style
                latin:styleName="navigateEmojiNextMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!fixedColumnOrder!2,!needsDividers!,!text/keyspec_emoji_action_key,!text/keyspec_action_next" />
            <key-style
                latin:styleName="navigateEmojiPreviousMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!fixedColumnOrder!2,!needsDividers!,!text/keyspec_emoji_action_key,!text/keyspec_action_previous" />
            <key-style
                latin:styleName="navigateEmojiPreviousNextMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!fixedColumnOrder!3,!needsDividers!,!text/keyspec_emoji_action_key,!text/keyspec_action_previous,!text/keyspec_action_next" />
        </case>
        <default>
            <key-style
                latin:styleName="navigateNextMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!icon/next_key|!code/key_action_next" />
            <key-style
                latin:styleName="navigatePreviousMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!icon/previous_key|!code/key_action_previous" />
            <key-style
                latin:styleName="navigatePreviousNextMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!fixedColumnOrder!2,!needsDividers!,!icon/previous_key|!code/key_action_previous,!icon/next_key|!code/key_action_next" />
            <key-style
                latin:styleName="navigateEmojiMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!text/keyspec_emoji_action_key" />
            <key-style
                latin:styleName="navigateEmojiNextMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!fixedColumnOrder!2,!needsDividers!,!text/keyspec_emoji_action_key,!icon/next_key|!code/key_action_next" />
            <key-style
                latin:styleName="navigateEmojiPreviousMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!fixedColumnOrder!2,!needsDividers!,!text/keyspec_emoji_action_key,!icon/previous_key|!code/key_action_previous" />
            <key-style
                latin:styleName="navigateEmojiPreviousNextMoreKeysStyle"
                latin:keyLabelFlags="hasPopupHint|preserveCase"
                latin:moreKeys="!fixedColumnOrder!3,!needsDividers!,!text/keyspec_emoji_action_key,!icon/previous_key|!code/key_action_previous,!icon/next_key|!code/key_action_next" />
        </default>
    </switch>
</merge>
