<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2010, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- Configuration values for Small Phone Portrait. -->
<resources>
    <bool name="config_use_fullscreen_mode">false</bool>

    <dimen name="config_key_hysteresis_distance">8.0dp</dimen>

    <!-- Preferable keyboard height in absolute scale: 1.285in -->
    <!-- This config_default_keyboard_height value should match with keyboard-heights.xml -->
    <dimen name="config_default_keyboard_height">205.6dp</dimen>
    <fraction name="config_max_keyboard_height">46%p</fraction>
    <fraction name="config_min_keyboard_height">-61.8%p</fraction>

    <dimen name="config_more_keys_keyboard_key_height">52.8dp</dimen>
    <!-- Amount of allowance for selecting keys in a mini popup keyboard by sliding finger. -->
    <!-- config_more_keys_keyboard_key_height x 1.2 -->
    <dimen name="config_more_keys_keyboard_slide_allowance">63.36dp</dimen>
    <dimen name="config_more_keys_keyboard_key_horizontal_padding">8dp</dimen>

    <fraction name="config_keyboard_top_padding_holo">2.335%p</fraction>
    <fraction name="config_keyboard_bottom_padding_holo">4.669%p</fraction>
    <fraction name="config_key_vertical_gap_holo">6.127%p</fraction>
    <fraction name="config_key_horizontal_gap_holo">1.739%p</fraction>
    <!-- config_more_keys_keyboard_key_height x -0.5 -->
    <dimen name="config_more_keys_keyboard_vertical_correction_holo">-26.4dp</dimen>
    <dimen name="config_key_preview_offset_holo">8.0dp</dimen>
    <dimen name="config_key_preview_height_holo">80dp</dimen>
    <dimen name="config_key_preview_offset_lxx">50.0dp</dimen>
    <dimen name="config_key_preview_height_lxx">122dp</dimen>
    <fraction name="config_key_preview_text_ratio">82%</fraction>
    <fraction name="config_key_letter_ratio_holo">55%</fraction>
    <fraction name="config_key_letter_ratio_lxx">55%</fraction>
    <fraction name="config_key_large_letter_ratio_holo">65%</fraction>
    <fraction name="config_key_large_letter_ratio_lxx">90%</fraction>
    <fraction name="config_key_label_ratio_holo">34%</fraction>
    <fraction name="config_key_label_ratio_lxx">34%</fraction>
    <fraction name="config_key_hint_letter_ratio_holo">25%</fraction>
    <fraction name="config_key_hint_letter_ratio_lxx">25%</fraction>
    <fraction name="config_key_hint_label_ratio_holo">44%</fraction>
    <fraction name="config_key_hint_label_ratio_lxx">30%</fraction>
    <fraction name="config_key_shifted_letter_hint_ratio_holo">35%</fraction>
    <fraction name="config_key_shifted_letter_hint_ratio_lxx">35%</fraction>
    <fraction name="config_language_on_spacebar_text_ratio">33.735%</fraction>
    <dimen name="config_key_hint_letter_padding">1dp</dimen>
    <dimen name="config_key_shifted_letter_hint_padding">2dp</dimen>

    <!-- For 5-row keyboard -->
    <fraction name="config_key_vertical_gap_5row">3.20%p</fraction>
    <fraction name="config_key_letter_ratio_5row">55%</fraction>
    <fraction name="config_key_shifted_letter_hint_ratio_5row">41%</fraction>

    <dimen name="config_suggestions_strip_height">40dp</dimen>
    <dimen name="config_suggestions_strip_horizontal_margin">36dp</dimen>
    <dimen name="config_suggestions_strip_edge_key_width">36dp</dimen>
    <dimen name="config_more_suggestions_row_height">40dp</dimen>
    <integer name="config_max_more_suggestions_row">6</integer>
    <fraction name="config_min_more_suggestions_width">90%</fraction>
    <dimen name="config_suggestion_min_width">44dp</dimen>
    <dimen name="config_suggestion_text_horizontal_padding">6dp</dimen>
    <dimen name="config_suggestion_text_size">18dp</dimen>
    <dimen name="config_more_suggestions_hint_text_size">27dp</dimen>

    <!-- Gesture floating preview text parameters -->
    <dimen name="config_gesture_floating_preview_text_size">24dp</dimen>
    <dimen name="config_gesture_floating_preview_text_offset">73dp</dimen>
    <dimen name="config_gesture_floating_preview_horizontal_padding">24dp</dimen>
    <dimen name="config_gesture_floating_preview_vertical_padding">16dp</dimen>
    <dimen name="config_gesture_floating_preview_round_radius">2dp</dimen>

    <!-- Emoji keyboard -->
    <fraction name="config_emoji_keyboard_key_width">14.2857%p</fraction>
    <fraction name="config_emoji_keyboard_row_height">33%p</fraction>
    <fraction name="config_emoji_keyboard_key_letter_size">68%p</fraction>
    <integer name="config_emoji_keyboard_max_page_key_count">21</integer>
</resources>
