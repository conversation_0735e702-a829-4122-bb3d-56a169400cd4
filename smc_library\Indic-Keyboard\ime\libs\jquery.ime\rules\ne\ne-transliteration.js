( function ( $ ) {
	'use strict';

	var neTransliteration = {
		id: 'ne-transliteration',
		name: 'लिप्यंतरण',
		description: 'Nepali transliteration',
		date: '2012-10-14',
		URL: 'http://github.com/wikimedia/jquery.ime',
		author: '<PERSON>aid <PERSON> ([[user:Junaidpv]]) and <PERSON><PERSON><PERSON> ([[user:<PERSON><PERSON><PERSON>]])',
		license: 'GPLv3',
		version: '1.0',
		contextLength: 4,
		maxKeyLength: 1,
		patterns: [
			['क्h','c','च्'],
			['\\\\([A-Za-z\\>_~\\.0-9])','\\\\','$1'],

			['([क-ह]़?)्a','$1'],
			['([क-ह]़?)्A','$1ा'],
			['([क-ह]़?)a','$1ा'],
			['([क-ह]़?)्i','$1ि'],
			['([क-ह]़?)(्I|िi|ेe)','$1ी'],
			['([क-ह]़?)्u','$1ु'],
			['([क-ह]़?)(ुu|्U|ोo)','$1ू'],
			['([क-ह]़?)्R','$1ृ'],
			['([क-ह]़?)ृR','$1ॄ'],
			['([क-ह]़?)्ळ्l','$1ॢ'],
			['([क-ह]़?)ॢl','$1ॣ'],
			['([क-ह]़?)ॆ\\^','$1ॅ'],
			['([क-ह]़?)्e','$1े'],
			['([क-ह]़?)्E','$1े'],
			['([क-ह]़?)ॊ\\^','$1ॉ'],
			['([क-ह]़?)i','$1ै'],
			['([क-ह]़?)्o','$1ो'],
			['([क-ह]़?)्O','$1ो'],
			['([क-ह]़?)u','$1ौ'],
			['([क-ह]़?)ृa','$1्ऱ'],
			['([क-ह]़?)ृA','$1्ऱा'],
			['([क-ह]़?)ृi','$1्ऱि'],
			['([क-ह]़?)ृI','$1्ऱी'],
			['([क-ह]़?)ृu','$1्ऱु'],
			['([क-ह]़?)ृU','$1्ऱू'],
			['([क-ह]़?)ृ\\^','$1्ऱॅ'],
			['([क-ह]़?)ृe','$1्ऱे'],
			['([क-ह]़?)ृE','$1्ऱे'],
			['([क-ह]़?)ृo','$1्ऱो'],
			['([क-ह]़?)ृO','$1्ऱो'],
			['([क-ह]़?)ृ\\~','$1्ऱ्'],
			['([क-ह])्\\`','$1़्'],

			['अa','आ'],
			['(ओo|उu)','ऊ'],
			['ऎ\\^','ए'],
			['अi','ऐ'],
			['अ\\^','अ'],
			['(इi|एe)','ई'],
			['ऒ\\^','ओ'],
			['अu','औ'],
			['र्र्r','\u0930\u094D\u200D'],
			['ड़्h','ढ्'],
			['ऋR','ॠ'],
			['ळ्l','ऌ'],
			['ऌl','ॡ'],
			['ं\\^','ँ'],
			['ंM','ँ'],
			['ओM','ॐ'],
			['ड्D','ड्'],
			['क्h','ख्'],
			['ग्h','घ्'],
			['G','ङ्'],
			['च्h','छ्'],
			['ज्h','झ्'],
			['न्j','ञ्'],
			['ट्h','ठ्'],
			['ड्h','ढ्'],
			['त्h','थ्'],
			['द्h','ध्'],
			['प्h','फ्'],
			['ब्h','भ्'],
			['ऋa','र'],
			['ऋA','रा'],
			['ऋi','रि'],
			['ऋI','री'],
			['ऋu','रु'],
			['ऋU','रू'],
			['ऋ\\^','ऋ'],
			['ऋe','रे'],
			['ऋE','रे'],
			['ऋo','रो'],
			['ऋO','रो'],
			['ऋ\\~','र्'],

			['स्h','श्'],
			['श्h','ष्'],
			['क़्h','ख्'],
			['ज़्h','ऴ्'],
			['।\\.','॥'],

			['a','अ'],
			['b','ब्'],
			['c','क्'],
			['d','द्'],
			['e','ए'],
			['(f|F)','फ्'],
			['g','ग्'],
			['h','ह्'],
			['i','इ'],
			['j','ज्'],
			['k','क्'],
			['l','ल्'],
			['m','म्'],
			['n','न्'],
			['o','ओ'],
			['p','प्'],
			['q','क़्'],
			['r','र्'],
			['s','स्'],
			['t','त्'],
			['u','उ'],
			['v','भ्'],
			['w','व्'],
			['x','क्ष्'],
			['y','य्'],
			['(z|Z)','ज़्'],
			['A','आ'],
			['B','ब्ब्'],
			['C','क्क्'],
			['D','ड्'],
			['E','ए'],
			['H','ः'],
			['I','ई'],
			['J','ज्ज्'],
			['K','क्क्'],
			['L','ळ्'],
			['M','ं'],
			['N','ण्'],
			['O','ओ'],
			['P','प्प्'],
			['Q','अ'],
			['R','ऋ'],
			['S','श्'],
			['T','ट्'],
			['U','ऊ'],
			['V','झ्'],
			['W','औ'],
			['X','क्ष्'],
			['Y','य्य्'],
			['0','०'],
			['1','१'],
			['2','२'],
			['3','३'],
			['4','४'],
			['5','५'],
			['6','६'],
			['7','७'],
			['8','८'],
			['9','९'],
			['\\.','।'],
			['//','ऽ'],
			['\\`','्']]
	};
	$.ime.register( neTransliteration );

}( jQuery ) );

