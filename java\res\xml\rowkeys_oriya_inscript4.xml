<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright © 2013-2014 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC), International Centre for Free and Open Source Software (ICFOSS)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case
            latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted"
        >
            <Key latin:keySpec="&#x0020;" />

            <!--X ଁ ORIYA SIGN CANDRABINDU-->
            <Key
                latin:keySpec="&#x0B01;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--C ଣ ORIYA LETTER NNA-->
            <Key
                latin:keySpec="&#x0B23;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key latin:keySpec="&#x0020;" />
            <Key latin:keySpec="&#x0020;" />

            <!--N ଳ ORIYA LETTER LLA-->
            <Key
                latin:keySpec="&#x0B33;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--M ଶ ORIYA LETTER SHA-->
            <Key
                latin:keySpec="&#x0B36;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\< ଷ ORIYA LETTER SSA-->
            <Key
                latin:keySpec="&#x0B37;"
                latin:moreKeys="&lt;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\> ୤  -->
            <Key
                latin:keySpec="&#x0B64;"
                latin:moreKeys="&gt;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\? ଯ ORIYA LETTER YA-->
            <Key
                latin:keySpec="&#x0B2F;"
                latin:moreKeys="\?"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

        </case>

        <default>
            <Key latin:keySpec="&#x0020;" />

            <!--x ଂ ORIYA SIGN ANUSVARA-->
            <Key
                latin:keySpec="&#x0B02;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--c ମ ORIYA LETTER MA-->
            <Key
                latin:keySpec="&#x0B2E;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--v ନ ORIYA LETTER NA-->
            <Key
                latin:keySpec="&#x0B28;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--b ୱ ORIYA LETTER WA-->
            <Key
                latin:keySpec="&#x0B71;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--n ଲ ORIYA LETTER LA-->
            <Key
                latin:keySpec="&#x0B32;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--m ସ ORIYA LETTER SA-->
            <Key
                latin:keySpec="&#x0B38;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec=","
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="."
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--/ ୟ ORIYA LETTER YYA-->
            <Key
                latin:keySpec="&#x0B5F;"
                latin:moreKeys="/"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
         </default>
    </switch>
</merge>
