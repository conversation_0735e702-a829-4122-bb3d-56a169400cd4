<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright © 2013-2014 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC), International Centre for Free and Open Source Software (ICFOSS)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case
            latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted"
        >
            <Key
                latin:keySpec="&#x0D0E;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <!-- ZWSP -->
            <Key
                latin:keySpec="&#x200b;"
                latin:keyHintLabel="ZWS"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D23;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key latin:keySpec="&#x0020;" />
            <Key
                latin:keySpec="&#x0D34;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D33;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D36;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D37;"
                latin:moreKeys="&lt;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&gt;"
                latin:moreKeys="!fixedColumnOrder!3,&#x203A;,&#x2265;,&#x00BB;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="\?"
                latin:moreKeys="!text/morekeys_question"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
        </case>

        <default>
            <Key
                latin:keySpec="&#x0D46;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D02;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D2E;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D28;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D35;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D32;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D38;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec=","
                latin:additionalMoreKeys="&lt;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="."
                latin:additionalMoreKeys="&gt;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
            <Key
                latin:keySpec="&#x0D2F;"
                latin:moreKeys="/"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
         </default>
    </switch>
</merge>
