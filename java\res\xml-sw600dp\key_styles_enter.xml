<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <!-- Navigate more keys style -->
    <include latin:keyboardLayout="@xml/key_styles_navigate_more_keys" />
    <switch>
        <case
            latin:imeAction="actionNext"
            latin:navigatePrevious="true"
        >
            <key-style
                latin:styleName="navigateMoreKeysStyle"
                latin:parentStyle="navigatePreviousMoreKeysStyle" />
        </case>
        <case
            latin:imeAction="actionNext"
        >
            <key-style
                latin:styleName="navigateMoreKeysStyle" />
        </case>
        <case
            latin:imeAction="actionPrevious"
            latin:navigateNext="true"
        >
            <key-style
                latin:styleName="navigateMoreKeysStyle"
                latin:parentStyle="navigateNextMoreKeysStyle" />
        </case>
        <case
            latin:imeAction="actionPrevious"
        >
            <key-style
                latin:styleName="navigateMoreKeysStyle" />
        </case>
        <case
            latin:navigateNext="true"
            latin:navigatePrevious="true"
        >
            <key-style
                latin:styleName="navigateMoreKeysStyle"
                latin:parentStyle="navigatePreviousNextMoreKeysStyle" />
        </case>
        <case
            latin:navigateNext="true"
        >
            <key-style
                latin:styleName="navigateMoreKeysStyle"
                latin:parentStyle="navigateNextMoreKeysStyle" />
        </case>
        <case
            latin:navigatePrevious="true"
        >
            <key-style
                latin:styleName="navigateMoreKeysStyle"
                latin:parentStyle="navigatePreviousMoreKeysStyle" />
        </case>
        <default>
            <key-style
                latin:styleName="navigateMoreKeysStyle" />
        </default>
    </switch>
    <!-- Enter key style -->
    <switch>
        <case latin:keyboardTheme="ICS|KLP">
            <key-style
                latin:styleName="defaultEnterKeyStyle"
                latin:keySpec="!icon/enter_key|!code/key_enter"
                latin:keyLabelFlags="preserveCase|autoXScale|followKeyLabelRatio|followFunctionalTextColor"
                latin:keyActionFlags="noKeyPreview"
                latin:backgroundType="action"
                latin:parentStyle="navigateMoreKeysStyle" />
        </case>
        <!-- keyboardTheme="LXXLight|LXXDark" -->
        <default>
            <key-style
                latin:styleName="defaultEnterKeyStyle"
                latin:keySpec="!icon/enter_key|!code/key_enter"
                latin:keyLabelFlags="preserveCase|autoXScale|followKeyLabelRatio|followFunctionalTextColor|keepBackgroundAspectRatio"
                latin:keyActionFlags="noKeyPreview"
                latin:backgroundType="action"
                latin:parentStyle="navigateMoreKeysStyle" />
        </default>
    </switch>
    <include latin:keyboardLayout="@xml/key_styles_actions" />
    <switch>
        <!-- Shift + Enter in textMultiLine field. -->
        <case
            latin:isMultiLine="true"
            latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLockShifted"
        >
            <key-style
                latin:styleName="enterKeyStyle"
                latin:keySpec="!icon/enter_key|!code/key_shift_enter"
                latin:parentStyle="defaultEnterKeyStyle" />
        </case>
        <case
            latin:imeAction="actionGo"
        >
            <key-style
                latin:styleName="enterKeyStyle"
                latin:parentStyle="goActionKeyStyle" />
        </case>
        <case
            latin:imeAction="actionNext"
        >
            <key-style
                latin:styleName="enterKeyStyle"
                latin:parentStyle="nextActionKeyStyle" />
        </case>
        <case
            latin:imeAction="actionPrevious"
        >
            <key-style
                latin:styleName="enterKeyStyle"
                latin:parentStyle="previousActionKeyStyle" />
        </case>
        <case
            latin:imeAction="actionDone"
        >
            <key-style
                latin:styleName="enterKeyStyle"
                latin:parentStyle="doneActionKeyStyle" />
        </case>
        <case
            latin:imeAction="actionSend"
        >
            <key-style
                latin:styleName="enterKeyStyle"
                latin:parentStyle="sendActionKeyStyle" />
        </case>
        <case
            latin:imeAction="actionSearch"
        >
            <key-style
                latin:styleName="enterKeyStyle"
                latin:parentStyle="searchActionKeyStyle" />
        </case>
        <case
            latin:imeAction="actionCustomLabel"
        >
            <key-style
                latin:styleName="enterKeyStyle"
                latin:parentStyle="customLabelActionKeyStyle" />
        </case>
        <!-- imeAction is either actionNone or actionUnspecified. -->
        <default>
            <key-style
                latin:styleName="enterKeyStyle"
                latin:parentStyle="defaultEnterKeyStyle" />
        </default>
    </switch>
</merge>
