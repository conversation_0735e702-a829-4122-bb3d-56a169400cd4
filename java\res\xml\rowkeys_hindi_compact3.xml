<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_candra_o" />
    <!-- U+0911: "ऑ" DEVANAGARI LETTER CANDRA O -->
    <Key
        latin:keySpec="&#x0911;"
        latin:keyStyle="moreKeysDevanagariVowelSignCandraO" />
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_virama" />
    <Key latin:keyStyle="baseKeyDevanagariSignVirama" />
    <!-- Because the font rendering system prior to API version 16 can't automatically
         render dotted circle for incomplete combining letter of some scripts, different
         set of Key definitions are needed based on the API version. -->
    <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_anusvara" />
    <Key latin:keyStyle="baseKeyDevanagariSignAnusvara" />
    <!-- U+092E: "म" DEVANAGARI LETTER MA
         U+0950: "ॐ" DEVANAGARI OM -->
    <Key
        latin:keySpec="&#x092E;"
        latin:moreKeys="&#x0950;" />
    <!-- U+0928: "न" DEVANAGARI LETTER NA
         U+0923: "ण" DEVANAGARI LETTER NNA
         U+091E: "ञ" DEVANAGARI LETTER NYA
         U+0919: "ङ" DEVANAGARI LETTER NGA -->
    <Key
        latin:keySpec="&#x0928;"
        latin:moreKeys="&#x0923;,&#x091E;,&#x0919;" />
    <!-- U+0935: "व" DEVANAGARI LETTER VA -->
    <Key latin:keySpec="&#x0935;" />
    <!-- U+0932: "ल" DEVANAGARI LETTER LA -->
    <Key latin:keySpec="&#x0932;" />
    <!-- U+0938: "स" DEVANAGARI LETTER SA
         U+0936: "श" DEVANAGARI LETTER SHA
         U+0937: "ष" DEVANAGARI LETTER SSA
         U+0936/U+094D/U+0930: "श्र" DEVANAGARI LETTER SHA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER RA -->
    <Key
        latin:keySpec="&#x0938;"
        latin:moreKeys="&#x0936;,&#x0937;,&#x0936;&#x094D;&#x0930;" />
    <!-- U+092F: "य" DEVANAGARI LETTER YA -->
    <Key latin:keySpec="&#x092F;" />
    <!-- U+0915/U+094D/U+0937: "क्ष" DEVANAGARI LETTER KA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER SSA -->
    <Key
        latin:keySpec="&#x0915;&#x094D;&#x0937;"
        latin:keyLabelFlags="followKeyLetterRatio" />
</merge>
