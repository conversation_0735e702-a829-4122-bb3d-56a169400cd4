<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted">
            <!-- U+0915/U+094D: "क्" DEVANAGARI LETTER KA/DEVANAGARI SIGN VIRAMA -->
            <Key
                latin:keySpec="&#x0915;&#x094D;"
                latin:keyLabelFlags="followKeyLetterRatio" />
            <!-- U+0939/U+094D/U+092E: "ह्म" DEVANAGARI LETTER HA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER MA -->
            <Key
                latin:keySpec="&#x0939;&#x094D;&#x092E;"
                latin:keyLabelFlags="followKeyLetterRatio" />
            <!-- U+090B: "ऋ" DEVANAGARI LETTER VOCALIC R -->
            <Key latin:keySpec="&#x090B;" />
            <!-- U+0950: "ॐ" DEVANAGARI OM -->
            <Key latin:keySpec="&#x0950;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_au" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignAu" />
            <!-- U+0926/U+094D/U+092F: "द्य" DEVANAGARI LETTER DA/DEVANAGARI SIGN VIRAMA/DEVANAGARI LETTER YA -->
            <Key
                latin:keySpec="&#x0926;&#x094D;&#x092F;"
                latin:keyLabelFlags="followKeyLetterRatio" />
        </case>
        <default>
            <!-- U+0936: "श" DEVANAGARI LETTER SHA -->
            <Key latin:keySpec="&#x0936;" />
            <!-- U+0939: "ह" DEVANAGARI LETTER HA -->
            <Key latin:keySpec="&#x0939;" />
            <!-- U+0905: "अ" DEVANAGARI LETTER A -->
            <Key latin:keySpec="&#x0905;" />
            <!-- U+0916: "ख" DEVANAGARI LETTER KHA -->
            <Key latin:keySpec="&#x0916;" />
            <!-- U+0926: "द" DEVANAGARI LETTER DA -->
            <Key latin:keySpec="&#x0926;" />
            <!-- U+0932: "ल" DEVANAGARI LETTER LA -->
            <Key latin:keySpec="&#x0932;" />
        </default>
    </switch>
</merge>
