<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright © 2013-2014 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC), International Centre for Free and Open Source Software (ICFOSS)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case
            latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted"
        >
            <Key latin:keySpec="&#x0020;" />

            <!--X ઁ GUJARATI SIGN CANDRABINDU-->
            <Key
                latin:keySpec="&#x0A81;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--C ણ GUJARATI LETTER NNA-->
            <Key
                latin:keySpec="&#x0AA3;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key latin:keySpec="&#x0020;" />

            <Key latin:keySpec="&#x0020;" />

            <!--N ળ GUJARATI LETTER LLA-->
            <Key
                latin:keySpec="&#x0AB3;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--M શ GUJARATI LETTER SHA-->
            <Key
                latin:keySpec="&#x0AB6;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\< ષ GUJARATI LETTER SSA-->
            <Key
                latin:keySpec="&#x0AB7;"
                latin:moreKeys="&lt;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--\> ૤  -->
            <Key
                latin:keySpec="&#x0AE4;"
                latin:moreKeys="&gt;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="\?"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
        </case>

        <default>
            <Key latin:keySpec="&#x0020;" />

            <!--x ં GUJARATI SIGN ANUSVARA-->
            <Key
                latin:keySpec="&#x0A82;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--c મ GUJARATI LETTER MA-->
            <Key
                latin:keySpec="&#x0AAE;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--v ન GUJARATI LETTER NA-->
            <Key
                latin:keySpec="&#x0AA8;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--b વ GUJARATI LETTER VA-->
            <Key
                latin:keySpec="&#x0AB5;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--n લ GUJARATI LETTER LA-->
            <Key
                latin:keySpec="&#x0AB2;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--m સ GUJARATI LETTER SA-->
            <Key
                latin:keySpec="&#x0AB8;"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec=","
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <Key
                latin:keySpec="."
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />

            <!--/ ય GUJARATI LETTER YA-->
            <Key
                latin:keySpec="&#x0AAF;"
                latin:moreKeys="/"
                latin:keyLabelFlags="fontNormal|followKeyLetterRatio" />
         </default>
    </switch>
</merge>
