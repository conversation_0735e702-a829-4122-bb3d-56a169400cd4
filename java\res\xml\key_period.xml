<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case
            latin:languageCode="hi"
            latin:keyboardLayoutSet="hindi_compact"
        >
            <!-- U+0964: "।" DEVANAGARI DANDA -->
            <Key
                latin:keySpec="\u0964"
                latin:keyLabelFlags="hasPopupHint"
                latin:moreKeys="!autoColumnOrder!9,\\,,.,?,!,#,),(,/,;,',@,:,-,&quot;,+,\\%,&amp;"
                latin:backgroundType="functional" />
        </case>
        <case
            latin:languageCode="ne"
            latin:keyboardLayoutSet="nepali_traditional"
        >
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include
                latin:keyboardLayout="@xml/keystyle_devanagari_sign_virama" />
            <Key
                latin:keyStyle="baseKeyDevanagariSignVirama"
                latin:keyLabelFlags="hasPopupHint"
                latin:moreKeys="!text/morekeys_punctuation"
                latin:backgroundType="functional" />
        </case>
        <case
            latin:keyboardLayoutSet="dvorak"
        >
            <Key
                latin:keySpec="z"
                latin:keyLabelFlags="hasPopupHint"
                latin:moreKeys="!text/morekeys_punctuation,!text/morekeys_z" />
        </case>
        <default>
            <Key
                latin:keySpec="!text/keyspec_period"
                latin:keyHintLabel="!text/keyhintlabel_period"
                latin:keyLabelFlags="hasPopupHint|hasShiftedLetterHint"
                latin:moreKeys="!text/morekeys_period"
                latin:backgroundType="functional" />
        </default>
    </switch>
</merge>
