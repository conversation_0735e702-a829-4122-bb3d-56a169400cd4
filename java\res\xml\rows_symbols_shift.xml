<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2008, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <include
        latin:keyboardLayout="@xml/key_styles_common" />
    <include
        latin:keyboardLayout="@xml/key_styles_currency" />
    <Row
        latin:keyWidth="10%p"
    >
        <include
            latin:keyboardLayout="@xml/rowkeys_symbols_shift1" />
    </Row>
    <Row
        latin:keyWidth="10%p"
    >
        <include
            latin:keyXPos="5%p"
            latin:keyboardLayout="@xml/rowkeys_symbols_shift2" />
    </Row>
    <Row
        latin:keyWidth="10%p"
    >
        <Key
            latin:keyStyle="backFromMoreSymbolKeyStyle"
            latin:keyWidth="15%p"
            latin:visualInsetsRight="1%p" />
        <include
            latin:keyboardLayout="@xml/rowkeys_symbols_shift3" />
        <Key
            latin:keyStyle="deleteKeyStyle"
            latin:keyWidth="fillRight"
            latin:visualInsetsLeft="1%p" />
    </Row>
    <Row
        latin:keyWidth="10%p"
        latin:backgroundType="functional"
    >
        <Key
            latin:keyStyle="toAlphaKeyStyle"
            latin:keyWidth="15%p" />
        <include
            latin:keyboardLayout="@xml/row_symbols_shift4" />
        <Key
            latin:keyStyle="enterKeyStyle"
            latin:keyWidth="fillRight" />
    </Row>
</merge>
