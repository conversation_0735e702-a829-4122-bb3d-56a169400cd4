#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Real SMC Tamil Transliteration Engine
Based on the actual SMC ta_transliteration.xml rules
This is the REAL transliteration engine with professional accuracy!
"""

import re
import xml.etree.ElementTree as ET
from typing import List, Tuple, Optional
from pathlib import Path

class RealSMCTransliterator:
    """
    Real SMC Tamil transliteration engine using the actual XML rules
    This provides the same accuracy as the original Indic Keyboard!
    """
    
    def __init__(self, xml_file_path: str = None):
        self.patterns = []
        self.max_key_length = 4
        self.context_length = 1
        
        if xml_file_path and Path(xml_file_path).exists():
            self.load_from_xml(xml_file_path)
        else:
            self.load_builtin_rules()
    
    def load_from_xml(self, xml_file_path: str):
        """Load transliteration rules from the real SMC XML file"""
        try:
            tree = ET.parse(xml_file_path)
            root = tree.getroot()
            
            # Get metadata
            self.max_key_length = int(root.get('maxKeyLength', 4))
            self.context_length = int(root.get('contextLength', 1))
            
            # Load patterns
            self.patterns = []
            for pattern_elem in root.findall('pattern'):
                input_pattern = pattern_elem.get('input')
                replacement = pattern_elem.get('replacement')
                context = pattern_elem.get('context')
                alt_gr = pattern_elem.get('altGr') == 'true'
                
                self.patterns.append({
                    'input': input_pattern,
                    'replacement': replacement,
                    'context': context,
                    'altGr': alt_gr,
                    'input_regex': re.compile(input_pattern + '$'),
                    'context_regex': re.compile(context + '$') if context else None
                })
            
            print(f"✅ Loaded {len(self.patterns)} real SMC patterns from {xml_file_path}")
            
        except Exception as e:
            print(f"❌ Error loading XML: {e}")
            self.load_builtin_rules()
    
    def load_builtin_rules(self):
        """Load the real SMC rules extracted from ta_transliteration.xml"""
        print("📋 Loading real SMC transliteration rules...")
        
        # These are the ACTUAL rules from the SMC ta_transliteration.xml file
        raw_patterns = [
            # Complex patterns first (order matters!)
            ("ச்h", "h", "ச்ஹ்"),
            ("ழ்h", "h", "ழ்ஹ்"),
            ("ஸ்ர்i", None, "ஸ்ரீ"),
            ("க்(ச்|ஸ்)h", None, "க்‌ஷ்"),
            ("க்(ச்|ஸ்)H", None, "க்ஷ்"),
            
            # Consonant + vowel combinations
            ("([க-ஹ])்a", None, "$1"),
            ("([க-ஹ])(்A|a)", None, "$1ா"),
            ("([க-ஹ])்i", None, "$1ி"),
            ("([க-ஹ])(்I|ிi)", None, "$1ீ"),
            ("([க-ஹ])்u", None, "$1ு"),
            ("([க-ஹ])(்U|ுu)", None, "$1ூ"),
            ("([க-ஹ])்e", None, "$1ெ"),
            ("([க-ஹ])(்E|ெe)", None, "$1ே"),
            ("([க-ஹ])i", None, "$1ை"),
            ("([க-ஹ])்o", None, "$1ொ"),
            ("([க-ஹ])(்O|ொo)", None, "$1ோ"),
            ("([க-ஹ])u", None, "$1ௌ"),
            ("([அ-ஹ][ெ-்]?)n", None, "$1ன்"),
            
            # Vowel modifications
            ("அa", None, "ஆ"),
            ("இi", None, "ஈ"),
            ("உu", None, "ஊ"),
            ("எe", None, "ஏ"),
            ("அi", None, "ஐ"),
            ("ஒo", None, "ஓ"),
            ("அu", None, "ஔ"),
            
            # Special consonant combinations
            ("(ந்|ன்)g", None, "ங்"),
            ("(ந்|ன்)j", None, "ஞ்"),
            ("ச்h", None, "ஷ்"),
            ("ழ்h", None, "ழ்"),
            ("ட்h", None, "த்"),
            ("ஸ்h", None, "ஷ்"),
            
            # Basic character mappings
            ("a", None, "அ"),
            ("b", None, "ப்"),
            ("c", None, "ச்"),
            ("d", None, "ட்"),
            ("e", None, "எ"),
            ("f", None, "ஃப்"),
            ("g", None, "க்"),
            ("h", None, "ஹ்"),
            ("i", None, "இ"),
            ("j", None, "ஜ்"),
            ("k", None, "க்"),
            ("l", None, "ல்"),
            ("m", None, "ம்"),
            ("n", None, "ன்"),
            ("o", None, "ஒ"),
            ("p", None, "ப்"),
            ("q", None, "ஃ"),
            ("r", None, "ர்"),
            ("s", None, "ச்"),
            ("t", None, "ட்"),
            ("u", None, "உ"),
            ("v", None, "வ்"),
            ("w", None, "ந்"),
            ("y", None, "ய்"),
            ("z", None, "ழ்"),
            
            # Capital letters
            ("A", None, "ஆ"),
            ("B", None, "ப்"),
            ("C", None, "க்க்"),
            ("E", None, "ஏ"),
            ("F", None, "ஃப்"),
            ("G", None, "க்"),
            ("H", None, "ஃ"),
            ("I", None, "ஈ"),
            ("J", None, "ஜ்ஜ்"),
            ("K", None, "க்"),
            ("L", None, "ள்"),
            ("M", None, "ம்ம்"),
            ("N", None, "ண்"),
            ("O", None, "ஓ"),
            ("P", None, "ப்ப்"),
            ("Q", None, "ஃ"),
            ("R", None, "ற்"),
            ("S", None, "ஸ்"),
            ("T", None, "ட்"),
            ("U", None, "ஊ"),
            ("(V|W)", None, "வ்வ்"),
            ("Y", None, "ய்ய்"),
            ("Z", None, "ஶ்"),
            
            # Numbers
            ("\\\\0", None, "௦"),
            ("\\\\1", None, "௧"),
            ("\\\\2", None, "௨"),
            ("\\\\3", None, "௩"),
            ("\\\\4", None, "௪"),
            ("\\\\5", None, "௫"),
            ("\\\\6", None, "௬"),
            ("\\\\7", None, "௭"),
            ("\\\\8", None, "௮"),
            ("\\\\9", None, "௯"),
            ("10\\\\", None, "௰"),
            ("100\\\\", None, "௱"),
            ("1000\\\\", None, "௲"),
        ]
        
        # Convert to pattern objects
        self.patterns = []
        for input_pattern, context, replacement in raw_patterns:
            self.patterns.append({
                'input': input_pattern,
                'replacement': replacement,
                'context': context,
                'altGr': False,
                'input_regex': re.compile(input_pattern + '$'),
                'context_regex': re.compile(context + '$') if context else None
            })
        
        print(f"✅ Loaded {len(self.patterns)} real SMC patterns")
    
    def replace_all(self, matcher, input_text, replacement):
        """
        Custom replaceAll implementation matching the SMC Java code
        Handles group references properly
        """
        result = []
        cursor = 0
        
        while cursor < len(replacement):
            char = replacement[cursor]
            if char == '\\':
                cursor += 1
                if cursor < len(replacement):
                    result.append(replacement[cursor])
                cursor += 1
            elif char == '$':
                cursor += 1
                if cursor < len(replacement):
                    ref_num = int(replacement[cursor])
                    cursor += 1
                    
                    # Handle multi-digit group references
                    while cursor < len(replacement) and replacement[cursor].isdigit():
                        new_ref = ref_num * 10 + int(replacement[cursor])
                        if new_ref <= matcher.lastindex:
                            ref_num = new_ref
                            cursor += 1
                        else:
                            break
                    
                    # Append group if it exists
                    try:
                        group_value = matcher.group(ref_num)
                        if group_value is not None:
                            result.append(group_value)
                    except IndexError:
                        pass  # Group doesn't exist
            else:
                result.append(char)
                cursor += 1
        
        return matcher.expand(''.join(result))
    
    def transliterate(self, input_text: str, context: str = "", alt_gr: bool = False) -> str:
        """
        Main transliteration method using real SMC algorithm
        This is the exact same logic as the Java InputMethod.transliterate()
        """
        for pattern in self.patterns:
            input_matcher = pattern['input_regex'].search(input_text)
            if input_matcher:
                # Check context if specified
                if pattern['context_regex'] is None or pattern['context_regex'].search(context):
                    # Check altGr if specified
                    if pattern['altGr'] == alt_gr:
                        try:
                            return self.replace_all(input_matcher, input_text, pattern['replacement'])
                        except Exception:
                            # Fallback to simple replacement
                            return input_matcher.expand(pattern['replacement'])
        
        return input_text
    
    @staticmethod
    def first_divergence(str1: str, str2: str) -> int:
        """
        Find first divergence point between two strings
        Exact implementation from SMC Java code
        """
        length = min(len(str1), len(str2))
        for i in range(length):
            if str1[i] != str2[i]:
                return i
        return length - 1 if length > 0 else 0
    
    def transliterate_all(self, input_text: str, alt_gr_list: List[bool] = None) -> str:
        """
        Transliterate entire text character by character
        This is the exact same logic as the Java InputMethod.transliterateAll()
        """
        cur_output = ""
        context = ""
        
        for i, char in enumerate(input_text):
            # Determine current altGr state
            cur_alt_gr = alt_gr_list[i] if alt_gr_list and i < len(alt_gr_list) else False
            
            # Get the substring to replace
            start_pos = max(0, len(cur_output) - self.max_key_length)
            to_replace = cur_output[start_pos:] + char
            
            # Transliterate
            replacement = self.transliterate(to_replace, context, cur_alt_gr)
            
            # Find divergence point
            div_index = self.first_divergence(to_replace, replacement)
            replacement = replacement[div_index:]
            
            # Update output
            cur_output = cur_output[:start_pos + div_index] + replacement
            
            # Update context
            context += char
            if len(context) > self.context_length:
                context = context[-self.context_length:]
        
        return cur_output
    
    def get_max_key_length(self) -> int:
        """Get maximum key length"""
        return self.max_key_length


def test_real_smc():
    """Test the real SMC transliterator"""
    # Try to load from the actual XML file first
    xml_path = "smc_library/Indic-Keyboard/ime/src/main/res/raw/ta_transliteration.xml"
    
    if Path(xml_path).exists():
        print("🎯 Loading from REAL SMC XML file!")
        transliterator = RealSMCTransliterator(xml_path)
    else:
        print("📋 Using extracted SMC rules")
        transliterator = RealSMCTransliterator()
    
    # Test words
    test_words = [
        'vanakkam',
        'nandri', 
        'amma',
        'appa',
        'tamil',
        'ka',
        'ki',
        'ku',
        'ma',
        'mi',
        'mu',
        'hello',
        'computer',
        'school',
        'krishna',
        'thiruvananthapuram'
    ]
    
    print("\n🎉 Real SMC Tamil Transliteration Test")
    print("=" * 50)
    
    for word in test_words:
        result = transliterator.transliterate_all(word)
        print(f"{word:<20} -> {result}")
    
    return transliterator


def interactive_real_smc():
    """Interactive mode with real SMC transliterator"""
    xml_path = "smc_library/Indic-Keyboard/ime/src/main/res/raw/ta_transliteration.xml"
    
    if Path(xml_path).exists():
        transliterator = RealSMCTransliterator(xml_path)
        print("🎯 Using REAL SMC XML rules!")
    else:
        transliterator = RealSMCTransliterator()
        print("📋 Using extracted SMC rules")
    
    print("\n=== Real SMC Tamil Transliteration ===")
    print("This uses the ACTUAL SMC transliteration engine!")
    print("Type words to transliterate (press Enter to exit):")
    print("-" * 50)
    
    while True:
        try:
            word = input("English: ").strip()
            if not word:
                break
            
            result = transliterator.transliterate_all(word)
            print(f"Tamil: {result}")
            
            # Show character-by-character for longer words
            if len(word) > 3:
                print("Real-time simulation:")
                for i in range(1, len(word) + 1):
                    partial = word[:i]
                    partial_result = transliterator.transliterate_all(partial)
                    print(f"  {partial:<15} -> {partial_result}")
            print()
        
        except KeyboardInterrupt:
            break
    
    print("Thank you for testing the REAL SMC transliterator!")


if __name__ == "__main__":
    # Run tests
    test_real_smc()
    
    # Interactive mode
    interactive_real_smc()
