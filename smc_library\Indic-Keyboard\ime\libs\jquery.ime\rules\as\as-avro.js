( function ( $ ) {
	'use strict';

	var asAvro = {
		id: 'as-avro',
		name: 'অভ্ৰ',
		description: 'Assamese Avro layout based on Bengali Avro input method',
		date: '2012-10-10',
		URL: 'http://github.com/wikimedia/jquery.ime',
		author: 'Junaid P V',
		license: 'GPLv3',
		version: '1.0',
		contextLength: 0,
		maxKeyLength: 2,
		patterns: [
			['([ক-হড়ঢ়য়ৰ])্?ৰৰi', '[^o`]', '$1ৃ'],
			['ৰৰi', '[^o`]', 'ঋ'],

			['(([ক-হড়ঢ়য়ৰ])|য়)o', '[^o`]', '$1'], // য় cannot be included in the range, why? everywhere else it is OK!
			['([ক-হড়ঢ়য়ৰ])a', '[^o`]', '$1া'],
			['([ক-হড়ঢ়য়ৰ])i', '[^o`]', '$1ি'],
			['([ক-হড়ঢ়য়ৰ])I', '[^o`]', '$1ী'],
			['([ক-হড়ঢ়য়ৰ])u', '[^o`]', '$1ু'],
			['([ক-হড়ঢ়য়ৰ])U', '[^o`]', '$1ূ'],
			['([ক-হড়ঢ়য়ৰ])o', '[o`]', '$1ু'],
			['([ক-হড়ঢ়য়ৰ])e', '[^o`]', '$1ে'],
			['([ক-হড়ঢ়য়ৰ])োI', '[^o`]', '$1ৈ'],
			['([ক-হড়ঢ়য়ৰ])O', '[^o`]', '$1ো'],
			['([ক-হড়ঢ়য়ৰ])োU', '[^o`]', '$1ৌ'],

			//'চচh', '[^o`]', 'চ্চ'],

			['([কঙলষস])(k|K)', '[^o`]', '$1্ক'],
			['([ঙদল])(g|G)', '[^o`]', '$1্গ'],
			['গg', '[^o`]', 'জ্ঞ'],
			['([চশ])c', '[^o`]', '$1্চ'],
			['([জঞব])j', '[^o`]', '$1্জ'],
			['নj', '[^o`]', 'ঞ্জ'],
			['([কটণনপলষস])T', '[^o`]', '$1্ট'],
			['([ডণনল])D', '[^o`]', '$1্ড'],
			['([গষহ])N', '[^o`]', '$1্ণ'],
			['([কতনপশসহ])t', '[^o`]', '$1্ত'],
			['([দনব])d', '[^o`]', '$1্দ'],
			['([গঘণতধনপমশসহ])n', '[^o`]', '$1্ন'],
			['([পমলস])p', '[^o`]', '$1্প'],
			['([বমল])b', '[^o`]', '$1্ব'],
			['([দম])(v|V)', '[^o`]', '$1্ভ'],
			['([কগঙটণতদধনমলশষসহ])m', '[^o`]', '$1্ম'],
			['([ক-ঘচ-ঝট-যলশ-হড়ঢ়য়])r', '[^o`]', '$1্ৰ'],
			['([কগপ-বমলশসহ])l', '[^o`]', '$1্ল'],
			['([কনপ])s', '[^o`]', '$1্স'],
			['([ক-হড়ঢ়য়ৰ])w', '[^o`]', '$1্ব'],
			['([ক-হড়ঢ়য়ৰ])y', '[^o`]', '$1্য'],
			['নc', '[^o`]', 'ঞ্চ'],

			['ত`', '`' , 'ৎ'],

			['ক্ক(h|H)', '[^o`]', 'ক্ষ'],
			['কশ(h|H)', '[^o`]', 'ক্ষ'],
			//'([অ-ঔা-ৌ])ৰৰk', '[^o]', '$1র্ক'],

			['ৰৰk', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্ক'],
			['ৰৰg', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্গ'],
			['ৰৰc', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্চ'],
			['ৰৰj', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্জ'],
			['ৰৰT', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্ট'],
			['ৰৰD', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্ড'],
			['ৰৰN', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্ণ'],
			['ৰৰt', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্ত'],
			['ৰৰd', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্দ'],
			['ৰৰn', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্ন'],
			['ৰৰp', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্প'],
			['ৰৰf', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্ফ'],
			['ৰৰb', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্ব'],
			['ৰৰv', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্ভ'],
			['ৰৰm', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্ম'],
			['ৰৰz', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্য'],
			['ৰৰl', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্ল'],
			['ৰৰS', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্শ'],
			['ৰৰs', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্স'],
			['ৰৰh', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্হ'],
			['ৰৰR', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্ড়'],
			['ৰৰ(y|Y)', '(o|a|i|I|u|U|e|O|OI|OU|rri)rr', 'র্য়'],

			['শ(h|H)', 'S', 'ষ'],

			['অo', '[^`]', 'উ'],
			['এe', '[^o`]', 'ঈ'],

			['কh', '[^o`]', 'খ'],
			['গh', '[^o`]', 'ঘ'],
			['ণg', '[^o`]', 'ঙ'],
			['চh', '[^o`]', 'ছ'],
			['জh', '[^o`]', 'ঝ'],
			['ণG', '[^o`]', 'ঞ'],
			['টh', '[^o`]', 'ঠ'],
			['ডh', '[^o`]', 'ঢ'],
			['তh', '[^o`]', 'থ'],
			['দh', '[^o`]', 'ধ'],
			['পh', '[^o`]', 'ফ'],
			['বh', '[^o`]', 'ভ'],
			['(সh)', '[^o`]', 'শ'],
			['ড়h', '[^o`]', 'ঢ়'],
			['ত্`', '[^o`]', 'ৎ'],
			['নg', '[^o`]', 'ং'],
			['ঃ`', '[^o`]', ':'],
			['ৰৰi', '[^o`]', 'ঋ'],
			['ওI', '[^o`]', 'ঐ'],
			['ওU', '[^o`]', 'ঔ'],
			['(k|K)', 'ক'],
			['(g|G)', 'গ'],
			['(c|C)', 'চ'],
			['j', 'জ'],
			['T', 'ট'],
			['D', 'ড'],
			['N', 'ণ'],
			['t', 'ত'],
			['d', 'দ'],
			['n', 'ন'],
			['(p|P)', 'প'],
			['f', 'ফ'],
			['(b|B)', 'ব'],
			['(v|V)', 'ভ'],
			['(m|M)', 'ম'],
			['z', 'য'],
			['r', 'ৰ'],
			['(l|L)', 'ল'],
			['S', 'শ'],
			['s', 'স'],
			['(h|H)', 'হ'],
			['R', 'ড়'],
			['(y|Y)', 'য়'],

			['Z','্য'],

			['o', 'অ'],
			['(a|A)', 'আ'],
			['i', 'ই'],
			['I', 'ঈ'],
			['u', 'উ'],
			['U', 'ঊ'],
			['e', 'এ'],
			['O', 'ও'],

			['0', '০'],
			['1', '১'],
			['2', '২'],
			['3', '৩'],
			['4', '৪'],
			['5', '৫'],
			['6', '৬'],
			['7', '৭'],
			['8', '৮'],
			['9', '৯'],

			['\\\\\\.', '.'],

			[',,','্'],
			['\\:','ঃ'],
			['\\^','ঁ'],
			['\\.', '।'],
			['\\$', '৳'],
			['ঃ`', ':'],
			['`', '']]
	};
	$.ime.register( asAvro );

}( jQuery ) );
