<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright © 2013-2014 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC), International Centre for Free and Open Source Software (ICFOSS)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <include
        latin:keyboardLayout="@xml/key_styles_common" />
    <Row latin:keyWidth="10%p" >
        <include latin:keyboardLayout="@xml/rowkeys_telugu1" />
    </Row>
    <Row latin:keyWidth="10%p" >
        <include latin:keyboardLayout="@xml/rowkeys_telugu2"
            latin:keyXPos="5%p" />
    </Row>
    <Row latin:keyWidth="10%p" >
        <Key
            latin:keyStyle="shiftKeyStyle"
            latin:keyWidth="15%p"
            latin:visualInsetsRight="1%p" />
        <include
            latin:keyboardLayout="@xml/rowkeys_telugu3" />
        <Key
            latin:keyStyle="deleteKeyStyle"
            latin:keyWidth="fillRight"
            latin:visualInsetsLeft="1%p" />
    </Row>
    <!--<Row latin:keyWidth="9.8%p" >-->
        <!--<Key-->
            <!--latin:keyStyle="shiftKeyStyle"-->
            <!--latin:keyWidth="10.8%p" />-->
        <!--<include-->
            <!--latin:keyboardLayout="@xml/rowkeys_telugu3" />-->
        <!--<Key-->
            <!--latin:keyStyle="deleteKeyStyle"-->
            <!--latin:keyWidth="fillRight" />-->
    <!--</Row>-->
    <include
        latin:keyboardLayout="@xml/row_qwerty4" />
</merge>
