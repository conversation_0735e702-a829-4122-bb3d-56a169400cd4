<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <!-- U+0C46: "ె" TELUGU VOWEL SIGN E
         U+0C12: "ఒ" TELUGU LETTER O -->
    <Key
        latin:keySpec="&#x0C46;"
        latin:moreKeys="&#x0C12;" />
    <!-- U+0C02: "ం" TELUGU SIGN ANUSVARA
         U+0C0E: "ఎ" TELUGU LETTER E -->
    <Key
        latin:keySpec="&#x0C02;"
        latin:moreKeys="&#x0C0E;" />
    <!-- U+0C2E: "మ" TELUGU LETTER MA -->
    <Key latin:keySpec="&#x0C2E;" />
    <!-- U+0C28: "న" TELUGU LETTER NA
         U+0C23: "ణ" TELUGU LETTER NNA
         U+0C19: "ఙ" TELUGU LETTER NGA -->
    <Key
        latin:keySpec="&#x0C28;"
        latin:moreKeys="&#x0C23;,&#x0C19;" />
    <!-- U+0C35: "వ" TELUGU LETTER VA -->
    <Key latin:keySpec="&#x0C35;" />
    <!-- U+0C32: "ల" TELUGU LETTER LA
         U+0C33: "ళ" TELUGU LETTER LLA -->
    <Key
        latin:keySpec="&#x0C32;"
        latin:moreKeys="&#x0C33;" />
    <!-- U+0C38: "స" TELUGU LETTER SA
         U+0C36: "శ" TELUGU LETTER SHA -->
    <Key
        latin:keySpec="&#x0C38;"
        latin:moreKeys="&#x0C36;" />
    <!-- U+0C0B: "ఋ" TELUGU LETTER VOCALIC R
         U+0C4D/U+0C30: "్ర" TELUGU SIGN VIRAMA/TELUGU LETTER RA -->
    <Key
        latin:keySpec="&#x0C0B;"
        latin:moreKeys="&#x0C4D;&#x0C30;" />
    <!-- U+0C37: "ష" TELUGU LETTER SSA
         U+0C15/U+0C4D/U+0C37: "క్ష" TELUGU LETTER KA/TELUGU SIGN VIRAMA/TELUGU LETTER SSA -->
    <Key
        latin:keySpec="&#x0C37;"
        latin:moreKeys="&#x0C15;&#x0C4D;&#x0C37;" />
    <!-- U+0C2F: "య" TELUGU LETTER YA
         U+0C1C/U+0C4D/U+0C1E: "జ్ఞ" TELUGU LETTER JA/TELUGU SIGN VIRAMA/TELUGU LETTER NYA -->
    <Key
        latin:keySpec="&#x0C2F;"
        latin:moreKeys="&#x0C1C;&#x0C4D;&#x0C1E;" />
</merge>
