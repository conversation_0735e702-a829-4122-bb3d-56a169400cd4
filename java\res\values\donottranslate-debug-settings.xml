<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2014, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Title for Android keyboard debug settings activity / dialog -->
    <string name="english_ime_debug_settings">Android Keyboard Debug settings</string>
    <string name="prefs_debug_mode">Debug Mode</string>
    <string name="prefs_force_non_distinct_multitouch">Force non-distinct multitouch</string>
    <string name="prefs_force_physical_keyboard_special_key">Force physical keyboard special key</string>
    <string name="prefs_show_ui_to_accept_typed_word">Show UI to accept typed word</string>
    <!-- Option to enable sliding key input indicator. The user can see a rubber band-like effect during sliding key input. [CHAR LIMIT=30]-->
    <string name="sliding_key_input_preview">Show slide indicator</string>
    <!-- Option summary to enable sliding key input indicator. The user can see a rubber band-like effect during sliding key input. [CHAR LIMIT=66]-->
    <string name="sliding_key_input_preview_summary">Display visual cue while sliding from Shift or Symbol keys</string>
    <!-- Title of the settings for key long press delay [CHAR LIMIT=35] -->
    <string name="prefs_key_longpress_timeout_settings">Key long press delay</string>
    <!-- Title of the settings for customize key popup animation parameters [CHAR LIMIT=35] -->
    <string name="prefs_customize_key_preview_animation">Customize key preview animation</string>
    <!-- Title of the settings for key popup show up animation duration (in milliseconds) [CHAR LIMIT=35] -->
    <string name="prefs_key_popup_show_up_duration_settings">Key popup show up duration</string>
    <!-- Title of the settings for key popup dismiss animation duration (in milliseconds) [CHAR LIMIT=35] -->
    <string name="prefs_key_popup_dismiss_duration_settings">Key popup dismiss duration</string>
    <!-- Title of the settings for key popup show up animation start X-scale (in percentile) [CHAR LIMIT=35] -->
    <string name="prefs_key_popup_show_up_start_x_scale_settings">Key popup show up start X scale</string>
    <!-- Title of the settings for key popup show up animation start Y-scale (in percentile) [CHAR LIMIT=35] -->
    <string name="prefs_key_popup_show_up_start_y_scale_settings">Key popup show up start Y scale</string>
    <!-- Title of the settings for key popup dismiss animation end X-scale (in percentile) [CHAR LIMIT=35] -->
    <string name="prefs_key_popup_dismiss_end_x_scale_settings">Key popup dismiss end X scale</string>
    <!-- Title of the settings for key popup dismiss animation end Y-scale (in percentile) [CHAR LIMIT=35] -->
    <string name="prefs_key_popup_dismiss_end_y_scale_settings">Key popup dismiss end Y scale</string>
    <!-- Title of the settings for reading an external dictionary file -->
    <string name="prefs_read_external_dictionary">Read external dictionary file</string>
    <!-- Message to show when there are no files to install as an external dictionary [CHAR LIMIT=100] -->
    <string name="read_external_dictionary_no_files_message">No dictionary files in the Downloads folder</string>
    <!-- Title of the dialog that selects a file to install as an external dictionary [CHAR LIMIT=50] -->
    <string name="read_external_dictionary_multiple_files_title">Select a dictionary file to install</string>
    <!-- Title of the confirmation dialog to install a file as an external dictionary [CHAR LIMIT=50] -->
    <string name="read_external_dictionary_confirm_install_message">Really install this file for <xliff:g id="LANGUAGE_NAME" example="English">%s</xliff:g>?</string>
    <!-- Title for an error dialog that contains the details of the error in the body [CHAR LIMIT=80] -->
    <string name="read_external_dictionary_error">There was an error</string>
    <!-- Title of the settings group for dumpping dictionary files that have been created on the device [CHAR LIMIT=35] -->
    <string name="prefs_dump_dynamic_dicts">Dump dictionary</string>
</resources>
