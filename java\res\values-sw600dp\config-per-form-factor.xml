<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2013, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<!-- Configuration values for Small Tablet. -->
<resources>
    <bool name="config_enable_show_key_preview_popup_option">false</bool>
    <!-- Whether or not Popup on key press is enabled by default -->
    <bool name="config_default_key_preview_popup">false</bool>
    <bool name="config_default_sound_enabled">true</bool>
    <bool name="config_enable_show_voice_key_option">false</bool>
    <bool name="config_key_selection_by_dragging_finger">false</bool>
    <!-- Showing more keys keyboard, just above the touched point if true, aligned to the key if
         false -->
    <bool name="config_show_more_keys_keyboard_at_touched_point">false</bool>
    <bool name="config_use_fullscreen_mode">false</bool>
</resources>
