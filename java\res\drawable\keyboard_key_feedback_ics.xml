<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2013 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<selector
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <!-- Left edge -->
    <item latin:state_left_edge="true" latin:state_has_morekeys="true"
          android:drawable="@drawable/keyboard_key_feedback_left_more_background_ics" />
    <item latin:state_left_edge="true"
          android:drawable="@drawable/keyboard_key_feedback_left_background_ics" />

    <!-- Right edge -->
    <item latin:state_right_edge="true" latin:state_has_morekeys="true"
          android:drawable="@drawable/keyboard_key_feedback_right_more_background_ics" />
    <item latin:state_right_edge="true"
          android:drawable="@drawable/keyboard_key_feedback_right_background_ics" />

    <item latin:state_has_morekeys="true"
          android:drawable="@drawable/keyboard_key_feedback_more_background_ics" />
    <item android:drawable="@drawable/keyboard_key_feedback_background_ics" />
</selector>
