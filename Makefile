# Complete SMC Tamil Transliteration Engine - Makefile
# Professional-grade C++ implementation with full integration

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g
INCLUDES = -I.
LIBS = 

# Target executable
TARGET = smc_transliterator
TEST_TARGET = smc_test

# Source files
SOURCES = main.cpp smc_transliterator.cpp
HEADERS = smc_transliterator.h
OBJECTS = $(SOURCES:.cpp=.o)

# Default paths for SMC files
SMC_XML_PATH = smc_library/Indic-Keyboard/ime/src/main/res/raw/ta_transliteration.xml
SMC_DICT_PATH = smc_library/Indic-Keyboard/dictionaries-indic/ta_wordlist.combined

# Default target
all: $(TARGET)

# Build the main executable
$(TARGET): $(OBJECTS)
	@echo "🔗 Linking $(TARGET)..."
	$(CXX) $(CXXFLAGS) -o $@ $^ $(LIBS)
	@echo "✅ Build complete: $(TARGET)"

# Compile source files
%.o: %.cpp $(HEADERS)
	@echo "🔨 Compiling $<..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Clean build files
clean:
	@echo "🧹 Cleaning build files..."
	rm -f $(OBJECTS) $(TARGET) $(TEST_TARGET)
	@echo "✅ Clean complete"

# Install dependencies (Ubuntu/Debian)
install-deps:
	@echo "📦 Installing dependencies..."
	sudo apt-get update
	sudo apt-get install -y build-essential g++ make
	@echo "✅ Dependencies installed"

# Quick test
test: $(TARGET)
	@echo "🧪 Running quick test..."
	./$(TARGET) --batch

# Full test with files
test-full: $(TARGET)
	@echo "🧪 Running full test with SMC files..."
	@if [ -f "$(SMC_XML_PATH)" ] && [ -f "$(SMC_DICT_PATH)" ]; then \
		./$(TARGET) -x "$(SMC_XML_PATH)" -d "$(SMC_DICT_PATH)" --test; \
	else \
		echo "⚠️ SMC files not found, running basic test..."; \
		./$(TARGET) --test; \
	fi

# Run interactive mode
run: $(TARGET)
	@echo "🚀 Starting SMC Transliterator..."
	@if [ -f "$(SMC_XML_PATH)" ] && [ -f "$(SMC_DICT_PATH)" ]; then \
		./$(TARGET) -x "$(SMC_XML_PATH)" -d "$(SMC_DICT_PATH)"; \
	else \
		echo "⚠️ SMC files not found, running in basic mode..."; \
		./$(TARGET); \
	fi

# Run with custom files
run-custom: $(TARGET)
	@echo "🚀 Starting SMC Transliterator with custom files..."
	@echo "Usage: make run-custom XML=path/to/file.xml DICT=path/to/dict.txt"
	@if [ -n "$(XML)" ] && [ -n "$(DICT)" ]; then \
		./$(TARGET) -x "$(XML)" -d "$(DICT)"; \
	else \
		echo "❌ Please specify XML and DICT variables"; \
		echo "Example: make run-custom XML=ta_transliteration.xml DICT=ta_wordlist.combined"; \
	fi

# Debug build
debug: CXXFLAGS += -DDEBUG -g3 -O0
debug: $(TARGET)
	@echo "🐛 Debug build complete"

# Release build
release: CXXFLAGS += -DNDEBUG -O3 -march=native
release: clean $(TARGET)
	@echo "🚀 Release build complete"

# Profile build
profile: CXXFLAGS += -pg -O2
profile: $(TARGET)
	@echo "📊 Profile build complete"

# Memory check (requires valgrind)
memcheck: $(TARGET)
	@echo "🔍 Running memory check..."
	valgrind --leak-check=full --show-leak-kinds=all ./$(TARGET) --batch

# Static analysis (requires cppcheck)
analyze:
	@echo "🔍 Running static analysis..."
	cppcheck --enable=all --std=c++17 $(SOURCES) $(HEADERS)

# Format code (requires clang-format)
format:
	@echo "🎨 Formatting code..."
	clang-format -i $(SOURCES) $(HEADERS)

# Create distribution package
dist: clean
	@echo "📦 Creating distribution package..."
	tar -czf smc_transliterator.tar.gz $(SOURCES) $(HEADERS) Makefile README.md
	@echo "✅ Distribution package created: smc_transliterator.tar.gz"

# Show help
help:
	@echo "🎯 SMC Tamil Transliteration Engine - Build System"
	@echo "=================================================="
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Build the main executable (default)"
	@echo "  clean        - Remove build files"
	@echo "  test         - Run quick test"
	@echo "  test-full    - Run full test with SMC files"
	@echo "  run          - Run interactive mode"
	@echo "  run-custom   - Run with custom XML and DICT files"
	@echo "  debug        - Build debug version"
	@echo "  release      - Build optimized release version"
	@echo "  profile      - Build with profiling enabled"
	@echo "  memcheck     - Run memory leak check (requires valgrind)"
	@echo "  analyze      - Run static code analysis (requires cppcheck)"
	@echo "  format       - Format source code (requires clang-format)"
	@echo "  dist         - Create distribution package"
	@echo "  install-deps - Install build dependencies (Ubuntu/Debian)"
	@echo "  help         - Show this help"
	@echo ""
	@echo "Examples:"
	@echo "  make                    # Build the program"
	@echo "  make run                # Build and run with auto-detected files"
	@echo "  make test-full          # Run comprehensive tests"
	@echo "  make run-custom XML=file.xml DICT=dict.txt"
	@echo ""
	@echo "File paths (auto-detected):"
	@echo "  XML:  $(SMC_XML_PATH)"
	@echo "  DICT: $(SMC_DICT_PATH)"

# Check if SMC files exist
check-files:
	@echo "🔍 Checking for SMC files..."
	@if [ -f "$(SMC_XML_PATH)" ]; then \
		echo "✅ XML file found: $(SMC_XML_PATH)"; \
	else \
		echo "❌ XML file not found: $(SMC_XML_PATH)"; \
	fi
	@if [ -f "$(SMC_DICT_PATH)" ]; then \
		echo "✅ Dictionary file found: $(SMC_DICT_PATH)"; \
		echo "   Size: $$(wc -l < "$(SMC_DICT_PATH)") lines"; \
	else \
		echo "❌ Dictionary file not found: $(SMC_DICT_PATH)"; \
	fi

# Benchmark
benchmark: $(TARGET)
	@echo "⏱️ Running benchmark..."
	@echo "Testing transliteration performance..."
	@time ./$(TARGET) --batch
	@echo "Benchmark complete"

# Install (copy to /usr/local/bin)
install: $(TARGET)
	@echo "📥 Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/
	@echo "✅ Installed to /usr/local/bin/$(TARGET)"

# Uninstall
uninstall:
	@echo "🗑️ Uninstalling $(TARGET)..."
	sudo rm -f /usr/local/bin/$(TARGET)
	@echo "✅ Uninstalled"

# Show system info
info:
	@echo "🖥️ System Information:"
	@echo "Compiler: $(CXX) $$($(CXX) --version | head -1)"
	@echo "Flags: $(CXXFLAGS)"
	@echo "OS: $$(uname -s) $$(uname -r)"
	@echo "Architecture: $$(uname -m)"
	@echo "CPU cores: $$(nproc)"

# Phony targets
.PHONY: all clean test test-full run run-custom debug release profile memcheck analyze format dist help check-files benchmark install uninstall install-deps info

# Default goal
.DEFAULT_GOAL := all
