<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2010, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="morekeys_a"></string>
    <string name="morekeys_e"></string>
    <string name="morekeys_i"></string>
    <string name="morekeys_o"></string>
    <string name="morekeys_u"></string>
    <string name="morekeys_s"></string>
    <string name="morekeys_n"></string>
    <string name="morekeys_c"></string>
    <string name="morekeys_y"></string>
    <string name="morekeys_d"></string>
    <string name="morekeys_r"></string>
    <string name="morekeys_t"></string>
    <string name="morekeys_z"></string>
    <string name="morekeys_k"></string>
    <string name="morekeys_l"></string>
    <string name="morekeys_g"></string>
    <string name="morekeys_v"></string>
    <string name="morekeys_h"></string>
    <string name="morekeys_j"></string>
    <string name="morekeys_w"></string>
    <string name="morekeys_q"></string>
    <string name="morekeys_x"></string>
    <string name="keyspec_q">q</string>
    <string name="keyspec_w">w</string>
    <string name="keyspec_y">y</string>
    <string name="keyspec_x">x</string>
    <string name="keyspec_nordic_row1_11"></string>
    <string name="keyspec_nordic_row2_10"></string>
    <string name="keyspec_nordic_row2_11"></string>
    <string name="morekeys_nordic_row2_10"></string>
    <string name="morekeys_nordic_row2_11"></string>
    <string name="keyspec_east_slavic_row1_9"></string>
    <string name="keyspec_east_slavic_row2_2"></string>
    <string name="keyspec_east_slavic_row2_11"></string>
    <string name="keyspec_east_slavic_row3_5"></string>
    <string name="morekeys_east_slavic_row2_2"></string>
    <string name="morekeys_east_slavic_row2_11"></string>
    <string name="morekeys_cyrillic_u"></string>
    <string name="morekeys_cyrillic_ka"></string>
    <string name="morekeys_cyrillic_en"></string>
    <string name="morekeys_cyrillic_ghe"></string>
    <string name="morekeys_cyrillic_a"></string>
    <string name="morekeys_cyrillic_o"></string>
    <string name="morekeys_cyrillic_i"></string>
    <string name="morekeys_cyrillic_ie"></string>
    <string name="morekeys_cyrillic_soft_sign"></string>
    <string name="keyspec_south_slavic_row1_6"></string>
    <string name="keyspec_south_slavic_row2_11"></string>
    <string name="keyspec_south_slavic_row3_1"></string>
    <string name="keyspec_south_slavic_row3_8"></string>
    <string name="keyspec_swiss_row1_11"></string>
    <string name="keyspec_swiss_row2_10"></string>
    <string name="keyspec_swiss_row2_11"></string>
    <string name="morekeys_swiss_row1_11"></string>
    <string name="morekeys_swiss_row2_10"></string>
    <string name="morekeys_swiss_row2_11"></string>
    <!-- Label for "switch to alphabetic" key. -->
    <string name="keylabel_to_alpha">ABC</string>
    <string name="single_quotes">!text/single_lqm_rqm</string>
    <string name="double_quotes">!text/double_lqm_rqm</string>
    <string name="single_angle_quotes">!text/single_laqm_raqm</string>
    <string name="double_angle_quotes">!text/double_laqm_raqm</string>
    <!-- $ - I'm making ₹ as default
         U+00A2: "¢" CENT SIGN
         U+00A3: "£" POUND SIGN
         U+20AC: "€" EURO SIGN
         U+00A5: "¥" YEN SIGN
         U+20B1: "₱" PESO SIGN -->
    <string name="morekeys_currency_dollar">$,&#x00A2;,&#x00A3;,&#x20AC;,&#x00A5;,&#x20B1;</string>
    <string name="keyspec_currency">&#x20b9;</string>
    <string name="morekeys_currency_generic">$,&#x00A2;,&#x20AC;,&#x00A3;,&#x00A5;,&#x20B1;</string>
    <string name="morekeys_punctuation">"!autoColumnOrder!8,\\,,?,!,#,!text/keyspec_right_parenthesis,!text/keyspec_left_parenthesis,/,;,',@,:,-,\",+,\\%,&amp;"</string>
    <string name="morekeys_tablet_punctuation">"!autoColumnOrder!7,\\,,',#,!text/keyspec_right_parenthesis,!text/keyspec_left_parenthesis,/,;,@,:,-,\",+,\\%,&amp;"</string>
    <!-- U+00F1: "ñ" LATIN SMALL LETTER N WITH TILDE -->
    <string name="keyspec_spanish_row2_10">&#x00F1;</string>
    <!-- U+2020: "†" DAGGER
         U+2021: "‡" DOUBLE DAGGER
         U+2605: "★" BLACK STAR -->
    <string name="morekeys_star">&#x2020;,&#x2021;,&#x2605;</string>
    <!-- U+266A: "♪" EIGHTH NOTE
         U+2665: "♥" BLACK HEART SUIT
         U+2660: "♠" BLACK SPADE SUIT
         U+2666: "♦" BLACK DIAMOND SUIT
         U+2663: "♣" BLACK CLUB SUIT -->
    <string name="morekeys_bullet">&#x266A;,&#x2665;,&#x2660;,&#x2666;,&#x2663;</string>
    <!-- U+00B1: "±" PLUS-MINUS SIGN -->
    <string name="morekeys_plus">&#x00B1;</string>
    <string name="morekeys_left_parenthesis">!fixedColumnOrder!3,!text/keyspecs_left_parenthesis_more_keys</string>
    <string name="morekeys_right_parenthesis">!fixedColumnOrder!3,!text/keyspecs_right_parenthesis_more_keys</string>
    <string name="morekeys_less_than">!fixedColumnOrder!3,!text/keyspec_left_single_angle_quote,!text/keyspec_less_than_equal,!text/keyspec_left_double_angle_quote</string>
    <string name="morekeys_greater_than">!fixedColumnOrder!3,!text/keyspec_right_single_angle_quote,!text/keyspec_greater_than_equal,!text/keyspec_right_double_angle_quote</string>
    <string name="morekeys_arabic_diacritics"></string>
    <string name="keyspec_symbols_1">1</string>
    <string name="keyspec_symbols_2">2</string>
    <string name="keyspec_symbols_3">3</string>
    <string name="keyspec_symbols_4">4</string>
    <string name="keyspec_symbols_5">5</string>
    <string name="keyspec_symbols_6">6</string>
    <string name="keyspec_symbols_7">7</string>
    <string name="keyspec_symbols_8">8</string>
    <string name="keyspec_symbols_9">9</string>
    <string name="keyspec_symbols_0">0</string>
    <!-- Label for "switch to symbols" key. -->
    <string name="keylabel_to_symbol">?123</string>
    <string name="additional_morekeys_symbols_1"></string>
    <string name="additional_morekeys_symbols_2"></string>
    <string name="additional_morekeys_symbols_3"></string>
    <string name="additional_morekeys_symbols_4"></string>
    <string name="additional_morekeys_symbols_5"></string>
    <string name="additional_morekeys_symbols_6"></string>
    <string name="additional_morekeys_symbols_7"></string>
    <string name="additional_morekeys_symbols_8"></string>
    <string name="additional_morekeys_symbols_9"></string>
    <string name="additional_morekeys_symbols_0"></string>
    <!-- U+00B9: "¹" SUPERSCRIPT ONE
         U+00BD: "½" VULGAR FRACTION ONE HALF
         U+2153: "⅓" VULGAR FRACTION ONE THIRD
         U+00BC: "¼" VULGAR FRACTION ONE QUARTER
         U+215B: "⅛" VULGAR FRACTION ONE EIGHTH -->
    <string name="morekeys_symbols_1">&#x00B9;,&#x00BD;,&#x2153;,&#x00BC;,&#x215B;</string>
    <!-- U+00B2: "²" SUPERSCRIPT TWO
         U+2154: "⅔" VULGAR FRACTION TWO THIRDS -->
    <string name="morekeys_symbols_2">&#x00B2;,&#x2154;</string>
    <!-- U+00B3: "³" SUPERSCRIPT THREE
         U+00BE: "¾" VULGAR FRACTION THREE QUARTERS
         U+215C: "⅜" VULGAR FRACTION THREE EIGHTHS -->
    <string name="morekeys_symbols_3">&#x00B3;,&#x00BE;,&#x215C;</string>
    <!-- U+2074: "⁴" SUPERSCRIPT FOUR -->
    <string name="morekeys_symbols_4">&#x2074;</string>
    <!-- U+215D: "⅝" VULGAR FRACTION FIVE EIGHTHS -->
    <string name="morekeys_symbols_5">&#x215D;</string>
    <string name="morekeys_symbols_6"></string>
    <!-- U+215E: "⅞" VULGAR FRACTION SEVEN EIGHTHS -->
    <string name="morekeys_symbols_7">&#x215E;</string>
    <string name="morekeys_symbols_8"></string>
    <string name="morekeys_symbols_9"></string>
    <!-- U+207F: "ⁿ" SUPERSCRIPT LATIN SMALL LETTER N
         U+2205: "∅" EMPTY SET -->
    <string name="morekeys_symbols_0">&#x207F;,&#x2205;</string>
    <!-- The all letters need to be mirrored are found at
         http://www.unicode.org/Public/6.1.0/ucd/BidiMirroring.txt
         U+2039: "‹" SINGLE LEFT-POINTING ANGLE QUOTATION MARK
         U+203A: "›" SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
         U+2264: "≤" LESS-THAN OR EQUAL TO
         U+2265: "≥" GREATER-THAN EQUAL TO
         U+00AB: "«" LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
         U+00BB: "»" RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK -->
    <string name="keyspec_left_parenthesis">(</string>
    <string name="keyspec_right_parenthesis">)</string>
    <string name="keyspec_left_square_bracket">[</string>
    <string name="keyspec_right_square_bracket">]</string>
    <string name="keyspec_left_curly_bracket">{</string>
    <string name="keyspec_right_curly_bracket">}</string>
    <string name="keyspec_less_than">&lt;</string>
    <string name="keyspec_greater_than">&gt;</string>
    <string name="keyspec_less_than_equal">&#x2264;</string>
    <string name="keyspec_greater_than_equal">&#x2265;</string>
    <string name="keyspec_left_double_angle_quote">&#x00AB;</string>
    <string name="keyspec_right_double_angle_quote">&#x00BB;</string>
    <string name="keyspec_left_single_angle_quote">&#x2039;</string>
    <string name="keyspec_right_single_angle_quote">&#x203A;</string>
    <!-- Comma key -->
    <string name="keyspec_comma">,</string>
    <string name="keyspec_tablet_comma">,</string>
    <string name="keyhintlabel_tablet_comma"></string>
    <string name="morekeys_tablet_comma"></string>
    <!-- Period key -->
    <string name="keyspec_period">.</string>
    <string name="keyhintlabel_period"></string>
    <string name="morekeys_period">!text/morekeys_punctuation</string>
    <string name="keyspec_tablet_period">.</string>
    <string name="keyhintlabel_tablet_period"></string>
    <string name="morekeys_tablet_period">!text/morekeys_tablet_punctuation</string>
    <string name="keyspec_symbols_question">?</string>
    <string name="keyspec_symbols_semicolon">;</string>
    <string name="keyspec_symbols_percent">%</string>
    <!-- U+00A1: "¡" INVERTED EXCLAMATION MARK -->
    <string name="morekeys_exclamation">&#x00A1;</string>
    <!-- U+00BF: "¿" INVERTED QUESTION MARK -->
    <string name="morekeys_question">&#x00BF;</string>
    <string name="morekeys_symbols_semicolon"></string>
    <!-- U+2030: "‰" PER MILLE SIGN -->
    <string name="morekeys_symbols_percent">&#x2030;</string>
    <string name="morekeys_am_pm">!fixedColumnOrder!2,!hasLabels!,!text/keylabel_time_am,!text/keylabel_time_pm</string>
    <string name="keyspec_settings">!icon/settings_key|!code/key_settings</string>
    <string name="keyspec_shortcut">!icon/shortcut_key|!code/key_shortcut</string>
    <string name="keyspec_action_next">!hasLabels!,!text/label_next_key|!code/key_action_next</string>
    <string name="keyspec_action_previous">!hasLabels!,!text/label_previous_key|!code/key_action_previous</string>
    <!-- Label for "switch to more symbol" modifier key ("= \ <"). Must be short to fit on key! -->
    <string name="keylabel_to_more_symbol">= \\\\ &lt;</string>
    <!-- Label for "switch to more symbol" modifier key on tablets.  Must be short to fit on key! -->
    <string name="keylabel_tablet_to_more_symbol">~ [ &lt;</string>
    <!-- Label for "switch to phone numeric" key.  Must be short to fit on key! -->
    <string name="keylabel_to_phone_numeric">123</string>
    <!-- Label for "switch to phone symbols" key.  Must be short to fit on key! -->
    <!-- U+FF0A: "＊" FULLWIDTH ASTERISK
         U+FF03: "＃" FULLWIDTH NUMBER SIGN -->
    <string name="keylabel_to_phone_symbols">&#xFF0A;&#xFF03;</string>
    <!-- Key label for "ante meridiem" -->
    <string name="keylabel_time_am">"AM"</string>
    <!-- Key label for "post meridiem" -->
    <string name="keylabel_time_pm">"PM"</string>
    <string name="keyspec_popular_domain">".com"</string>
    <!-- popular web domains for the locale - most popular, displayed on the keyboard -->
    <string name="morekeys_popular_domain">"!hasLabels!,.net,.org,.gov,.edu"</string>
    <string name="keyspecs_left_parenthesis_more_keys">!text/keyspec_less_than,!text/keyspec_left_curly_bracket,!text/keyspec_left_square_bracket</string>
    <string name="keyspecs_right_parenthesis_more_keys">!text/keyspec_greater_than,!text/keyspec_right_curly_bracket,!text/keyspec_right_square_bracket</string>
    <!-- The following characters don't need BIDI mirroring.
         U+2018: "‘" LEFT SINGLE QUOTATION MARK
         U+2019: "’" RIGHT SINGLE QUOTATION MARK
         U+201A: "‚" SINGLE LOW-9 QUOTATION MARK
         U+201C: "“" LEFT DOUBLE QUOTATION MARK
         U+201D: "”" RIGHT DOUBLE QUOTATION MARK
         U+201E: "„" DOUBLE LOW-9 QUOTATION MARK -->
    <!-- Abbreviations are:
         laqm: LEFT-POINTING ANGLE QUOTATION MARK
         raqm: RIGHT-POINTING ANGLE QUOTATION MARK
         lqm: LEFT QUOTATION MARK
         rqm: RIGHT QUOTATION MARK
         9qm: LOW-9 QUOTATION MARK -->
    <!--  The following each quotation mark pair consist of
            <opening quotation mark>, <closing quotation mark>
          and is named after (single|double)_<opening quotation mark>_<closing quotation mark>. -->
    <string name="single_laqm_raqm">!text/keyspec_left_single_angle_quote,!text/keyspec_right_single_angle_quote</string>
    <string name="single_raqm_laqm">!text/keyspec_right_single_angle_quote,!text/keyspec_left_single_angle_quote</string>
    <string name="double_laqm_raqm">!text/keyspec_left_double_angle_quote,!text/keyspec_right_double_angle_quote</string>
    <string name="double_raqm_laqm">!text/keyspec_right_double_angle_quote,!text/keyspec_left_double_angle_quote</string>
    <!-- The following each quotation mark triplet consists of
           <another quotation mark>, <opening quotation mark>, <closing quotation mark>
         and is named after (single|double)_<opening quotation mark>_<closing quotation mark>. -->
    <string name="single_lqm_rqm">&#x201A;,&#x2018;,&#x2019;</string>
    <string name="single_9qm_lqm">&#x2019;,&#x201A;,&#x2018;</string>
    <string name="single_9qm_rqm">&#x2018;,&#x201A;,&#x2019;</string>
    <string name="single_rqm_9qm">&#x2018;,&#x2019;,&#x201A;</string>
    <string name="double_lqm_rqm">&#x201E;,&#x201C;,&#x201D;</string>
    <string name="double_9qm_lqm">&#x201D;,&#x201E;,&#x201C;</string>
    <string name="double_9qm_rqm">&#x201C;,&#x201E;,&#x201D;</string>
    <string name="double_rqm_9qm">&#x201C;,&#x201D;,&#x201E;</string>
    <string name="morekeys_single_quote">!fixedColumnOrder!5,!text/single_quotes,!text/single_angle_quotes</string>
    <string name="morekeys_double_quote">!fixedColumnOrder!5,!text/double_quotes,!text/double_angle_quotes</string>
    <string name="morekeys_tablet_double_quote">!fixedColumnOrder!6,!text/double_quotes,!text/single_quotes,!text/double_angle_quotes,!text/single_angle_quotes</string>
    <string name="keyspec_emoji_action_key">!icon/emoji_action_key|!code/key_emoji</string>
    <string name="label_go_key">!string/label_go_key</string>
    <string name="label_send_key">!string/label_send_key</string>
    <string name="label_next_key">!string/label_next_key</string>
    <string name="label_done_key">!string/label_done_key</string>
    <string name="label_search_key">!string/label_search_key</string>
    <string name="label_previous_key">!string/label_previous_key</string>
    <string name="label_pause_key">!string/label_pause_key</string>
    <string name="label_wait_key">!string/label_wait_key</string>
</resources>
