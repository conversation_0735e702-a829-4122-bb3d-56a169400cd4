<?xml version="1.0" encoding="utf-8"?>
<!--
/*
** Kannada KaGaPa phonetic layout
** Author: <PERSON><PERSON><PERSON> <<EMAIL>>
** Copyright © 2013-2014 <PERSON><PERSON><PERSON>, Swathanthra Malayalam Computing (SMC), International Centre for Free and Open Source Software (ICFOSS)
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
 >
    <switch>
        <case
            latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted"
            >

            <!-- // Z: nya, nga -->
            <Key
                latin:keySpec="&#x0C99;"
                latin:keyLabelFlags="fontNormal" />

            <!-- // X: ssa, nukta below, LLLA -->
            <Key
                latin:keySpec="&#x0CBC;"
                latin:keyLabelFlags="fontNormal" />

            <!-- // C: ca, cha -->
            <Key
                latin:keySpec="&#x0C9B;"
                latin:keyLabelFlags="fontNormal" />

            <!-- V: va, matra au, au -->
            <Key
                latin:keySpec="&#x0CCC;"
                latin:keyLabelFlags="fontNormal" />

            <!-- // B: ba, bha -->
            <Key
                latin:keySpec="&#x0CAD;"
                latin:keyLabelFlags="fontNormal" />

            <!-- N: na, nna -->
            <Key
                latin:keySpec="&#x0CA3;"
                latin:keyLabelFlags="fontNormal" />

            <!-- M: ma, anusvara, avagraha, Devanagari OM -->
            <Key
                latin:keySpec="&#x0C82;"
                latin:moreKeys="&#x0950;"
                latin:keyHintLabel="&#x0950;"
                latin:keyLabelFlags="fontNormal" />
        </case>

        <default>
            <!-- // Z: nya, nga -->
            <Key
                latin:keySpec="&#x0C9E;"
                latin:keyLabelFlags="fontNormal" />

            <!-- // X: ssa, nukta below, LLLA -->
            <Key
                latin:keySpec="&#x0CB7;"
                latin:moreKeys="&#x0CDE;"
                latin:keyHintLabel="&#x0CDE;"
                latin:keyLabelFlags="fontNormal" />

            <!-- // C: ca, cha -->
            <Key
                latin:keySpec="&#x0C9A;"
                latin:keyLabelFlags="fontNormal" />

            <!-- V: va, matra au, au -->
            <Key
                latin:keySpec="&#x0CB5;"
                latin:moreKeys="&#x0C94;"
                latin:keyHintLabel="&#x0C94;"
                latin:keyLabelFlags="fontNormal" />

            <!-- // B: ba, bha -->
            <Key
                latin:keySpec="&#x0CAC;"
                latin:keyLabelFlags="fontNormal" />

            <!-- N: na, nna -->
            <Key
                latin:keySpec="&#x0CA8;"
                latin:keyLabelFlags="fontNormal" />

            <!-- M: ma, anusvara, avagraha, Devanagari OM -->
            <Key
                latin:keySpec="&#x0CAE;"
                latin:moreKeys="&#x0CBD;"
                latin:keyHintLabel="&#x0CBD;"
                latin:keyLabelFlags="fontNormal" />
        </default>
    </switch>
</merge>
