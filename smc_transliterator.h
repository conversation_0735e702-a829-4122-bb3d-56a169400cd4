#ifndef SMC_TRANSLITERATOR_H
#define SMC_TRANSLITERATOR_H

#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <regex>
#include <memory>
#include <fstream>
#include <iostream>

/**
 * Complete SMC Tamil Transliteration Engine in C++
 * Based on the real SMC library with full integration
 * Includes: 49,898 Tamil words + 92 transliteration patterns + ML suggestions
 */

namespace smc {

// Forward declarations
class TransliterationPattern;
class Dictionary;
class SuggestionEngine;

/**
 * Main transliteration pattern class
 */
class TransliterationPattern {
public:
    std::string input;
    std::string replacement;
    std::string context;
    std::regex inputRegex;
    std::regex contextRegex;
    bool hasContext;
    
    TransliterationPattern(const std::string& inp, const std::string& repl, const std::string& ctx = "");
    bool matches(const std::string& text, const std::string& context = "") const;
    std::string apply(const std::string& text) const;
};

/**
 * High-performance dictionary with frequency-based ranking
 */
class Dictionary {
private:
    std::unordered_set<std::string> words;
    std::unordered_map<std::string, int> frequencies;
    std::vector<std::string> wordList; // For fast iteration
    
public:
    bool loadFromFile(const std::string& filename);
    bool contains(const std::string& word) const;
    int getFrequency(const std::string& word) const;
    std::vector<std::pair<std::string, int>> findMatches(const std::string& prefix, int maxResults = 10) const;
    std::vector<std::string> searchContaining(const std::string& substring, int maxResults = 20) const;
    size_t size() const { return words.size(); }
    
    // Statistics
    struct Stats {
        size_t totalWords;
        int maxFrequency;
        int minFrequency;
        double avgFrequency;
    };
    Stats getStats() const;
};

/**
 * ML-based suggestion engine
 */
class SuggestionEngine {
private:
    const Dictionary* dictionary;
    std::unordered_map<std::string, std::vector<std::string>> bigramCache;
    std::unordered_map<std::string, double> characterProximity;
    
    void initializeProximityMap();
    double calculateSimilarity(const std::string& word1, const std::string& word2) const;
    std::vector<std::string> generateTypoVariants(const std::string& input) const;
    
public:
    SuggestionEngine(const Dictionary* dict);
    std::vector<std::pair<std::string, double>> getSuggestions(const std::string& input, int maxSuggestions = 5) const;
    std::vector<std::string> getContextualSuggestions(const std::string& input, const std::string& context) const;
};

/**
 * Main SMC Transliterator class
 */
class SMCTransliterator {
private:
    std::vector<std::unique_ptr<TransliterationPattern>> patterns;
    std::unique_ptr<Dictionary> dictionary;
    std::unique_ptr<SuggestionEngine> suggestionEngine;
    
    int maxKeyLength;
    int contextLength;
    
    // Performance optimization
    mutable std::unordered_map<std::string, std::string> transliterationCache;
    
    bool loadPatternsFromXML(const std::string& xmlFile);
    void loadBuiltinPatterns();
    std::string applyPatterns(const std::string& input, const std::string& context = "") const;
    
public:
    SMCTransliterator();
    ~SMCTransliterator() = default;
    
    // Initialization
    bool initialize(const std::string& xmlFile = "", const std::string& dictFile = "");
    
    // Core transliteration methods
    std::string transliterate(const std::string& input, const std::string& context = "") const;
    std::string transliterateAll(const std::string& input) const;
    std::string transliterateSmart(const std::string& input) const;
    
    // Advanced features
    std::vector<std::string> getSuggestions(const std::string& input, int maxSuggestions = 5) const;
    std::vector<std::string> getContextualSuggestions(const std::string& input, const std::string& context) const;
    std::vector<std::string> searchDictionary(const std::string& query, int maxResults = 20) const;
    
    // Real-time processing (like the original SMC)
    std::string processCharacterByCharacter(const std::string& input) const;
    std::string simulateRealTimeTyping(const std::string& input) const;
    
    // Utility methods
    int getMaxKeyLength() const { return maxKeyLength; }
    size_t getDictionarySize() const { return dictionary ? dictionary->size() : 0; }
    Dictionary::Stats getDictionaryStats() const;
    void clearCache() const { transliterationCache.clear(); }
    
    // Performance monitoring
    struct PerformanceStats {
        size_t cacheHits;
        size_t cacheMisses;
        size_t totalTransliterations;
        double avgProcessingTime;
    };
    mutable PerformanceStats perfStats = {0, 0, 0, 0.0};
    PerformanceStats getPerformanceStats() const { return perfStats; }
};

/**
 * Interactive CLI interface
 */
class SMCInterface {
private:
    std::unique_ptr<SMCTransliterator> transliterator;
    bool running;
    
    void showWelcome() const;
    void showHelp() const;
    void showStats() const;
    void processCommand(const std::string& command);
    void runBatchTest() const;
    void simulateRealTime(const std::string& word) const;
    
public:
    SMCInterface();
    ~SMCInterface() = default;
    
    bool initialize(const std::string& xmlFile = "", const std::string& dictFile = "");
    void run();
    void runTests() const;
};

// Utility functions
namespace utils {
    std::string trim(const std::string& str);
    std::vector<std::string> split(const std::string& str, char delimiter);
    std::string toLowerCase(const std::string& str);
    bool fileExists(const std::string& filename);
    std::string getCurrentTimestamp();
    
    // Unicode utilities for Tamil text
    bool isTamilCharacter(const std::string& utf8Char);
    std::vector<std::string> splitUTF8(const std::string& utf8String);
    size_t utf8Length(const std::string& utf8String);
}

} // namespace smc

#endif // SMC_TRANSLITERATOR_H
