( function ( $ ) {
	'use strict';

	var asBornona = {
		id: 'as-born<PERSON>',
		name: 'বৰ্ণনা',
		description: 'Bornona input method for Assamese',
		date: '2012-10-10',
		URL: 'http://github.com/wikimedia/jquery.ime',
		author: 'Junaid P V',
		license: 'GPLv3',
		version: '1.0',
		contextLength: 0,
		maxKeyLength: 0,
		patterns: [
			['q', 'ং'],
			['Q', 'ঙ'],
			['w', 'ঢ'],
			['W', 'ঠ'],
			['e', 'ে'],
			['E', 'ৈ'],
			['r', 'ৰ'],
			['R', 'ৃ'],
			['t', 'ত'],
			['T', 'ট'],
			['y', 'ধ'],
			['Y', 'থ'],
			['u', 'ু'],
			['U', 'ূ'],
			['i', 'ি'],
			['I', 'ী'],
			['o', 'ো'],
			['O', 'ৌ'],
			['p', 'প'],
			['P', '্ৰ'],
			['\\|', 'র'],
			['\\\\', 'ৱ'],
			['a', 'া'],
			['A', 'অ'],
			['s', 'স'],
			['S', 'শ'],
			['d', 'দ'],
			['D', 'ড'],
			['f', 'ফ'],
			['F', 'ৰ্ফ'],
			['g', 'গ'],
			['G', 'ঘ'],
			['h', '্'],
			['H', 'হ'],
			['j', 'জ'],
			['J', 'ঝ'],
			['k', 'ক'],
			['K', 'খ'],
			['l', 'ল'],
			['L', '।'],
			['z', 'য'],
			['Z', 'ড়'],
			['x', 'ষ'],
			['X', 'ঢ়'],
			['c', 'চ'],
			['C', 'ছ'],
			['v', 'ভ'],
			['V', '্য'],
			['b', 'ব'],
			['B', 'য়'],
			['n', 'ন'],
			['N', 'ণ'],
			['m', 'ম'],
			['M', 'ঞ'],
			['0', '০'],
			['1', '১'],
			['2', '২'],
			['3', '৩'],
			['4', '৪'],
			['5', '৫'],
			['6', '৬'],
			['7', '৭'],
			['8', '৮'],
			['9', '৯'],
			['\\`', '\u200C']]
	};
	$.ime.register( asBornona );

}( jQuery ) );
