<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2010, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge
    xmlns:latin="http://schemas.android.com/apk/res-auto"
>
    <switch>
        <case
            latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLockShifted"
        >
            <key-style
                latin:styleName="hasShiftedLetterHintStyle"
                latin:keyLabelFlags="hasShiftedLetterHint|shiftedLetterActivated" />
        </case>
        <default>
            <key-style
                latin:styleName="hasShiftedLetterHintStyle"
                latin:keyLabelFlags="hasShiftedLetterHint" />
        </default>
    </switch>
    <!-- Base key style for the key which may have settings key as more keys. -->
    <key-style
        latin:styleName="baseSettingsMoreKeysStyle" />
    <include
        latin:keyboardLayout="@xml/key_styles_settings" />
    <!-- Functional key styles -->
    <!-- Base style for shift key. A single space is used for dummy label in moreKeys. -->
    <key-style
        latin:styleName="baseForShiftKeyStyle"
        latin:keyActionFlags="noKeyPreview"
        latin:keyLabelFlags="preserveCase"
        latin:moreKeys="!noPanelAutoMoreKey!, |!code/key_capslock" />
    <switch>
        <case
            latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetAutomaticShifted"
        >
            <key-style
                latin:styleName="shiftKeyStyle"
                latin:keySpec="!icon/shift_key_shifted|!code/key_shift"
                latin:backgroundType="stickyOff"
                latin:parentStyle="baseForShiftKeyStyle" />
        </case>
        <case
            latin:keyboardLayoutSetElement="alphabetShiftLocked|alphabetShiftLockShifted"
        >
            <key-style
                latin:styleName="shiftKeyStyle"
                latin:keySpec="!icon/shift_key_shifted|!code/key_shift"
                latin:backgroundType="stickyOn"
                latin:parentStyle="baseForShiftKeyStyle" />
        </case>
        <default>
            <key-style
                latin:styleName="shiftKeyStyle"
                latin:keySpec="!icon/shift_key|!code/key_shift"
                latin:backgroundType="stickyOff"
                latin:parentStyle="baseForShiftKeyStyle" />
        </default>
    </switch>
    <key-style
        latin:styleName="deleteKeyStyle"
        latin:keySpec="!icon/delete_key|!code/key_delete"
        latin:keyActionFlags="isRepeatable|noKeyPreview"
        latin:backgroundType="functional" />
    <!-- emojiKeyStyle must be defined before including @xml/key_syles_enter. -->
    <switch>
        <case latin:keyboardTheme="ICS|KLP">
            <key-style
                latin:styleName="emojiKeyStyle"
                latin:keySpec="!icon/emoji_action_key|!code/key_emoji"
                latin:keyActionFlags="noKeyPreview"
                latin:backgroundType="action" />
        </case>
        <!-- keyboardTheme="LXXLight|LXXDark" -->
        <default>
            <key-style
                latin:styleName="emojiKeyStyle"
                latin:keySpec="!icon/emoji_action_key|!code/key_emoji"
                latin:keyLabelFlags="keepBackgroundAspectRatio"
                latin:keyActionFlags="noKeyPreview"
                latin:backgroundType="action" />
        </default>
    </switch>
    <include
        latin:keyboardLayout="@xml/key_styles_enter" />
    <!-- TODO: Currently there is no way to specify icon alignment per theme. -->
    <key-style
        latin:styleName="spaceKeyStyle"
        latin:keySpec="!icon/space_key|!code/key_space"
        latin:backgroundType="spacebar"
        latin:keyActionFlags="noKeyPreview|enableLongPress" />
    <!-- U+200C: ZERO WIDTH NON-JOINER
         U+200D: ZERO WIDTH JOINER -->
    <key-style
        latin:styleName="zwnjKeyStyle"
        latin:keySpec="!icon/zwnj_key|&#x200C;"
        latin:moreKeys="!icon/zwj_key|&#x200D;"
        latin:keyLabelFlags="hasPopupHint"
        latin:keyActionFlags="noKeyPreview" />
    <key-style
        latin:styleName="shortcutKeyStyle"
        latin:keySpec="!icon/shortcut_key|!code/key_shortcut"
        latin:keyIconDisabled="!icon/shortcut_key_disabled"
        latin:keyActionFlags="noKeyPreview|altCodeWhileTyping"
        latin:altCode="!code/key_space"
        latin:parentStyle="settingsMoreKeysStyle" />
    <key-style
        latin:styleName="settingsKeyStyle"
        latin:keySpec="!icon/settings_key|!code/key_settings"
        latin:keyActionFlags="noKeyPreview|altCodeWhileTyping"
        latin:altCode="!code/key_space"
        latin:backgroundType="functional" />
    <key-style
        latin:styleName="languageSwitchKeyStyle"
        latin:keySpec="!icon/language_switch_key|!code/key_language_switch"
        latin:keyActionFlags="noKeyPreview|altCodeWhileTyping|enableLongPress"
        latin:altCode="!code/key_space" />
    <key-style
        latin:styleName="tabKeyStyle"
        latin:keySpec="!icon/tab_key|!code/key_tab"
        latin:backgroundType="functional" />
    <!-- Note: This key style is not for functional tab key. This is used for the tab key which is
         laid out as normal letter key. -->
    <key-style
        latin:styleName="nonSpecialBackgroundTabKeyStyle"
        latin:keySpec="!icon/tab_key|!code/key_tab" />
    <key-style
        latin:styleName="baseForLayoutSwitchKeyStyle"
        latin:keyLabelFlags="preserveCase|followFunctionalTextColor"
        latin:keyActionFlags="noKeyPreview"
        latin:backgroundType="functional" />
    <key-style
        latin:styleName="toSymbolKeyStyle"
        latin:keySpec="!text/keylabel_to_symbol|!code/key_switch_alpha_symbol"
        latin:parentStyle="baseForLayoutSwitchKeyStyle" />
    <key-style
        latin:styleName="toAlphaKeyStyle"
        latin:keySpec="!text/keylabel_to_alpha|!code/key_switch_alpha_symbol"
        latin:parentStyle="baseForLayoutSwitchKeyStyle" />
    <key-style
        latin:styleName="toMoreSymbolKeyStyle"
        latin:keySpec="!text/keylabel_to_more_symbol|!code/key_shift"
        latin:parentStyle="baseForLayoutSwitchKeyStyle" />
    <key-style
        latin:styleName="backFromMoreSymbolKeyStyle"
        latin:keySpec="!text/keylabel_to_symbol|!code/key_shift"
        latin:parentStyle="baseForLayoutSwitchKeyStyle" />
    <key-style
        latin:styleName="comKeyStyle"
        latin:keySpec="!text/keyspec_popular_domain"
        latin:keyLabelFlags="autoXScale|fontNormal|hasPopupHint|preserveCase"
        latin:moreKeys="!text/morekeys_popular_domain"
        latin:backgroundType="functional" />
</merge>
