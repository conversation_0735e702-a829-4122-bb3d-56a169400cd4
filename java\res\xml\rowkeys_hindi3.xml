<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<merge xmlns:latin="http://schemas.android.com/apk/res-auto">
    <switch>
        <case latin:keyboardLayoutSetElement="alphabetManualShifted|alphabetShiftLocked|alphabetShiftLockShifted">
            <!-- U+0911: "ऑ" DEVANAGARI LETTER CANDRA O -->
            <Key latin:keySpec="&#x0911;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_candrabindu" />
            <Key latin:keyStyle="baseKeyDevanagariSignCandrabindu" />
            <!-- U+0923: "ण" DEVANAGARI LETTER NNA -->
            <Key latin:keySpec="&#x0923;" />
            <!-- U+0929: "ऩ" DEVANAGARI LETTER NNNA -->
            <Key latin:keySpec="&#x0929;" />
            <!-- U+0933: "ळ" DEVANAGARI LETTER LLA
                 U+0934: "ऴ" DEVANAGARI LETTER LLLA -->
            <Key
                latin:keySpec="&#x0933;"
                latin:moreKeys="&#x0934;" />
            <!-- U+0936: "श" DEVANAGARI LETTER SHA -->
            <Key latin:keySpec="&#x0936;" />
            <!-- U+0937: "ष" DEVANAGARI LETTER SSA -->
            <Key latin:keySpec="&#x0937;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_vocalic_r" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignVocalicR" />
            <!-- U+091E: "ञ" DEVANAGARI LETTER NYA -->
            <Key latin:keySpec="&#x091E;" />
        </case>
        <default>
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_vowel_sign_candra_o" />
            <Key latin:keyStyle="baseKeyDevanagariVowelSignCandraO" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_anusvara" />
            <Key latin:keyStyle="baseKeyDevanagariSignAnusvara" />
            <!-- U+092E: "म" DEVANAGARI LETTER MA
                 U+0950: "ॐ" DEVANAGARI OM -->
            <Key
                latin:keySpec="&#x092E;"
                latin:moreKeys="&#x0950;" />
            <!-- U+0928: "न" DEVANAGARI LETTER NA
                 U+091E: "ञ" DEVANAGARI LETTER NYA
                 U+0919: "ङ" DEVANAGARI LETTER NGA
                 U+0928/U+093C: "ऩ" DEVANAGARI LETTER NA/DEVANAGARI SIGN NUKTA -->
            <Key
                latin:keySpec="&#x0928;"
                latin:moreKeys="&#x091E;,&#x0919;,&#x0928;&#x093C;" />
            <!-- U+0935: "व" DEVANAGARI LETTER VA -->
            <Key latin:keySpec="&#x0935;" />
            <!-- U+0932: "ल" DEVANAGARI LETTER LA
                 U+090C: "ऌ" DEVANAGARI LETTER VOCALIC L
                 U+0961: "ॡ" DEVANAGARI LETTER VOCALIC LL -->
            <Key
                latin:keySpec="&#x0932;"
                latin:moreKeys="&#x090C;,&#x0961;" />
            <!-- U+0938: "स" DEVANAGARI LETTER SA -->
            <Key latin:keySpec="&#x0938;" />
            <!-- U+092F: "य" DEVANAGARI LETTER YA
                 U+095F: "य़" DEVANAGARI LETTER YYA -->
            <Key
                latin:keySpec="&#x092F;"
                latin:moreKeys="&#x095F;" />
            <!-- Because the font rendering system prior to API version 16 can't automatically
                 render dotted circle for incomplete combining letter of some scripts, different
                 set of Key definitions are needed based on the API version. -->
            <include latin:keyboardLayout="@xml/keystyle_devanagari_sign_nukta" />
            <Key latin:keyStyle="baseKeyDevanagariSignNukta" />
        </default>
    </switch>
</merge>
